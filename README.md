# Raspberry Pi Network Manager

A cross-platform Flutter application for managing Raspberry Pi devices and other SSH-enabled network devices.

## Features

- **Network Device Discovery**: Scan your local network to discover devices
- **Device Management**: Add, edit, and organize your network devices
- **SSH Terminal**: Connect to devices via SSH with an integrated terminal
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **Secure Credentials**: Encrypted storage of SSH credentials
- **Real-time Status**: Monitor device connectivity status

## Requirements

- Flutter 3.6.0 or higher
- Python 3.7+ (for network scanning)
- SSH access to target devices

## Installation

1. Clone the repository
2. Install Flutter dependencies: `flutter pub get`
3. Install Python dependencies: `pip install paramiko nmap-python`
4. Run the application: `flutter run`

## Usage

1. **Scan Network**: Use the device discovery feature to find devices on your network
2. **Add Devices**: Add discovered devices or manually add device information
3. **Connect via SSH**: Click connect on any device to establish an SSH session
4. **Execute Commands**: Use the integrated terminal to run commands on remote devices

## Supported Platforms

- Linux (Primary)
- Windows
- macOS
