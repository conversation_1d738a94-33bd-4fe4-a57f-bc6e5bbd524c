#!/usr/bin/env python3
"""
Raspberry Pi Diagnostics - SD Card Sector Test Backend
Real implementation using badblocks for sector testing
"""

import sys
import subprocess
import json
import time
import signal
import os
import re
from typing import Dict

class SectorTester:
    def __init__(self, device_path: str, sudo_password: str):
        self.device_path = device_path
        self.sudo_password = sudo_password
        self.process = None
        self.cancelled = False
        self.total_sectors = 0
        self.tested_sectors = 0
        self.bad_sectors = 0
        self.start_time = time.time()
        
    def get_device_info(self) -> Dict:
        """Get basic device information"""
        info = {
            'size': 'Unknown',
            'model': 'Unknown',
            'manufacturer': 'Unknown',
            'serial': 'Unknown',
            'filesystem': 'Unknown'
        }

        try:
            # Get device size - try multiple methods
            size_bytes = None

            # Method 1: blockdev (requires sudo)
            try:
                result = subprocess.run(['sudo', '-S', 'blockdev', '--getsize64', self.device_path],
                                      input=self.sudo_password + '\n',
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    size_bytes = int(result.stdout.strip())
            except:
                pass

            # Method 2: lsblk as fallback
            if size_bytes is None:
                try:
                    result = subprocess.run(['lsblk', '-b', '-d', '-n', '-o', 'SIZE', self.device_path],
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        size_bytes = int(result.stdout.strip())
                except:
                    pass

            if size_bytes:
                size_gb = size_bytes / (1024 * 1024 * 1024)
                info['size'] = f"{size_gb:.1f} GB"

                # Calculate total blocks for badblocks (1024-byte blocks, not 512-byte sectors)
                # badblocks uses 1024-byte blocks by default, so divide by 1024
                self.total_sectors = size_bytes // 1024
            else:
                # Default fallback
                self.total_sectors = 1000
                
            # Get device model and manufacturer - try multiple methods
            # Method 1: udevadm
            try:
                result = subprocess.run(['udevadm', 'info', '--query=all', f'--name={self.device_path}'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'ID_MODEL=' in line and '=' in line:
                            model_val = line.split('=', 1)[1].strip().replace('_', ' ')
                            if model_val and model_val != 'Unknown':
                                info['model'] = model_val
                        elif 'ID_VENDOR=' in line and '=' in line:
                            vendor_val = line.split('=', 1)[1].strip().replace('_', ' ')
                            if vendor_val and vendor_val != 'Unknown':
                                info['manufacturer'] = vendor_val
                        elif 'ID_SERIAL_SHORT=' in line and '=' in line:
                            serial_val = line.split('=', 1)[1].strip()
                            if serial_val and serial_val != 'Unknown':
                                info['serial'] = serial_val
            except Exception as e:
                print(f"udevadm failed: {e}", file=sys.stderr)

            # Method 2: lsblk for model info
            if info['model'] == 'Unknown':
                try:
                    result = subprocess.run(['lsblk', '-d', '-n', '-o', 'MODEL', self.device_path],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        model = result.stdout.strip()
                        if model and model != '' and model != '-':
                            info['model'] = model
                except Exception as e:
                    print(f"lsblk model failed: {e}", file=sys.stderr)

            # Method 3: Try to get vendor from lsblk
            if info['manufacturer'] == 'Unknown':
                try:
                    result = subprocess.run(['lsblk', '-d', '-n', '-o', 'VENDOR', self.device_path],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        vendor = result.stdout.strip()
                        if vendor and vendor != '' and vendor != '-':
                            info['manufacturer'] = vendor
                except Exception as e:
                    print(f"lsblk vendor failed: {e}", file=sys.stderr)

            # Method 4: Try hdparm for additional info
            if info['model'] == 'Unknown' or info['manufacturer'] == 'Unknown':
                try:
                    result = subprocess.run(['sudo', '-S', 'hdparm', '-I', self.device_path],
                                          input=self.sudo_password + '\n',
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        for line in result.stdout.split('\n'):
                            if 'Model Number:' in line and info['model'] == 'Unknown':
                                model = line.split(':', 1)[1].strip()
                                if model:
                                    info['model'] = model
                            elif 'Serial Number:' in line and info['serial'] == 'Unknown':
                                serial = line.split(':', 1)[1].strip()
                                if serial:
                                    info['serial'] = serial
                except Exception as e:
                    print(f"hdparm failed: {e}", file=sys.stderr)
                        
            # Get filesystem info
            result = subprocess.run(['lsblk', '-f', self.device_path], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                output = result.stdout.lower()
                if 'ext4' in output:
                    info['filesystem'] = 'ext4'
                elif 'fat32' in output or 'vfat' in output:
                    info['filesystem'] = 'FAT32'
                elif 'ntfs' in output:
                    info['filesystem'] = 'NTFS'
                    
        except Exception as e:
            print(f"Error getting device info: {e}", file=sys.stderr)
            
        return info
    
    def run_test(self) -> None:
        """Run the actual badblocks test with real-time progress"""
        try:
            # Output command being run to terminal
            cmd_str = f"sudo badblocks -v -s {self.device_path}"
            print(f"$ {cmd_str}", file=sys.stderr, flush=True)
            print(f"Starting badblocks test on {self.device_path}...", file=sys.stderr, flush=True)

            # Try different approaches to get real-time badblocks output
            success = False

            # Approach 1: Use script command to create a pseudo-terminal
            try:
                cmd = [
                    'sudo', '-S', 'script', '-q', '-c',
                    f'stdbuf -o0 -e0 badblocks -v -s -c 1024 {self.device_path}',
                    '/dev/null'
                ]
                success = self._run_badblocks_command(cmd)
            except Exception as e:
                print(f"Script approach failed: {e}", file=sys.stderr, flush=True)

            # Approach 2: Direct stdbuf approach (fallback)
            if not success:
                try:
                    cmd = [
                        'sudo', '-S', 'stdbuf', '-o0', '-e0', 'badblocks',
                        '-v', '-s', '-c', '1024', self.device_path
                    ]
                    success = self._run_badblocks_command(cmd)
                except Exception as e:
                    print(f"Stdbuf approach failed: {e}", file=sys.stderr, flush=True)

            # Approach 3: Basic badblocks (final fallback)
            if not success:
                try:
                    cmd = [
                        'sudo', '-S', 'badblocks',
                        '-v', '-s', self.device_path
                    ]
                    success = self._run_badblocks_command(cmd)
                except Exception as e:
                    print(f"Basic approach failed: {e}", file=sys.stderr, flush=True)

            if not success:
                raise Exception("All badblocks execution approaches failed")

        except Exception as e:
            print(f"Error running test: {e}", file=sys.stderr, flush=True)
            self.cancelled = True

    def _run_badblocks_command(self, cmd) -> bool:
        """Run a specific badblocks command and handle its output"""
        try:
            # Create a process with pipes for all streams
            self.process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,  # Line buffered
                universal_newlines=True
            )

            # Properly handle sudo password with special characters
            try:
                self.process.stdin.write(self.sudo_password + '\n')
                self.process.stdin.flush()
            except BrokenPipeError:
                pass  # Process may have exited already
            
            # Use threading to read both stdout and stderr simultaneously
            import threading
            
            def read_stream(stream, is_stderr=False):
                """Read from a stream and process lines"""
                try:
                    for line in iter(stream.readline, ''):
                        if self.cancelled:
                            break
                        line = line.strip()
                        if line and not self._is_sensitive_line(line):
                            print(line, file=sys.stderr if is_stderr else sys.stdout, flush=True)
                            
                            # Parse for badblocks output regardless of stream
                            if line.isdigit():
                                self.bad_sectors += 1
                                print(f"Bad sector found: {line}", file=sys.stderr, flush=True)
                            self._parse_badblocks_output(line)
                except Exception as e:
                    print(f"Error reading {'stderr' if is_stderr else 'stdout'}: {e}", file=sys.stderr, flush=True)

            # Start threads for both stdout and stderr
            stdout_thread = threading.Thread(target=read_stream, args=(self.process.stdout, False))
            stderr_thread = threading.Thread(target=read_stream, args=(self.process.stderr, True))
            
            stdout_thread.start()
            stderr_thread.start()
            
            # Wait for both threads to finish
            stdout_thread.join()
            stderr_thread.join()
            
            # Wait for process to finish
            self.process.wait()
            return True
        except Exception as e:
            print(f"Error in _run_badblocks_command: {e}", file=sys.stderr, flush=True)
            return False

            return exit_code == 0 or exit_code == 1  # 1 might be expected for some errors

        except Exception as e:
            print(f"Error in _run_badblocks_command: {e}", file=sys.stderr, flush=True)
            return False

    def _is_sensitive_line(self, line: str) -> bool:
        """Check if a line contains sensitive information that shouldn't be displayed"""
        sensitive_patterns = [
            self.sudo_password,  # Don't show the actual password
            '[sudo] password',   # Don't show sudo password prompts
            'Sorry, try again',  # Don't show password retry messages
            'sudo: no password was provided',  # Don't show password errors
            'could not determine device size',  # Hide this error since we show size in UI
            'no such file or directory while trying to determine device size',
            'warning: could not determine device size',
        ]

        line_lower = line.lower()
        for pattern in sensitive_patterns:
            if pattern and pattern.lower() in line_lower:
                return True
        return False

    def _parse_badblocks_output(self, line: str) -> None:
        """Parse badblocks output for progress and bad sectors"""
        try:
            # Check for bad sector numbers (standalone digits)
            if line.isdigit():
                self.bad_sectors += 1
                print(f"Bad sector found: {line}", file=sys.stderr, flush=True)
                return

            # Parse progress patterns from badblocks
            # Pattern 1: "Checking blocks 1234567 to 2345678"
            if "Checking blocks" in line:
                match = re.search(r'Checking blocks (\d+) to (\d+)', line)
                if match:
                    current_block = int(match.group(1))
                    # badblocks uses 1024-byte blocks, so current_block is already correct
                    self.tested_sectors = current_block
                    return

            # Pattern 2: "Checking for bad blocks (read-only test): X.XX% done, H:MM elapsed. (E/E/E errors)"
            # This is the format you showed in your screenshot
            if "% done" in line and "elapsed" in line:
                match = re.search(r'(\d+(?:\.\d+)?)%\s+done', line)
                if match:
                    percentage = float(match.group(1))
                    self.tested_sectors = int((percentage / 100.0) * self.total_sectors)
                    return

            # Pattern 3: Simple percentage format
            if "%" in line and "done" not in line:
                match = re.search(r'(\d+(?:\.\d+)?)%', line)
                if match:
                    percentage = float(match.group(1))
                    self.tested_sectors = int((percentage / 100.0) * self.total_sectors)
                    return

            # Pattern 4: "Reading and comparing: X% done"
            if "Reading and comparing" in line and "%" in line:
                match = re.search(r'(\d+(?:\.\d+)?)%', line)
                if match:
                    percentage = float(match.group(1))
                    self.tested_sectors = int((percentage / 100.0) * self.total_sectors)
                    return

            # Pattern 5: Block numbers being tested (e.g., "Testing with pattern 0xaa: 1234567")
            if "Testing with pattern" in line:
                match = re.search(r'Testing with pattern.*?(\d+)', line)
                if match:
                    current_block = int(match.group(1))
                    # badblocks uses 1024-byte blocks, so current_block is already correct
                    self.tested_sectors = current_block
                    return

            # Pattern 6: Progress with block ranges (alternative format)
            if "blocks" in line and "/" in line:
                match = re.search(r'(\d+)/(\d+)\s+blocks', line)
                if match:
                    current_blocks = int(match.group(1))
                    total_blocks = int(match.group(2))
                    if total_blocks > 0:
                        percentage = (current_blocks / total_blocks) * 100
                        self.tested_sectors = int((percentage / 100.0) * self.total_sectors)
                        return

        except (ValueError, AttributeError) as e:
            # Ignore parsing errors and continue
            pass

    def _output_progress(self) -> None:
        """Output current progress as JSON"""
        elapsed = time.time() - self.start_time
        
        # Calculate estimated time remaining
        if self.tested_sectors > 0 and elapsed > 0:
            sectors_per_second = self.tested_sectors / elapsed
            remaining_sectors = self.total_sectors - self.tested_sectors
            estimated_remaining = remaining_sectors / sectors_per_second if sectors_per_second > 0 else 0
        else:
            estimated_remaining = 0
            
        progress = {
            'total_sectors': self.total_sectors,
            'tested_sectors': self.tested_sectors,
            'bad_sectors': self.bad_sectors,
            'good_sectors': self.tested_sectors - self.bad_sectors,
            'elapsed_seconds': elapsed,
            'estimated_remaining_seconds': estimated_remaining,
            'progress_percentage': (self.tested_sectors / self.total_sectors * 100) if self.total_sectors > 0 else 0,
            'is_complete': False,
            'is_cancelled': self.cancelled
        }
        
        # Ensure we flush after each JSON update
        print(json.dumps({
            'type': 'progress',
            'data': progress
        }), flush=True)
    
    def cancel(self) -> None:
        """Cancel the running test"""
        self.cancelled = True
        if self.process:
            self.process.terminate()
            time.sleep(1)
            if self.process.poll() is None:
                self.process.kill()
    
    def get_final_results(self) -> Dict:
        """Get final test results"""
        elapsed = time.time() - self.start_time
        
        return {
            'total_sectors': self.total_sectors,
            'tested_sectors': self.tested_sectors,
            'bad_sectors': self.bad_sectors,
            'good_sectors': self.tested_sectors - self.bad_sectors,
            'elapsed_seconds': elapsed,
            'estimated_remaining_seconds': 0,
            'progress_percentage': 100.0,
            'is_complete': True,
            'is_cancelled': self.cancelled
        }


def signal_handler(signum, frame):
    """Handle interrupt signals"""
    global tester
    if tester:
        tester.cancel()
    sys.exit(0)

def main():
    if len(sys.argv) != 3:
        print("Usage: sector_test.py <device_path> <sudo_password>", file=sys.stderr)
        sys.exit(1)

    device_path = sys.argv[1]
    sudo_password = sys.argv[2]
    
    global tester
    tester = SectorTester(device_path, sudo_password)
    
    # Set up signal handlers for graceful cancellation
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Output device info first
        device_info = tester.get_device_info()
        # Add total_sectors to device info
        device_info['total_sectors'] = tester.total_sectors
        print(json.dumps({'type': 'device_info', 'data': device_info}), flush=True)

        # Run the test
        tester.run_test()

        # Output final results
        final_results = tester.get_final_results()
        print(json.dumps({'type': 'final_results', 'data': final_results}), flush=True)
        
    except Exception as e:
        error_result = {
            'type': 'error',
            'data': {'message': str(e)}
        }
        print(json.dumps(error_result), flush=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
