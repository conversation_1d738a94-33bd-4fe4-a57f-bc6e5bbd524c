from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import subprocess
import os
import sys
import json
from typing import List, Dict

app = FastAPI()

# Allow CORS for Flutter web
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

def parse_arp_output(output: str) -> List[Dict[str, str]]:
    devices = []
    lines = output.decode().split('\n')
    
    for line in lines:
        if not line.strip():
            continue
            
        parts = line.split()
        if len(parts) >= 3:
            ip = parts[0]
            mac = parts[1]
            vendor = "Unknown"
            
            if len(parts) >= 4:
                vendor = " ".join(parts[3:])
                
            devices.append({
                "ip": ip,
                "mac": mac,
                "vendor": vendor
            })
    
    return devices

@app.get("/discover")
def discover_devices():
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(["powershell", "Get-NetNeighbor"], capture_output=True)
        elif sys.platform == 'darwin':  # macOS
            result = subprocess.run(["arp", "-a"], capture_output=True)
        else:  # Linux
            result = subprocess.run(["arp", "-n"], capture_output=True)
        
        devices = parse_arp_output(result.stdout)
        return {"status": "success", "devices": devices}
    except Exception as e:
        return {"status": "error", "message": str(e)}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
