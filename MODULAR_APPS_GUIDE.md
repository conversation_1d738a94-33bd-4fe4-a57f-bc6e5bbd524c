# Jelly Pi Modular Apps System

## Overview

The Jelly Pi app now supports a modular app architecture where each app is self-contained in its own folder within `assets/apps/`. This makes it easy for third-party developers to create and distribute their own apps.

## Key Features

### ✅ **Folder-Based Organization**
- Each app lives in its own folder: `assets/apps/app_name/`
- All app files (code, assets, config) are contained within the app folder
- Clean separation between different apps

### ✅ **Dynamic App Loading**
- Apps are loaded dynamically from their folder structure
- Support for both new modular apps and legacy apps
- Automatic app discovery through manifest system

### ✅ **Enhanced App Metadata**
- Rich app configuration with `app.json`
- App-specific settings and permissions
- Asset management and dependency tracking

### ✅ **Third-Party Developer Support**
- Clear development guidelines and examples
- Standardized app structure and API
- Easy integration with existing Jelly Pi functionality

## Folder Structure

```
assets/apps/
├── manifest.json                 # Global app registry
├── README.md                     # Developer guide
├── system_monitor/               # Built-in system monitor app
│   ├── app.json                 # App configuration
│   ├── system_monitor_app.dart  # Main app code
│   ├── config.json              # App settings
│   └── icon.png                 # App icon
├── file_manager/                 # File management app
│   ├── app.json
│   ├── config.json
│   └── (uses built-in implementation)
├── network_tools/                # Third-party network tools
│   ├── app.json
│   ├── network_tools_app.dart
│   ├── config.json
│   └── styles.json
└── your_custom_app/              # Your custom app
    ├── app.json
    ├── your_app.dart
    └── assets/
```

## App Configuration (app.json)

Each app must have an `app.json` file with the following structure:

```json
{
  "id": "unique_app_id",
  "title": "App Display Name",
  "description": "Brief description",
  "iconName": "material_icon_name",
  "route": "/app_route",
  "category": "system|utilities|network|development|media|security|custom",
  "isEnabled": true,
  "sortOrder": 1,
  "version": "1.0.0",
  "author": "Developer Name",
  "appFolderPath": "assets/apps/app_folder",
  "mainDartFile": "main_app_file.dart",
  "assetFiles": ["icon.png", "config.json"],
  "appConfig": {
    "setting1": "value1",
    "setting2": true
  },
  "metadata": {
    "requiresConnection": true,
    "realTimeUpdates": false,
    "features": ["Feature 1", "Feature 2"]
  },
  "requiredPermissions": ["ssh_access", "network_access"]
}
```

## Implementation Details

### App Module Manager
- **File**: `lib/services/app_module_manager.dart`
- **Purpose**: Manages loading and organizing apps
- **Features**: 
  - Loads both modular and legacy apps
  - Handles app ordering and categorization
  - Manages app states and preferences

### Dynamic App Loader
- **File**: `lib/services/dynamic_app_loader.dart`
- **Purpose**: Dynamically loads apps from folder structure
- **Features**:
  - Asset loading and management
  - App launcher registration
  - Runtime app validation

### Enhanced App Model
- **File**: `lib/models/app_module.dart`
- **Purpose**: Represents app metadata and configuration
- **New Fields**:
  - `appFolderPath`: Path to app folder
  - `mainDartFile`: Main Dart implementation file
  - `assetFiles`: List of app assets
  - `appConfig`: App-specific configuration

## Example Apps

### 1. System Monitor (Built-in)
- **Location**: `assets/apps/system_monitor/`
- **Purpose**: Monitor system resources and performance
- **Features**: CPU, memory, temperature, disk monitoring

### 2. File Manager (Hybrid Implementation)
- **Location**: `assets/apps/file_manager/` (config) + `lib/screens/file_manager_screen.dart` (implementation)
- **Purpose**: Browse and manage files on remote devices
- **Status**: Full implementation with enhanced toolbar actions and no right-click menus

### 3. Network Tools (Third-party Example)
- **Location**: `assets/apps/network_tools/`
- **Purpose**: Network diagnostics and monitoring
- **Features**: Ping, traceroute, network analysis

## Benefits for Developers

### 🎯 **Easy Development**
- Clear structure and guidelines
- Self-contained app development
- No need to modify core Jelly Pi code

### 🔧 **Flexible Configuration**
- Rich metadata support
- Custom settings and permissions
- Asset management

### 🚀 **Simple Distribution**
- Just share the app folder
- Easy installation process
- Version management support

### 🔒 **Secure Integration**
- Permission-based access control
- Sandboxed app execution
- Safe asset loading

## Migration from Legacy Apps

The system supports both new modular apps and legacy apps:

- **New apps**: Use folder-based structure in `assets/apps/app_name/`
- **Legacy apps**: Continue to work from `assets/apps/*.json` files
- **Gradual migration**: Apps can be migrated one by one

## Future Enhancements

- **Hot reload**: Dynamic app updates without restart
- **App store**: Centralized app distribution
- **Plugin system**: More advanced app integration
- **Sandboxing**: Enhanced security and isolation
- **External loading**: Load apps from external directories

## Getting Started

1. **Read the guide**: Check `assets/apps/README.md`
2. **Study examples**: Look at existing apps in the folders
3. **Create your app**: Follow the folder structure
4. **Test and iterate**: Use the development tools
5. **Share your app**: Distribute your app folder

The modular app system makes Jelly Pi extensible and developer-friendly while maintaining the clean, organized structure that users expect.
