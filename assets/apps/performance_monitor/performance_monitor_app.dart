import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:jelly_pi/services/app_state.dart';

/// Performance Monitor App Entry Point
class PerformanceMonitorApp {
  static const String id = 'performance_monitor';
  static const String title = 'Performance Monitor';
  static const String version = '1.2.0';

  /// Launch the Performance Monitor app
  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const PerformanceMonitorScreen(),
      ),
    );
  }

  /// Check if the app can run
  static bool canRun(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    return appState.connectedDevices.isNotEmpty;
  }
}

/// Performance Monitor Screen
class PerformanceMonitorScreen extends StatefulWidget {
  const PerformanceMonitorScreen({super.key});

  @override
  State<PerformanceMonitorScreen> createState() =>
      _PerformanceMonitorScreenState();
}

class _PerformanceMonitorScreenState extends State<PerformanceMonitorScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Performance Monitor'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.speed,
              size: 64,
              color: Color(0xFF6366F1),
            ),
            SizedBox(height: 16),
            Text(
              'Performance Monitor',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Advanced monitoring coming soon!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Features:\n• Real-time CPU monitoring\n• Memory usage graphs\n• Network traffic analysis\n• Disk I/O monitoring\n• Temperature alerts',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
