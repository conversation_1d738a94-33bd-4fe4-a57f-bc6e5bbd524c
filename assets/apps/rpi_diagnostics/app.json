{"id": "rpi_diagnostics", "title": "RPi Diagnostics", "description": "Comprehensive Raspberry Pi boot diagnostics and repair tool for SD cards and USB devices", "iconName": "health_and_safety", "route": "/rpi_diagnostics", "category": "system", "isEnabled": true, "sortOrder": 10, "version": "1.0.0", "author": "Jelly Pi Team", "appFolderPath": "assets/apps/rpi_diagnostics", "mainDartFile": "lib/screens/rpi_diagnostics_app.dart", "assetFiles": ["config.json", "README.md"], "appConfig": {"defaultTheme": "auto", "autoBackup": true, "confirmChanges": true, "showAdvancedOptions": false, "enableDeepScan": true, "autoDetectDevices": true}, "metadata": {"requiresConnection": false, "realTimeUpdates": false, "features": ["Boot Configuration Analysis", "Overclocking Safety Checks", "File System Integrity", "Automatic Repairs", "Configuration Backup", "Interactive Diagnostics"]}, "requiredPermissions": ["file_access"], "raspberryPiOnly": false}