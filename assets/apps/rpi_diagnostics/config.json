{"name": "RPi Diagnostics", "version": "1.0.0", "description": "Configuration for Raspberry Pi Diagnostics app", "settings": {"defaultTheme": "auto", "autoBackup": true, "confirmChanges": true, "showAdvancedOptions": false, "enableDeepScan": true, "autoDetectDevices": true, "refreshInterval": 5, "enableNotifications": true}, "ui": {"theme": "auto", "showTooltips": true, "compactMode": false, "showProgressDetails": true}, "permissions": {"fileAccess": true, "systemAccess": false, "networkAccess": false}, "diagnosticCategories": ["boot_config", "overclocking", "file_system", "hardware_config", "power_management", "memory_config"], "safetyFeatures": {"backupBeforeChanges": true, "validateConfigs": true, "requireConfirmation": true, "emergencyRestore": true, "safeOverclockLimits": true}, "autoRepairs": {"fixCommonIssues": true, "resetOverclocking": true, "repairBootConfig": true, "optimizeMemorySplit": true}}