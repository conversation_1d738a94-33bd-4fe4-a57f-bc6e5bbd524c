# RPi Diagnostics App

A comprehensive Raspberry Pi boot diagnostics and repair tool for SD cards and USB devices.

## Features

### Device Selection
- **Auto-detection**: Automatically scans for Raspberry Pi boot devices
- **Manual selection**: Browse and select SD card/USB device mount points
- **Validation**: Verifies presence of Raspberry Pi boot files

### Comprehensive Diagnostics
- **Boot Configuration Analysis**: Analyzes config.txt and cmdline.txt files
- **Overclocking Safety Checks**: Validates overclocking settings for stability
- **File System Integrity**: Checks for file system corruption
- **Hardware Configuration**: Validates hardware-specific settings
- **Memory Configuration**: Analyzes memory split settings
- **Power Management**: Checks power-related configurations

### Automatic Repairs
- **Safe Overclocking Reset**: Resets dangerous overclocking to safe defaults
- **Configuration Repair**: Fixes common boot configuration issues
- **Memory Optimization**: Optimizes memory split for better performance
- **Backup System**: Automatically backs up files before modification

### Interactive Features
- **Progress Indicators**: Real-time feedback during operations
- **Issue Classification**: Categorizes issues by severity (Critical, Warning, Info)
- **Auto-fix Suggestions**: Identifies issues that can be automatically resolved
- **Detailed Reporting**: Comprehensive diagnostic reports

## Usage

1. **Select Device**: Choose your Raspberry Pi SD card or USB device
2. **Run Diagnostics**: Perform full diagnostics or quick scan
3. **Review Issues**: Examine any detected problems
4. **Apply Fixes**: Use automatic repairs or manual fixes
5. **Backup Management**: Restore from backups if needed

## Safety Features

- **Automatic Backups**: All modified files are backed up with .bk extension
- **Validation**: Configuration changes are validated before application
- **Confirmation**: User confirmation required for critical changes
- **Emergency Restore**: Quick restore from backups if issues occur

## Supported Configurations

- Raspberry Pi OS (32-bit and 64-bit)
- Raspberry Pi models: Pi 1, Pi 2, Pi 3, Pi 4, Pi 5, Pi Zero
- Boot locations: /boot/config.txt and /boot/firmware/config.txt
- SD cards and USB boot devices

## Technical Details

- **No SSH Required**: Works directly with local storage devices
- **Cross-platform**: Supports Windows, macOS, and Linux
- **File System Support**: FAT32, exFAT boot partitions
- **Configuration Parsing**: Advanced config.txt and cmdline.txt analysis

## Version

1.0.0 - Initial release with core diagnostic functionality
