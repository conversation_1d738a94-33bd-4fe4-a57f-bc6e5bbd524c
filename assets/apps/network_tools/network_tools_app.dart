import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:jelly_pi/services/app_state.dart';

/// Network Tools App - Example Third Party App
class NetworkToolsApp {
  static const String id = 'network_tools';
  static const String title = 'Network Tools';
  static const String version = '1.0.0';

  /// Launch the Network Tools app
  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const NetworkToolsScreen(),
      ),
    );
  }

  /// Check if the app can run
  static bool canRun(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    return appState.connectedDevices.isNotEmpty;
  }

  /// Get app configuration
  static Map<String, dynamic> getConfig() {
    return {
      'defaultPingCount': 4,
      'defaultTimeout': 5000,
      'enableTraceroute': true,
      'enablePortScan': false,
    };
  }
}

/// Network Tools Screen
class NetworkToolsScreen extends StatefulWidget {
  const NetworkToolsScreen({super.key});

  @override
  State<NetworkToolsScreen> createState() => _NetworkToolsScreenState();
}

class _NetworkToolsScreenState extends State<NetworkToolsScreen> {
  final _targetController = TextEditingController();
  bool _isRunning = false;
  String _results = '';

  @override
  void dispose() {
    _targetController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Network Tools'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          IconButton(
            onPressed: _clearResults,
            icon: const Icon(Icons.clear),
            tooltip: 'Clear Results',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            const Row(
              children: [
                Icon(
                  Icons.network_check,
                  size: 32,
                  color: Color(0xFF6366F1),
                ),
                SizedBox(width: 12),
                Text(
                  'Network Diagnostics',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Third-party modular app example',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 24),

            // Input Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Target Host',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _targetController,
                      decoration: const InputDecoration(
                        hintText: 'Enter IP address or hostname',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.language),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton.icon(
                          onPressed: _isRunning ? null : _runPing,
                          icon: const Icon(Icons.network_ping),
                          label: const Text('Ping'),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton.icon(
                          onPressed: _isRunning ? null : _runTraceroute,
                          icon: const Icon(Icons.route),
                          label: const Text('Traceroute'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Results Section
            const Text(
              'Results',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Card(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  child: _isRunning
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CircularProgressIndicator(),
                              SizedBox(height: 16),
                              Text('Running network test...'),
                            ],
                          ),
                        )
                      : _results.isEmpty
                          ? Center(
                              child: Text(
                                'No results yet.\nRun a network test to see output here.',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                ),
                              ),
                            )
                          : SingleChildScrollView(
                              child: Text(
                                _results,
                                style: const TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                              ),
                            ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _runPing() async {
    if (_targetController.text.isEmpty) {
      _showError('Please enter a target host');
      return;
    }

    setState(() {
      _isRunning = true;
      _results = '';
    });

    // Simulate ping operation
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isRunning = false;
      _results = '''PING ${_targetController.text} (***********): 56 data bytes
64 bytes from ***********: icmp_seq=0 ttl=64 time=1.234 ms
64 bytes from ***********: icmp_seq=1 ttl=64 time=1.456 ms
64 bytes from ***********: icmp_seq=2 ttl=64 time=1.123 ms
64 bytes from ***********: icmp_seq=3 ttl=64 time=1.345 ms

--- ${_targetController.text} ping statistics ---
4 packets transmitted, 4 packets received, 0.0% packet loss
round-trip min/avg/max/stddev = 1.123/1.290/1.456/0.142 ms''';
    });
  }

  void _runTraceroute() async {
    if (_targetController.text.isEmpty) {
      _showError('Please enter a target host');
      return;
    }

    setState(() {
      _isRunning = true;
      _results = '';
    });

    // Simulate traceroute operation
    await Future.delayed(const Duration(seconds: 3));

    setState(() {
      _isRunning = false;
      _results =
          '''traceroute to ${_targetController.text} (*******), 30 hops max, 60 byte packets
 1  *********** (***********)  1.234 ms  1.456 ms  1.123 ms
 2  ******** (********)  5.678 ms  5.432 ms  5.789 ms
 3  *********** (***********)  12.345 ms  12.678 ms  12.234 ms
 4  ******* (*******)  15.123 ms  15.456 ms  15.234 ms''';
    });
  }

  void _clearResults() {
    setState(() {
      _results = '';
    });
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
