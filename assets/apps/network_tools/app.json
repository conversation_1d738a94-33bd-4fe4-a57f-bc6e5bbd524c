{"id": "network_tools", "title": "Network Tools", "description": "Network diagnostics and monitoring tools", "iconName": "network_check", "route": "/network_tools", "category": "network", "isEnabled": true, "sortOrder": 3, "version": "1.0.0", "author": "Third Party Developer", "appFolderPath": "assets/apps/network_tools", "mainDartFile": "network_tools_app.dart", "assetFiles": ["icon.png", "config.json", "styles.json"], "appConfig": {"defaultPingCount": 4, "defaultTimeout": 5000, "enableTraceroute": true, "enablePortScan": false, "maxPorts": 1000}, "metadata": {"requiresConnection": true, "realTimeUpdates": true, "features": ["Ping testing", "Network latency monitoring", "Traceroute functionality", "Port scanning", "Network interface monitoring", "Bandwidth testing"]}, "requiredPermissions": ["network_access", "ssh_access"]}