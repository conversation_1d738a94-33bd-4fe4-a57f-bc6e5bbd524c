{"id": "file_manager", "title": "File Manager", "description": "Browse and manage files on remote devices with enhanced toolbar actions", "iconName": "folder", "route": "/file_manager", "category": "utilities", "isEnabled": true, "sortOrder": 2, "version": "1.0.0", "author": "Jelly Pi Team", "appFolderPath": "assets/apps/file_manager", "mainDartFile": "file_manager_app.dart", "assetFiles": ["config.json"], "appConfig": {"defaultPath": "/home", "showHiddenFiles": false, "enableUpload": true, "enableDownload": true, "maxFileSize": "100MB"}, "metadata": {"requiresConnection": true, "realTimeUpdates": false, "features": ["File browsing", "File upload/download", "Directory navigation", "File permissions", "Enhanced toolbar actions", "Copy/Cut/Paste operations", "No right-click menus"]}}