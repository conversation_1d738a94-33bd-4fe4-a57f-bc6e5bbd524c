import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:jelly_pi/services/app_state.dart';
import 'package:jelly_pi/screens/system_monitor_screen.dart';

/// System Monitor App Entry Point
/// This is the main entry point for the System Monitor app module
class SystemMonitorApp {
  static const String id = 'system_monitor';
  static const String title = 'System Monitor';
  static const String version = '1.0.0';

  /// Launch the System Monitor app
  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SystemMonitorScreen(),
      ),
    );
  }

  /// Get app configuration
  static Map<String, dynamic> getConfig() {
    return {
      'refreshInterval': 10,
      'showAdvancedMetrics': true,
      'defaultView': 'grid',
    };
  }

  /// Check if the app can run (has required permissions/connections)
  static bool canRun(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    return appState.connectedDevices.isNotEmpty;
  }

  /// Get app status information
  static Map<String, dynamic> getStatus(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    final connectedDevices = appState.connectedDevices;

    return {
      'canRun': connectedDevices.isNotEmpty,
      'connectedDevices': connectedDevices.length,
      'selectedDevice': appState.selectedDevice?.name ?? 'None',
      'lastUpdate': DateTime.now().toIso8601String(),
    };
  }
}
