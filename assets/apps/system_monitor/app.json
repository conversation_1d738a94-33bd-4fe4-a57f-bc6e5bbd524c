{"id": "system_monitor", "title": "System Monitor", "description": "Monitor system resources and performance", "iconName": "monitor", "route": "/system_monitor", "category": "system", "isEnabled": true, "sortOrder": 1, "version": "1.0.0", "author": "Jelly Pi Team", "appFolderPath": "assets/apps/system_monitor", "mainDartFile": "system_monitor_app.dart", "assetFiles": ["icon.png", "config.json"], "appConfig": {"refreshInterval": 10, "showAdvancedMetrics": true, "defaultView": "grid"}, "metadata": {"requiresConnection": true, "realTimeUpdates": true, "features": ["CPU usage monitoring", "Memory usage tracking", "Temperature monitoring", "Disk usage analysis", "Individual CPU core details", "GPU information", "Power status", "Network interfaces"]}, "requiredPermissions": ["system_monitor", "ssh_access"]}