# System Monitor

Real-time system monitoring app for Jelly Pi that displays comprehensive
hardware and system health information.

## Author

<PERSON><PERSON> Team

## Version

1.0.0

## Features

- Real-time CPU usage monitoring
- Memory usage tracking
- Temperature monitoring
- Network statistics
- Disk usage information
- System uptime and load averages
- Beautiful responsive design

## Installation

This app is part of the core Jelly Pi app suite.

## Usage

1.  Connect to a device
2.  Launch the System Monitor app
3.  View real-time system metrics
4.  Monitor system health and performance

## Configuration

Edit the config.json file to customize:

- Update intervals
- Display preferences
- Alert thresholds
- Metric visibility

## Requirements

- Active connection to target device
- System monitoring permissions

## Features

The System Monitor provides comprehensive system health information including
CPU usage, memory statistics, temperature readings, and network activity in a
beautiful, responsive interface.

