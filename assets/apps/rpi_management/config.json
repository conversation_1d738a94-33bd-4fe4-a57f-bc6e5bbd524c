{"name": "Raspberry Pi Management", "version": "1.0.0", "description": "Configuration for Raspberry Pi Management app", "settings": {"defaultTheme": "auto", "autoBackup": true, "confirmChanges": true, "showAdvancedOptions": false, "refreshInterval": 5, "enableNotifications": true}, "ui": {"theme": "auto", "showTooltips": true, "compactMode": false, "showSystemStatus": true}, "permissions": {"systemAccess": true, "networkAccess": true, "fileAccess": true, "adminAccess": true}, "managementCategories": ["system_config", "overclocking", "network", "hardware", "boot", "services", "updates"], "safetyFeatures": {"backupBeforeChanges": true, "validateConfigs": true, "requireConfirmation": true, "emergencyRestore": true}}