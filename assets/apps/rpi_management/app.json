{"id": "rpi_management", "title": "Raspberry Pi Management", "description": "Comprehensive Raspberry Pi system management and configuration", "iconName": "settings", "route": "/rpi_management", "category": "system", "isEnabled": true, "sortOrder": 3, "version": "1.0.0", "author": "Jelly Pi Team", "appFolderPath": "assets/apps/rpi_management", "mainDartFile": "lib/screens/rpi_management_app.dart", "assetFiles": ["config.json", "README.md"], "appConfig": {"defaultTheme": "auto", "autoBackup": true, "confirmChanges": true, "showAdvancedOptions": false}, "metadata": {"requiresConnection": true, "realTimeUpdates": true, "features": ["System Configuration", "Overclocking & Performance", "Network Management", "Hardware Control", "Boot Configuration", "Firmware Updates", "Temperature Monitoring", "Power Management", "Service Management", "<PERSON><PERSON><PERSON><PERSON>"]}, "requiredPermissions": ["ssh_access", "system_admin"], "raspberryPiOnly": true}