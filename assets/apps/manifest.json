{"version": "2.0.0", "description": "<PERSON><PERSON>pp <PERSON> - Enhanced <PERSON>", "structure": "folder-based", "apps": [{"id": "system_monitor", "folder": "system_monitor", "configFile": "app.json", "enabled": true}, {"id": "file_manager", "folder": "file_manager", "configFile": "app.json", "enabled": true}, {"id": "network_tools", "folder": "network_tools", "configFile": "app.json", "enabled": true}, {"id": "rpi_management", "folder": "rpi_management", "configFile": "app.json", "enabled": true}, {"id": "camera_stream", "folder": "camera_stream", "configFile": "app.json", "enabled": true}, {"id": "gpio_control", "folder": "gpio_control", "configFile": "app.json", "enabled": true}, {"id": "log_viewer", "folder": "log_viewer", "configFile": "app.json", "enabled": true}, {"id": "performance_monitor", "folder": "performance_monitor", "configFile": "app.json", "enabled": true}, {"id": "ai_assistant", "folder": "ai_assistant", "configFile": "app.json", "enabled": true}, {"id": "rpi_diagnostics", "folder": "rpi_diagnostics", "configFile": "app.json", "enabled": true}], "legacy_modules": []}