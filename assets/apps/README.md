# Jelly Pi App Development Guide

This guide explains how to create modular apps for Jelly Pi using the new
folder-based structure.

## App Structure

Each app should be contained in its own folder within `assets/apps/`. The folder
structure should be:

```
assets/apps/
├── your_app_name/
│   ├── app.json          # App configuration and metadata
│   ├── your_app.dart     # Main app implementation
│   ├── config.json       # App-specific configuration (optional)
│   ├── icon.png          # App icon (optional)
│   └── other_assets/     # Additional assets (optional)
└── manifest.json         # Global app registry
```
## Creating a New App

### 1\. Create App Folder

Create a new folder in `assets/apps/` with your app's name (use snake_case).

### 2\. Create app.json

This is the main configuration file for your app:

```json
{
  "id": "your_app_id",
  "title": "Your App Name",
  "description": "Brief description of your app",
  "iconName": "icon_name",
  "route": "/your_app",
  "category": "utilities",
  "isEnabled": true,
  "sortOrder": 10,
  "version": "1.0.0",
  "author": "Your Name",
  "appFolderPath": "assets/apps/your_app_name",
  "mainDartFile": "your_app.dart",
  "assetFiles": [
    "icon.png",
    "config.json"
  ],
  "appConfig": {
    "setting1": "value1",
    "setting2": true
  },
  "metadata": {
    "requiresConnection": true,
    "realTimeUpdates": false,
    "features": [
      "Feature 1",
      "Feature 2"
    ]
  },
  "requiredPermissions": [
    "ssh_access"
  ]
}
```
### 3\. Create Main Dart File

Create your main app implementation file:

```dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../lib/services/app_state.dart';

class YourApp {
  static const String id = 'your_app_id';
  static const String title = 'Your App Name';
  static const String version = '1.0.0';

  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const YourAppScreen(),
      ),
    );
  }

  static bool canRun(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    return appState.connectedDevices.isNotEmpty;
  }
}

class YourAppScreen extends StatefulWidget {
  const YourAppScreen({super.key});

  @override
  State<YourAppScreen> createState() => _YourAppScreenState();
}

class _YourAppScreenState extends State<YourAppScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Your App Name'),
      ),
      body: const Center(
        child: Text('Your app content here'),
      ),
    );
  }
}
```
### 4\. Update manifest.json

Add your app to the global manifest:

```json
{
  "version": "2.0.0",
  "description": "Jelly Pi App Modules Manifest",
  "structure": "folder-based",
  "apps": [
    {
      "id": "your_app_id",
      "folder": "your_app_name",
      "configFile": "app.json",
      "enabled": true
    }
  ]
}
```
## App Configuration Options

### Categories

- `system`: System monitoring and management
- `development`: Development tools and utilities
- `network`: Network tools and diagnostics
- `media`: Media streaming and management
- `security`: Security and access control
- `utilities`: General utilities and tools
- `custom`: Custom user applications

### Icon Names

Use Material Design icon names (without Icons. prefix):

- `monitor`, `folder`, `code`, `camera`, `network_check`, etc.

### Permissions

- `ssh_access`: Access to SSH connections
- `file_access`: File system access
- `network_access`: Network operations
- `system_monitor`: System monitoring capabilities

## Best Practices

1.  **Keep apps self-contained**: All app files should be in the app folder
2.  **Use descriptive names**: Choose clear, descriptive names for your app and
    files
3.  **Handle errors gracefully**: Always check for device connections and handle
    errors
4.  **Follow naming conventions**: Use snake_case for folders and files
5.  **Document your app**: Include clear descriptions and feature lists
6.  **Test thoroughly**: Test your app with different device states

## Example Apps

Check the existing apps for examples:

- `system_monitor/`: System monitoring app
- `file_manager/`: File management app (placeholder)

## Integration

Your app will automatically appear in the Apps grid once added to the manifest.
The app launcher will handle loading and displaying your app based on the
configuration.

