# GPIO Control

Hardware control app for Jelly Pi that allows you to control GPIO pins and sensors on Raspberry Pi devices.

## Author
Je<PERSON> Pi Team

## Version
1.0.0

## Features
- GPIO pin control (input/output)
- Sensor readings
- PWM control
- I2C/SPI support
- Pin state monitoring
- Safety mode protection

## Installation
This app is part of the core Jelly Pi app suite.

## Usage
1. Connect to a Raspberry Pi device
2. Launch the GPIO Control app
3. Select pins to configure
4. Set pin modes and states
5. Monitor sensor readings

## Configuration
Edit the config.json file to customize:
- Default pin modes
- Pull-up resistor settings
- Refresh rates
- Safety mode settings

## Requirements
- Active connection to Raspberry Pi
- GPIO access permissions
- Hardware control permissions

## Safety
This app includes safety features to prevent hardware damage. Always verify pin configurations before applying changes.
