{"name": "GPIO Control", "version": "1.0.0", "description": "Configuration for GPIO Control app", "settings": {"defaultMode": "input", "enablePullUp": true, "refreshRate": 1000, "showAdvanced": false, "safetyMode": true}, "ui": {"theme": "auto", "showPinDiagram": true, "compactView": false}, "permissions": {"systemAccess": true, "networkAccess": false, "fileAccess": false}, "supportedDevices": ["raspberry_pi"]}