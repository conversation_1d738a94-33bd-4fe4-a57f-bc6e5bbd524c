{"id": "gpio_control", "title": "GPIO Control", "description": "Control GPIO pins and sensors", "iconName": "sensors", "route": "/gpio_control", "category": "system", "isEnabled": true, "sortOrder": 5, "version": "1.0.0", "author": "Jelly Pi Team", "appFolderPath": "assets/apps/gpio_control", "mainDartFile": "gpio_control_app.dart", "assetFiles": ["config.json", "README.md"], "appConfig": {"defaultMode": "input", "enablePullUp": true, "refreshRate": 1000, "showAdvanced": false}, "metadata": {"requiresConnection": true, "deviceTypes": ["raspberry_pi"], "features": ["GPIO pin control", "Sensor readings", "PWM control", "I2C/SPI support", "Pin state monitoring"]}, "requiredPermissions": ["gpio_access", "hardware_control", "ssh_access"], "raspberryPiOnly": true}