import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:jelly_pi/services/app_state.dart';

/// GPIO Control App Entry Point
class GpioControlApp {
  static const String id = 'gpio_control';
  static const String title = 'GPIO Control';
  static const String version = '1.0.0';

  /// Launch the GPIO Control app
  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const GpioControlScreen(),
      ),
    );
  }

  /// Check if the app can run
  static bool canRun(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    return appState.connectedDevices.isNotEmpty;
  }
}

/// GPIO Control Screen
class GpioControlScreen extends StatefulWidget {
  const GpioControlScreen({super.key});

  @override
  State<GpioControlScreen> createState() => _GpioControlScreenState();
}

class _GpioControlScreenState extends State<GpioControlScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GPIO Control'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.sensors,
              size: 64,
              color: Color(0xFF6366F1),
            ),
            SizedBox(height: 16),
            Text(
              'GPIO Control',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Hardware control coming soon!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Features:\n• GPIO pin control\n• Sensor readings\n• PWM control\n• I2C/SPI support\n• Pin state monitoring',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
