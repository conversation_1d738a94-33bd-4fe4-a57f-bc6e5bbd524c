# Camera Stream

Live camera streaming app for Jelly Pi that allows you to view camera feeds from connected devices.

## Author
<PERSON><PERSON> Team

## Version
1.0.0

## Features
- Live video streaming
- Multiple camera support
- Recording capability
- Motion detection
- Stream quality control
- Multiple protocol support (MJPEG, RTSP, WebRTC)

## Installation
This app is part of the core Jelly Pi app suite.

## Usage
1. Connect to a device with camera capabilities
2. Launch the Camera Stream app
3. Select camera source
4. Configure stream settings
5. Start streaming

## Configuration
Edit the config.json file to customize:
- Default video quality
- Auto-start behavior
- Recording settings
- Motion detection sensitivity

## Requirements
- Active connection to device with camera
- Camera access permissions
- Network access for streaming
