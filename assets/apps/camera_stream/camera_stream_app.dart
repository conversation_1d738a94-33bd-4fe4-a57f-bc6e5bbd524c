import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:jelly_pi/services/app_state.dart';

/// Camera Stream App Entry Point
class CameraStreamApp {
  static const String id = 'camera_stream';
  static const String title = 'Camera Stream';
  static const String version = '1.0.0';

  /// Launch the Camera Stream app
  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CameraStreamScreen(),
      ),
    );
  }

  /// Check if the app can run
  static bool canRun(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);
    return appState.connectedDevices.isNotEmpty;
  }
}

/// Camera Stream Screen
class CameraStreamScreen extends StatefulWidget {
  const CameraStreamScreen({super.key});

  @override
  State<CameraStreamScreen> createState() => _CameraStreamScreenState();
}

class _CameraStreamScreenState extends State<CameraStreamScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Camera Stream'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.camera_alt,
              size: 64,
              color: Color(0xFF6366F1),
            ),
            SizedBox(height: 16),
            Text(
              'Camera Stream',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Live camera streaming coming soon!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Features:\n• Live video streaming\n• Multiple camera support\n• Recording capability\n• Motion detection\n• Stream quality control',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
