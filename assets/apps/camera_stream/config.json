{"name": "Camera Stream", "version": "1.0.0", "description": "Configuration for Camera Stream app", "settings": {"defaultQuality": "720p", "autoStart": false, "enableRecording": true, "motionDetection": false, "streamTimeout": 30000}, "ui": {"theme": "auto", "showControls": true, "fullscreenMode": false}, "permissions": {"systemAccess": false, "networkAccess": true, "fileAccess": false}, "streamProtocols": ["mjpeg", "rtsp", "webrtc"]}