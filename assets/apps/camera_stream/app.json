{"id": "camera_stream", "title": "Camera Stream", "description": "View live camera feeds", "iconName": "camera", "route": "/camera_stream", "category": "media", "isEnabled": true, "sortOrder": 4, "version": "1.0.0", "author": "Jelly Pi Team", "appFolderPath": "assets/apps/camera_stream", "mainDartFile": "camera_stream_app.dart", "assetFiles": ["config.json", "README.md"], "appConfig": {"defaultQuality": "720p", "autoStart": false, "enableRecording": true, "motionDetection": false}, "metadata": {"requiresConnection": true, "streamProtocols": ["mjpeg", "rtsp", "webrtc"], "features": ["Live video streaming", "Multiple camera support", "Recording capability", "Motion detection", "Stream quality control"]}, "requiredPermissions": ["camera_access", "network_access", "ssh_access"]}