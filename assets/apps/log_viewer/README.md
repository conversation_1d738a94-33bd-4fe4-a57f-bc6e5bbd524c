# Log Viewer

System log analysis app for Jelly Pi that allows you to view and analyze system logs with advanced filtering and search capabilities.

## Author
Community

## Version
1.1.0

## Features
- Real-time log monitoring
- Advanced filtering
- Search functionality
- Export logs
- Log rotation management
- Multiple log type support
- Colorized output

## Installation
This app is part of the core Jelly Pi app suite.

## Usage
1. Connect to a device
2. Launch the Log Viewer app
3. Select log type to view
4. Use filters and search to find specific entries
5. Export logs if needed

## Configuration
Edit the config.json file to customize:
- Maximum log lines to display
- Refresh intervals
- Auto-refresh settings
- Display preferences

## Supported Log Types
- System logs (syslog)
- Authentication logs (auth.log)
- Kernel logs (kern.log)
- Device messages (dmesg)
- Custom log files

## Requirements
- Active connection to target device
- File read permissions
- System log access permissions
