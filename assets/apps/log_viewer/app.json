{"id": "log_viewer", "title": "Log Viewer", "description": "View and analyze system logs with filtering and search", "iconName": "logs", "route": "/log_viewer", "category": "utilities", "isEnabled": true, "sortOrder": 15, "version": "1.1.0", "author": "Community", "appFolderPath": "assets/apps/log_viewer", "mainDartFile": "log_viewer_app.dart", "assetFiles": ["config.json", "README.md"], "appConfig": {"maxLogLines": 10000, "refreshInterval": 5000, "autoRefresh": true, "showTimestamps": true}, "metadata": {"requiresConnection": true, "supportedLogTypes": ["syslog", "auth.log", "kern.log", "dmesg", "custom"], "features": ["Real-time log monitoring", "Advanced filtering", "Search functionality", "Export logs", "Log rotation management"], "maxLogLines": 10000, "refreshInterval": 5000}, "requiredPermissions": ["file_read", "system_logs"]}