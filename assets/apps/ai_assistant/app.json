{"id": "ai_assistant", "title": "AI Assistant", "description": "An AI Assistant", "iconName": "auto_awesome", "iconPath": null, "route": "/ai_assistant", "category": "utilities", "isEnabled": true, "sortOrder": 9, "metadata": {"requiresConnection": false, "realTimeUpdates": false, "features": ["AI Chat Interface", "Smart Responses", "Context Awareness"]}, "requiredPermissions": [], "version": "1.0.0", "author": "<PERSON><PERSON>", "appFolderPath": "assets/apps/ai_assistant", "mainDartFile": "ai_assistant_app.dart", "assetFiles": ["config.json", "README.md"], "appConfig": {"theme": "auto", "showTooltips": true}, "raspberryPiOnly": false}