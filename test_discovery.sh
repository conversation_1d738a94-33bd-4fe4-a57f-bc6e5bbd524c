#!/bin/bash

# Test Network Discovery
echo "🔍 Testing Network Discovery..."
echo "================================"

# Test the Python script directly
echo "Testing Python script directly:"
python3 lib/scripts/device_discovery.py /tmp/test_discovery.json

if [ -f "/tmp/test_discovery.json" ]; then
    echo "✅ Python script works!"
    echo "Found devices:"
    cat /tmp/test_discovery.json | python3 -m json.tool
else
    echo "❌ Python script failed"
fi

echo ""
echo "🚀 Now test in the app:"
echo "1. Open the app (should be running)"
echo "2. Go to the Discovery tab"
echo "3. Click 'Start Network Scan'"
echo "4. Check the terminal for debug output"
