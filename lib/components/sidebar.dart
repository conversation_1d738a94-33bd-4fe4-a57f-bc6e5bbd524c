import 'package:flutter/material.dart';

class Sidebar extends StatefulWidget {
  final bool isCollapsed;
  final int selectedSection;
  final Function(bool) onToggle;
  final Function(int) onSectionSelected;

  const Sidebar({
    super.key,
    required this.isCollapsed,
    required this.selectedSection,
    required this.onToggle,
    required this.onSectionSelected,
  });

  @override
  State<Sidebar> createState() => _SidebarState();
}

class _SidebarState extends State<Sidebar> {
  String? selectedDeviceIp;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.isCollapsed ? 80 : 250,
      color: Colors.blueGrey[900],
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: IconButton(
              icon: Icon(
                widget.isCollapsed ? Icons.menu_open : Icons.menu,
                color: Colors.white,
              ),
              onPressed: () => widget.onToggle(!widget.isCollapsed),
            ),
          ),
          Expanded(
            child: ListView(
              children: [
                _buildNavItem(
                  context: context,
                  icon: Icons.devices,
                  label: 'Manage Devices',
                  sectionIndex: 0,
                ),
                _buildNavItem(
                  context: context,
                  icon: Icons.terminal,
                  label: 'Terminal',
                  sectionIndex: 1,
                ),
                _buildNavItem(
                  context: context,
                  icon: Icons.settings,
                  label: 'Configure',
                  sectionIndex: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required int sectionIndex,
  }) {
    final isSelected = widget.selectedSection == sectionIndex;
    return ListTile(
      leading: Icon(icon, color: Colors.white),
      title: widget.isCollapsed
          ? null
          : Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w200,
                color: isSelected
                    ? Colors.white
                    : Colors.white.withValues(alpha: 0.7),
              ),
            ),
      selected: isSelected,
      onTap: () {
        widget.onSectionSelected(sectionIndex);
        if (sectionIndex == 1) {
          // Terminal section
          // Show device selection dialog
          _showDeviceSelection(context);
        }
      },
    );
  }

  void _showDeviceSelection(BuildContext context) {
    // Device selection is now handled by the main app state
    // This method is no longer needed as device selection is managed centrally
    debugPrint('Device selection handled by main app state');
  }
}
