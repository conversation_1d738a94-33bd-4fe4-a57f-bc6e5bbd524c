import 'package:flutter/material.dart';

// Configuration section for managing device settings and services
class ConfigureSection extends StatelessWidget {
  const ConfigureSection({super.key});

  @override
  Widget build(BuildContext context) {
    // Main configuration card containing all settings
    return Card(
      margin: const EdgeInsets.all(16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header with icon and title
            Row(
              children: [
                const Icon(Icons.settings, size: 28),
                const SizedBox(width: 8),
                Text(
                  'Device Configuration',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ],
            ),
            const SizedBox(height: 24),
            // General settings section
            Text(
              'General Settings',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            // Hostname input field
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Device Hostname',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.computer),
              ),
            ),
            const SizedBox(height: 16),
            // Timezone input field
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Timezone',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.access_time),
              ),
            ),
            const SizedBox(height: 24),
            // Services section with toggle switches
            Text(
              'Services',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            // SSH service toggle
            Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: SwitchListTile(
                title: const Text('Enable SSH'),
                subtitle: const Text('Secure Shell access'),
                value: true,
                secondary: const Icon(Icons.terminal),
                onChanged: (value) {
                  // SSH toggle functionality would be implemented here
                  debugPrint('SSH toggle: $value');
                },
              ),
            ),
            // VNC service toggle
            Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: SwitchListTile(
                title: const Text('Enable VNC'),
                subtitle: const Text('Remote desktop access'),
                value: false,
                secondary: const Icon(Icons.desktop_windows),
                onChanged: (value) {
                  // VNC toggle functionality would be implemented here
                  debugPrint('VNC toggle: $value');
                },
              ),
            ),
            // Web interface toggle
            Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: SwitchListTile(
                title: const Text('Enable Web Interface'),
                subtitle: const Text('Access via web browser'),
                value: true,
                secondary: const Icon(Icons.language),
                onChanged: (value) {
                  // Web interface toggle functionality would be implemented here
                  debugPrint('Web interface toggle: $value');
                },
              ),
            ),
            const SizedBox(height: 24),
            // Action buttons at bottom
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                // Reset button to restore default settings
                OutlinedButton(
                  onPressed: () {
                    // Reset configuration to defaults
                    debugPrint('Resetting configuration to defaults');
                  },
                  child: const Text('Reset'),
                ),
                const SizedBox(width: 8),
                // Save button to apply configuration changes
                ElevatedButton.icon(
                  icon: const Icon(Icons.save),
                  label: const Text('Save Configuration'),
                  onPressed: () {
                    // Save configuration changes
                    debugPrint('Saving configuration changes');
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
