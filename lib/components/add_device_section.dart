import 'package:flutter/material.dart';
import '../widgets/add_device_dialog.dart';

// Widget for adding new devices to the system
// Provides a form for entering device details
class AddDeviceSection extends StatelessWidget {
  const AddDeviceSection({super.key});

  @override
  Widget build(BuildContext context) {
    // Main card containing the add device form
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            const Text(
              'Add New Device',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // Device name input field
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Device Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            // IP address input field
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'IP Address',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            // Add device button
            Align(
              alignment: Alignment.centerRight,
              child: ElevatedButton(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) => const AddDeviceDialog(),
                ),
                child: const Text('Add Device'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
