import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:xterm/xterm.dart';
import '../state/app_state.dart';

final _terminal = Terminal();

class SSHSection extends StatefulWidget {
  const SSHSection({super.key});

  @override
  State<SSHSection> createState() => _SSHSectionState();
}

class _SSHSectionState extends State<SSHSection> {
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _commandController = TextEditingController();

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadSavedCredentials();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  Future<void> _loadSavedCredentials() async {
    final appState = Provider.of<AppState>(context, listen: false);
    if (appState.currentSSHConnection == null) return;

    final credentials = await appState
        .getSSHCredentials(appState.currentSSHConnection!.deviceIp);

    if (credentials != null) {
      setState(() {
        _usernameController.text = credentials['username']!;
        _passwordController.text = credentials['password']!;
      });
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    if (!appState.isDeviceConnected || appState.currentSSHConnection == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('No device connected'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                appState.setCurrentIndex(0); // Switch to device management
              },
              child: const Text('Connect a Device'),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Connected to: ${appState.currentSSHConnection?.deviceIp}',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              ElevatedButton(
                onPressed: () {
                  appState
                      .disconnectSSH(appState.currentSSHConnection!.deviceIp);
                  appState.setCurrentIndex(0); // Return to device management
                },
                child: const Text('Disconnect'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: StreamBuilder<String>(
                      stream: appState.currentSSHConnection?.outputStream,
                      builder: (context, snapshot) {
                        final output = appState.terminalOutput;

                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _scrollToBottom();
                        });

                        return ListView.builder(
                          controller: _scrollController,
                          itemCount: output.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8.0,
                                vertical: 2.0,
                              ),
                              child: Text(
                                output[index],
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontFamily: 'Courier',
                                  fontSize: 14,
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TerminalView(_terminal),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _commandController.dispose();
    super.dispose();
  }
}
