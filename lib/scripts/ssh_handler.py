import json
import sys
import paramiko
from typing import Dict

class SSHHandler:
    def __init__(self):
        self.client = None
        self.connected = False

    def connect(self, host: str, username: str, password: str) -> Dict[str, str]:
        try:
            if not host or not username or not password:
                return {
                    'status': 'failed',
                    'error': 'Missing required connection parameters'
                }

            # Create SSH client
            self.client = paramiko.SSHClient()
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Connect to host
            # Set up environment variables
            transport = self.client.get_transport()
            if transport:
                transport.set_keepalive(30)
            
            self.client.connect(
                hostname=host,
                port=22,
                username=username,
                password=password,
                timeout=10
            )
            
            # Set TERM environment variable
            stdin, stdout, stderr = self.client.exec_command('export TERM=xterm-256color')
            exit_status = stdout.channel.recv_exit_status()
            if exit_status != 0:
                raise Exception('Failed to set TERM environment variable')
            
            # Test connection
            stdin, stdout, stderr = self.client.exec_command('echo "Connection test"')
            exit_status = stdout.channel.recv_exit_status()
            
            if exit_status != 0:
                return {
                    'status': 'failed',
                    'error': stderr.read().decode().strip()
                }
            
            self.connected = True
            return {
                'status': 'success',
                'message': f'Connected to {host}'
            }
        except Exception as e:
            return {
                'status': 'failed',
                'error': f'Connection error: {str(e)}'
            }

    def execute_command(self, command: str) -> Dict[str, str]:
        if not self.connected or not self.client:
            return {
                'status': 'failed',
                'error': 'Not connected to any host'
            }

        try:
            # Create full interactive shell channel
            chan = self.client.invoke_shell()
            chan.get_pty(term='xterm-256color', 
                       width=80, 
                       height=24,
                       width_pixels=800,
                       height_pixels=600)
            
            # Set up comprehensive terminal environment
            chan.update_environment({
                'TERM': 'xterm-256color',
                'COLORTERM': 'truecolor',
                'LANG': 'en_US.UTF-8',
                'COLUMNS': '80',
                'LINES': '24'
            })
            
            # Execute command with proper shell initialization
            chan.send(command + '\n')
            
            # Read output with proper ANSI handling
            output = ''
            while True:
                # Handle both stdout and stderr
                if chan.recv_ready():
                    data = chan.recv(4096).decode('utf-8', errors='replace')
                    output += data
                if chan.recv_stderr_ready():
                    error = chan.recv_stderr(4096).decode('utf-8', errors='replace')
                    output += error
                
                # Check for EOF or timeout
                if chan.exit_status_ready() and not chan.recv_ready():
                    break
            
            exit_status = chan.recv_exit_status()
            
            # Handle terminal resizing
            def on_resize(cols, rows, width_px, height_px):
                chan.resize_pty(width=cols, height=rows, 
                              width_px=width_px, height_px=height_px)
            
            # Preserve ANSI escape sequences
            output = output.replace('\r\n', '\n').replace('\n', '\r\n')
            
            return {
                'status': 'success' if exit_status == 0 else 'failed',
                'output': output,
                'exit_status': exit_status
            }
        except Exception as e:
            print(f'Command execution failed: {str(e)}')
            sys.stdout.flush()
            return

    def close(self):
        if self.connected and self.client:
            self.client.close()
            self.connected = False

def main():
    try:
        if len(sys.argv) < 4:
            raise ValueError('Invalid number of arguments')
            
        host = sys.argv[1]
        username = sys.argv[2]
        password = sys.argv[3]
        interactive = len(sys.argv) > 4 and sys.argv[4] == '--interactive'

        handler = SSHHandler()
        connection_result = handler.connect(host, username, password)
        
        if connection_result['status'] == 'failed':
            print(connection_result['error'])
            sys.exit(1)

        if interactive:
            # Interactive mode - read commands from stdin
            while True:
                try:
                    command = sys.stdin.readline().strip()
                    if not command:
                        break
                        
                    result = handler.execute_command(command)
                    if result is not None and 'output' in result:
                        print(result['output'])
                    sys.stdout.flush()
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f'Command error: {str(e)}')
                    sys.stdout.flush()
        else:
            # Single command mode
            if len(sys.argv) != 5:
                raise ValueError('Command required in non-interactive mode')
                
            command = sys.argv[4]
            command_result = handler.execute_command(command)
            print(command_result['output'])
            sys.stdout.flush()
            
        handler.close()
        
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
