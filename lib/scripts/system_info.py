#!/usr/bin/env python3
"""
System information gathering script for Raspberry Pi devices
Gets CPU usage, memory usage, temperature, disk usage, and uptime
"""

import subprocess
import json
import re
import time
import platform

def get_cpu_usage():
    """Get comprehensive CPU usage information"""
    cpu_info = {}

    # Overall CPU usage
    try:
        # Get CPU usage using top command
        result = subprocess.run(['top', '-bn1'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'Cpu(s):' in line or '%Cpu(s):' in line:
                    # Extract CPU usage from line like: %Cpu(s):  2.0 us,  1.3 sy,  0.0 ni, 96.7 id
                    match = re.search(r'(\d+\.?\d*)\s*id', line)
                    if match:
                        idle = float(match.group(1))
                        cpu_info['overall'] = round(100 - idle, 1)
                        break

        # Fallback: use vmstat
        if 'overall' not in cpu_info:
            result = subprocess.run(['vmstat', '1', '2'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) >= 4:
                    last_line = lines[-1].split()
                    if len(last_line) >= 15:
                        idle = int(last_line[14])
                        cpu_info['overall'] = round(100 - idle, 1)
    except:
        cpu_info['overall'] = None

    # Per-core CPU usage with multiple methods
    try:
        # Method 1: Try mpstat first
        result = subprocess.run(['mpstat', '-P', 'ALL', '1', '1'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            cores = {}
            for line in result.stdout.split('\n'):
                if 'CPU' in line and '%idle' in line:
                    continue  # Skip header
                if re.match(r'.*\s+\d+\s+', line):
                    parts = line.split()
                    if len(parts) >= 12:
                        try:
                            cpu_num = parts[2]
                            idle = float(parts[-1])
                            if cpu_num.isdigit():
                                cores[f'core{cpu_num}'] = round(100 - idle, 1)
                        except:
                            continue
            if cores:
                cpu_info['cores'] = cores

        # Method 2: Fallback to /proc/stat for per-core data
        if 'cores' not in cpu_info:
            with open('/proc/stat', 'r') as f:
                cores = {}
                for line in f:
                    if line.startswith('cpu') and line[3:4].isdigit():
                        parts = line.split()
                        cpu_num = parts[0][3:]  # Extract number from 'cpu0', 'cpu1', etc.
                        if cpu_num.isdigit():
                            # Calculate CPU usage from /proc/stat
                            user, nice, system, idle, iowait, irq, softirq = map(int, parts[1:8])
                            total = user + nice + system + idle + iowait + irq + softirq
                            if total > 0:
                                usage = round(((total - idle) / total) * 100, 1)
                                cores[f'core{cpu_num}'] = usage
                if cores:
                    cpu_info['cores'] = cores
    except:
        pass

    # CPU frequency information
    try:
        # Current frequency
        with open('/sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq', 'r') as f:
            freq_khz = int(f.read().strip())
            cpu_info['frequency_mhz'] = round(freq_khz / 1000, 0)
    except:
        try:
            # Try vcgencmd for Raspberry Pi
            result = subprocess.run(['vcgencmd', 'measure_clock', 'arm'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                match = re.search(r'frequency\(48\)=(\d+)', result.stdout)
                if match:
                    freq_hz = int(match.group(1))
                    cpu_info['frequency_mhz'] = round(freq_hz / 1000000, 0)
        except:
            cpu_info['frequency_mhz'] = None

    # CPU model information
    try:
        with open('/proc/cpuinfo', 'r') as f:
            cpuinfo_content = f.read()

            # Extract CPU model name
            for line in cpuinfo_content.split('\n'):
                if line.startswith('model name'):
                    cpu_info['model'] = line.split(':', 1)[1].strip()
                    break
                elif line.startswith('Processor'):
                    cpu_info['processor'] = line.split(':', 1)[1].strip()
                elif line.startswith('Hardware'):
                    cpu_info['hardware'] = line.split(':', 1)[1].strip()

            # If no model name found, try to construct from other fields
            if 'model' not in cpu_info:
                if 'processor' in cpu_info:
                    cpu_info['model'] = cpu_info['processor']
                elif 'hardware' in cpu_info:
                    cpu_info['model'] = cpu_info['hardware']
    except:
        pass

    return cpu_info if cpu_info else None

def get_memory_usage():
    """Get comprehensive memory usage information including swap and process breakdown"""
    memory_info = {}

    # Basic memory information
    try:
        result = subprocess.run(['free', '-m'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if line.startswith('Mem:'):
                    parts = line.split()
                    total = int(parts[1])
                    used = int(parts[2])
                    free = int(parts[3])
                    shared = int(parts[4]) if len(parts) > 4 else 0
                    buff_cache = int(parts[5]) if len(parts) > 5 else 0
                    available = int(parts[6]) if len(parts) > 6 else free
                    usage_percent = round((used / total) * 100, 1)

                    memory_info.update({
                        'total': total,
                        'used': used,
                        'free': free,
                        'shared': shared,
                        'buff_cache': buff_cache,
                        'available': available,
                        'usage_percent': usage_percent
                    })
                elif line.startswith('Swap:'):
                    parts = line.split()
                    if len(parts) >= 4:
                        swap_total = int(parts[1])
                        swap_used = int(parts[2])
                        swap_free = int(parts[3])
                        swap_usage_percent = round((swap_used / swap_total) * 100, 1) if swap_total > 0 else 0

                        memory_info['swap'] = {
                            'total': swap_total,
                            'used': swap_used,
                            'free': swap_free,
                            'usage_percent': swap_usage_percent
                        }
    except:
        pass

    # Detailed memory breakdown from /proc/meminfo
    try:
        with open('/proc/meminfo', 'r') as f:
            meminfo = {}
            for line in f:
                if ':' in line:
                    key, value = line.split(':', 1)
                    value = value.strip()
                    if 'kB' in value:
                        # Convert kB to MB
                        value_kb = int(value.replace('kB', '').strip())
                        meminfo[key] = round(value_kb / 1024, 1)
                    else:
                        meminfo[key] = value

            # Add useful detailed metrics
            memory_info['detailed'] = {
                'buffers': meminfo.get('Buffers', 0),
                'cached': meminfo.get('Cached', 0),
                'slab': meminfo.get('Slab', 0),
                'kernel_stack': meminfo.get('KernelStack', 0),
                'page_tables': meminfo.get('PageTables', 0),
                'dirty': meminfo.get('Dirty', 0),
                'writeback': meminfo.get('Writeback', 0),
                'anon_pages': meminfo.get('AnonPages', 0),
                'mapped': meminfo.get('Mapped', 0),
                'shmem': meminfo.get('Shmem', 0)
            }
    except:
        pass

    # Top memory-consuming processes
    try:
        result = subprocess.run(['ps', 'aux', '--sort=-%mem'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.split('\n')[1:]  # Skip header
            processes = []
            for i, line in enumerate(lines[:10]):  # Top 10 processes
                if line.strip():
                    parts = line.split(None, 10)
                    if len(parts) >= 11:
                        try:
                            processes.append({
                                'pid': int(parts[1]),
                                'user': parts[0],
                                'cpu_percent': float(parts[2]),
                                'mem_percent': float(parts[3]),
                                'vsz': int(parts[4]),  # Virtual memory size in KB
                                'rss': int(parts[5]),  # Resident set size in KB
                                'command': parts[10][:50]  # Truncate long commands
                            })
                        except (ValueError, IndexError):
                            continue
            memory_info['top_processes'] = processes
    except:
        pass

    return memory_info if memory_info else None

def get_temperature():
    """Get comprehensive temperature information"""
    temps = {}

    # CPU temperature (main)
    try:
        with open('/sys/class/thermal/thermal_zone0/temp', 'r') as f:
            temp_millidegrees = int(f.read().strip())
            temps['cpu'] = round(temp_millidegrees / 1000.0, 1)
    except:
        try:
            # Try vcgencmd for Raspberry Pi
            result = subprocess.run(['vcgencmd', 'measure_temp'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                match = re.search(r'temp=(\d+\.?\d*)\'C', result.stdout)
                if match:
                    temps['cpu'] = float(match.group(1))
        except:
            temps['cpu'] = None

    # Try to get additional thermal zones
    for i in range(1, 10):  # Check thermal_zone1 through thermal_zone9
        try:
            with open(f'/sys/class/thermal/thermal_zone{i}/temp', 'r') as f:
                temp_millidegrees = int(f.read().strip())
                temp_celsius = round(temp_millidegrees / 1000.0, 1)

                # Try to get the zone type
                try:
                    with open(f'/sys/class/thermal/thermal_zone{i}/type', 'r') as type_f:
                        zone_type = type_f.read().strip()
                        temps[zone_type] = temp_celsius
                except:
                    temps[f'zone{i}'] = temp_celsius
        except:
            continue

    # GPU temperature for Raspberry Pi
    try:
        result = subprocess.run(['vcgencmd', 'measure_temp'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            match = re.search(r'temp=(\d+\.?\d*)\'C', result.stdout)
            if match:
                temps['gpu'] = float(match.group(1))
    except:
        temps['gpu'] = None

    return temps if temps else None

def get_disk_usage():
    """Get comprehensive disk usage and performance information"""
    disk_info = {}

    # Basic disk usage for all mounted filesystems
    try:
        result = subprocess.run(['df', '-h'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.split('\n')[1:]  # Skip header
            filesystems = []
            for line in lines:
                if line.strip() and not line.startswith('tmpfs') and not line.startswith('devtmpfs'):
                    parts = line.split()
                    if len(parts) >= 6:
                        filesystems.append({
                            'filesystem': parts[0],
                            'size': parts[1],
                            'used': parts[2],
                            'available': parts[3],
                            'usage_percent': int(parts[4].rstrip('%')),
                            'mount_point': parts[5]
                        })
            disk_info['filesystems'] = filesystems

            # Root filesystem for backward compatibility
            for fs in filesystems:
                if fs['mount_point'] == '/':
                    disk_info.update({
                        'total': fs['size'],
                        'used': fs['used'],
                        'available': fs['available'],
                        'usage_percent': fs['usage_percent']
                    })
                    break
    except:
        pass

    # Disk I/O statistics
    try:
        with open('/proc/diskstats', 'r') as f:
            disk_stats = []
            for line in f:
                parts = line.split()
                if len(parts) >= 14:
                    device = parts[2]
                    # Skip loop devices and ram devices
                    if not device.startswith(('loop', 'ram', 'dm-')):
                        disk_stats.append({
                            'device': device,
                            'reads_completed': int(parts[3]),
                            'reads_merged': int(parts[4]),
                            'sectors_read': int(parts[5]),
                            'time_reading': int(parts[6]),
                            'writes_completed': int(parts[7]),
                            'writes_merged': int(parts[8]),
                            'sectors_written': int(parts[9]),
                            'time_writing': int(parts[10]),
                            'io_in_progress': int(parts[11]),
                            'time_io': int(parts[12]),
                            'weighted_time_io': int(parts[13])
                        })
            disk_info['io_stats'] = disk_stats
    except:
        pass

    # Block device information
    try:
        result = subprocess.run(['lsblk', '-J', '-o', 'NAME,SIZE,TYPE,MOUNTPOINT,FSTYPE,MODEL'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            import json
            lsblk_data = json.loads(result.stdout)
            disk_info['block_devices'] = lsblk_data.get('blockdevices', [])
    except:
        pass

    # Disk queue length and performance metrics
    try:
        disk_performance = []
        result = subprocess.run(['iostat', '-x', '1', '2'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            in_device_section = False
            for line in lines:
                if 'Device' in line and 'r/s' in line:
                    in_device_section = True
                    continue
                elif in_device_section and line.strip():
                    parts = line.split()
                    if len(parts) >= 12:
                        try:
                            device = parts[0]
                            if not device.startswith(('loop', 'ram')):
                                disk_performance.append({
                                    'device': device,
                                    'reads_per_sec': float(parts[1]),
                                    'writes_per_sec': float(parts[2]),
                                    'kb_read_per_sec': float(parts[3]),
                                    'kb_written_per_sec': float(parts[4]),
                                    'avg_queue_size': float(parts[8]),
                                    'avg_wait_time': float(parts[9]),
                                    'avg_service_time': float(parts[10]),
                                    'utilization_percent': float(parts[11])
                                })
                        except (ValueError, IndexError):
                            continue
                elif in_device_section and not line.strip():
                    break
            disk_info['performance'] = disk_performance
    except:
        pass

    # USB and hotplugged devices
    try:
        result = subprocess.run(['lsusb'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            usb_devices = []
            for line in result.stdout.split('\n'):
                if line.strip() and 'Bus' in line:
                    # Parse USB device info
                    match = re.search(r'Bus (\d+) Device (\d+): ID ([0-9a-f:]+) (.+)', line)
                    if match:
                        usb_devices.append({
                            'bus': match.group(1),
                            'device': match.group(2),
                            'id': match.group(3),
                            'description': match.group(4)
                        })
            disk_info['usb_devices'] = usb_devices
    except:
        pass

    # Recently mounted devices (from dmesg)
    try:
        result = subprocess.run(['dmesg', '-T'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            recent_mounts = []
            lines = result.stdout.split('\n')[-100:]  # Last 100 lines
            for line in lines:
                if any(keyword in line.lower() for keyword in ['usb', 'sd', 'mount', 'attached']):
                    if any(device in line for device in ['sda', 'sdb', 'sdc', 'sdd', 'usb']):
                        recent_mounts.append(line.strip())
            disk_info['recent_activity'] = recent_mounts[-10:]  # Last 10 relevant entries
    except:
        pass

    return disk_info if disk_info else None

def get_uptime():
    """Get system uptime"""
    try:
        with open('/proc/uptime', 'r') as f:
            uptime_seconds = float(f.read().split()[0])

            days = int(uptime_seconds // 86400)
            hours = int((uptime_seconds % 86400) // 3600)
            minutes = int((uptime_seconds % 3600) // 60)

            if days > 0:
                return f"{days}d {hours}h {minutes}m"
            elif hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"
    except:
        pass
    return None

def get_load_average():
    """Get system load average"""
    try:
        with open('/proc/loadavg', 'r') as f:
            load_data = f.read().split()
            return {
                '1min': float(load_data[0]),
                '5min': float(load_data[1]),
                '15min': float(load_data[2])
            }
    except:
        pass
    return None

def get_network_info():
    """Get network interface information"""
    try:
        result = subprocess.run(['ip', 'addr', 'show'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            interfaces = []
            current_interface = None

            for line in result.stdout.split('\n'):
                if re.match(r'^\d+:', line):
                    if current_interface:
                        interfaces.append(current_interface)

                    match = re.search(r'^\d+:\s+(\w+):', line)
                    if match:
                        current_interface = {
                            'name': match.group(1),
                            'state': 'UP' if 'UP' in line else 'DOWN',
                            'ips': []
                        }
                elif current_interface and 'inet ' in line:
                    match = re.search(r'inet\s+([0-9.]+)', line)
                    if match:
                        current_interface['ips'].append(match.group(1))

            if current_interface:
                interfaces.append(current_interface)

            return interfaces
    except:
        pass
    return []

def get_gpu_info():
    """Get GPU information (Raspberry Pi specific)"""
    gpu_info = {}

    try:
        # GPU memory split
        result = subprocess.run(['vcgencmd', 'get_mem', 'gpu'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            match = re.search(r'gpu=(\d+)M', result.stdout)
            if match:
                gpu_info['memory_mb'] = int(match.group(1))
    except:
        gpu_info['memory_mb'] = None

    try:
        # GPU frequency
        result = subprocess.run(['vcgencmd', 'measure_clock', 'core'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            match = re.search(r'frequency\(1\)=(\d+)', result.stdout)
            if match:
                freq_hz = int(match.group(1))
                gpu_info['frequency_mhz'] = round(freq_hz / 1000000, 0)
    except:
        gpu_info['frequency_mhz'] = None

    return gpu_info if gpu_info else None

def get_processes():
    """Get detailed process information including disk I/O"""
    processes = []

    try:
        # Get detailed process information using ps
        result = subprocess.run([
            'ps', 'axo',
            'pid,ppid,user,pcpu,pmem,vsz,rss,tty,stat,start,time,comm,cmd'
        ], capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            lines = result.stdout.split('\n')[1:]  # Skip header
            for line in lines:
                if line.strip():
                    # Split with maxsplit to preserve command line with spaces
                    parts = line.strip().split(None, 12)
                    if len(parts) >= 13:
                        try:
                            process = {
                                'pid': int(parts[0]),
                                'ppid': int(parts[1]),
                                'user': parts[2],
                                'cpu_percent': float(parts[3]),
                                'mem_percent': float(parts[4]),
                                'vsz': int(parts[5]),  # Virtual memory size in KB
                                'rss': int(parts[6]),  # Resident set size in KB
                                'tty': parts[7],
                                'stat': parts[8],  # Process state
                                'start': parts[9],
                                'time': parts[10],  # CPU time
                                'comm': parts[11],  # Command name
                                'cmd': parts[12][:200]  # Full command line (truncated)
                            }
                            processes.append(process)
                        except (ValueError, IndexError):
                            continue
    except:
        pass

    # Add disk I/O information for each process
    try:
        for process in processes:
            pid = process['pid']
            io_stats = _get_process_io_stats(pid)
            process.update(io_stats)
    except:
        # If we can't get I/O stats, set default values
        for process in processes:
            process.update({
                'read_bytes': 0,
                'write_bytes': 0,
                'read_rate': 0.0,
                'write_rate': 0.0,
                'disk_usage': 0.0
            })

    return processes

def _get_process_io_stats(pid):
    """Get I/O statistics for a specific process"""
    io_stats = {
        'read_bytes': 0,
        'write_bytes': 0,
        'read_rate': 0.0,
        'write_rate': 0.0,
        'disk_usage': 0.0
    }

    try:
        # Read process I/O stats from /proc/[pid]/io
        with open(f'/proc/{pid}/io', 'r') as f:
            io_data = f.read()

        for line in io_data.split('\n'):
            if line.startswith('read_bytes:'):
                io_stats['read_bytes'] = int(line.split()[1])
            elif line.startswith('write_bytes:'):
                io_stats['write_bytes'] = int(line.split()[1])

        # Calculate total disk usage in MB
        total_bytes = io_stats['read_bytes'] + io_stats['write_bytes']
        io_stats['disk_usage'] = total_bytes / (1024 * 1024)  # Convert to MB

        # Convert to MB for display (total cumulative I/O)
        io_stats['read_rate'] = io_stats['read_bytes'] / (1024 * 1024)  # MB total
        io_stats['write_rate'] = io_stats['write_bytes'] / (1024 * 1024)  # MB total

        # Try to get more detailed I/O stats from /proc/[pid]/stat for better rate info
        try:
            with open(f'/proc/{pid}/stat', 'r') as f:
                stat_data = f.read().split()
                if len(stat_data) >= 42:
                    # Fields 39-42 contain I/O info (0-indexed, so 38-41)
                    # But these are not always available, so we'll stick with /proc/[pid]/io
                    pass
        except:
            pass

    except (FileNotFoundError, PermissionError, ValueError):
        # Process might have disappeared or we don't have permission
        pass

    return io_stats

def get_services():
    """Get systemd service information"""
    services = []

    try:
        # Get all systemd services
        result = subprocess.run([
            'systemctl', 'list-units', '--type=service', '--all', '--no-pager', '--plain'
        ], capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if line.strip() and '.service' in line:
                    # Parse systemctl output
                    parts = line.split()
                    if len(parts) >= 4:
                        service_name = parts[0]
                        load_state = parts[1]
                        active_state = parts[2]
                        sub_state = parts[3]
                        description = ' '.join(parts[4:]) if len(parts) > 4 else ''

                        service = {
                            'name': service_name,
                            'load_state': load_state,
                            'active_state': active_state,
                            'sub_state': sub_state,
                            'description': description
                        }
                        services.append(service)
    except:
        pass

    # Get additional service details for important services
    important_services = ['ssh', 'networking', 'systemd-resolved', 'cron', 'rsyslog']

    for service_info in services:
        service_name = service_info['name']
        # Check if this is an important service or if it's active
        if any(important in service_name for important in important_services) or service_info['active_state'] == 'active':
            try:
                # Get detailed service status
                result = subprocess.run([
                    'systemctl', 'show', service_name, '--no-pager'
                ], capture_output=True, text=True, timeout=5)

                if result.returncode == 0:
                    details = {}
                    for line in result.stdout.split('\n'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            details[key] = value

                    # Add useful details
                    service_info.update({
                        'main_pid': details.get('MainPID', '0'),
                        'memory_current': details.get('MemoryCurrent', '0'),
                        'cpu_usage_nsec': details.get('CPUUsageNSec', '0'),
                        'restart_count': details.get('NRestarts', '0'),
                        'exec_start': details.get('ExecStart', ''),
                        'unit_file_state': details.get('UnitFileState', ''),
                        'fragment_path': details.get('FragmentPath', '')
                    })
            except:
                pass

    return services

def get_system_processes():
    """Get system process tree and resource usage"""
    process_tree = {}

    try:
        # Get enhanced process tree using pstree with more options
        result = subprocess.run(['pstree', '-p', '-a', '-l', '-u'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            process_tree['tree'] = result.stdout
        else:
            # Fallback to basic pstree if enhanced version fails
            result = subprocess.run(['pstree', '-p', '-a'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                process_tree['tree'] = result.stdout
    except:
        process_tree['tree'] = None

    # Get top CPU consuming processes with enhanced information
    try:
        result = subprocess.run(['ps', 'aux', '--sort=-%cpu'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.split('\n')[1:]  # Skip header
            top_cpu = []
            for line in lines[:20]:  # Top 20 CPU processes (increased from 15)
                if line.strip():
                    parts = line.split(None, 10)
                    if len(parts) >= 11:
                        try:
                            top_cpu.append({
                                'pid': int(parts[1]),
                                'user': parts[0],
                                'cpu_percent': float(parts[2]),
                                'mem_percent': float(parts[3]),
                                'vsz': int(parts[4]),
                                'rss': int(parts[5]),
                                'tty': parts[6],
                                'stat': parts[7],
                                'start': parts[8],
                                'time': parts[9],
                                'command': parts[10][:150]  # Increased from 100 to 150 chars
                            })
                        except (ValueError, IndexError):
                            continue
            process_tree['top_cpu'] = top_cpu
    except:
        process_tree['top_cpu'] = []

    # Get top memory consuming processes
    try:
        result = subprocess.run(['ps', 'aux', '--sort=-%mem'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.split('\n')[1:]  # Skip header
            top_mem = []
            for line in lines[:15]:  # Top 15 memory processes
                if line.strip():
                    parts = line.split(None, 10)
                    if len(parts) >= 11:
                        try:
                            top_mem.append({
                                'pid': int(parts[1]),
                                'user': parts[0],
                                'cpu_percent': float(parts[2]),
                                'mem_percent': float(parts[3]),
                                'vsz': int(parts[4]),
                                'rss': int(parts[5]),
                                'tty': parts[6],
                                'stat': parts[7],
                                'start': parts[8],
                                'time': parts[9],
                                'command': parts[10][:150]
                            })
                        except (ValueError, IndexError):
                            continue
            process_tree['top_mem'] = top_mem
    except:
        process_tree['top_mem'] = []

    # Get enhanced system resource summary
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.split('\n')[1:]  # Skip header
            total_processes = 0
            running_processes = 0
            sleeping_processes = 0
            zombie_processes = 0
            stopped_processes = 0
            idle_processes = 0
            disk_sleep_processes = 0

            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 8:
                        total_processes += 1
                        stat = parts[7]
                        # More detailed process state classification
                        if 'R' in stat:
                            running_processes += 1
                        elif 'S' in stat:
                            sleeping_processes += 1
                        elif 'D' in stat:
                            disk_sleep_processes += 1
                        elif 'T' in stat:
                            stopped_processes += 1
                        elif 'Z' in stat:
                            zombie_processes += 1
                        elif 'I' in stat:
                            idle_processes += 1

            process_tree['summary'] = {
                'total': total_processes,
                'running': running_processes,
                'sleeping': sleeping_processes,
                'disk_sleep': disk_sleep_processes,
                'stopped': stopped_processes,
                'zombie': zombie_processes,
                'idle': idle_processes
            }
    except:
        process_tree['summary'] = None

    return process_tree

def get_os_info():
    """Get operating system information"""
    os_info = {}

    try:
        os_info['system'] = platform.system()  # Linux, Darwin, Windows
        os_info['platform'] = platform.platform()
        os_info['machine'] = platform.machine()  # x86_64, armv7l, etc.
        os_info['processor'] = platform.processor()
        os_info['architecture'] = platform.architecture()[0]  # 64bit, 32bit

        # Get distribution info for Linux
        if platform.system() == 'Linux':
            try:
                with open('/etc/os-release', 'r') as f:
                    for line in f:
                        if line.startswith('PRETTY_NAME='):
                            os_info['distribution'] = line.split('=', 1)[1].strip().strip('"')
                            break
            except:
                try:
                    result = subprocess.run(['lsb_release', '-d'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        os_info['distribution'] = result.stdout.split(':', 1)[1].strip()
                except:
                    os_info['distribution'] = 'Unknown Linux'

        # Check if it's a Raspberry Pi
        try:
            with open('/proc/cpuinfo', 'r') as f:
                cpuinfo = f.read()
                if 'Raspberry Pi' in cpuinfo or 'BCM' in cpuinfo:
                    os_info['is_raspberry_pi'] = True
                    # Extract Pi model from multiple possible sources
                    for line in cpuinfo.split('\n'):
                        if line.startswith('Model'):
                            os_info['pi_model'] = line.split(':', 1)[1].strip()
                            break

                    # If no model found in cpuinfo, try device tree
                    if 'pi_model' not in os_info:
                        try:
                            with open('/proc/device-tree/model', 'r') as f:
                                model = f.read().strip('\x00')
                                if model:
                                    os_info['pi_model'] = model
                        except:
                            pass
                else:
                    os_info['is_raspberry_pi'] = False
        except:
            os_info['is_raspberry_pi'] = False

    except Exception as e:
        os_info['error'] = str(e)

    return os_info

def get_power_info():
    """Get power and throttling information (Raspberry Pi specific)"""
    power_info = {}

    try:
        # Throttling status
        result = subprocess.run(['vcgencmd', 'get_throttled'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            match = re.search(r'throttled=0x([0-9a-fA-F]+)', result.stdout)
            if match:
                throttled_hex = match.group(1)
                throttled_int = int(throttled_hex, 16)
                power_info['throttled'] = throttled_int != 0
                power_info['throttled_hex'] = throttled_hex
    except:
        power_info['throttled'] = None

    try:
        # Voltage
        for component in ['core', 'sdram_c', 'sdram_i', 'sdram_p']:
            result = subprocess.run(['vcgencmd', 'measure_volts', component], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                match = re.search(r'volt=(\d+\.?\d*)V', result.stdout)
                if match:
                    power_info[f'voltage_{component}'] = float(match.group(1))
    except:
        pass

    return power_info if power_info else None

def get_uname_info():
    """Get uname system information"""
    uname_info = {}

    try:
        # Get uname information
        result = subprocess.run(['uname', '-a'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            uname_parts = result.stdout.strip().split()
            if len(uname_parts) >= 6:
                uname_info = {
                    'sysname': uname_parts[0],      # Linux
                    'nodename': uname_parts[1],     # hostname
                    'release': uname_parts[2],      # kernel release
                    'version': uname_parts[3],      # kernel version
                    'machine': uname_parts[4],      # machine hardware name
                    'full': result.stdout.strip()   # full uname output
                }
    except:
        # Fallback to platform module
        try:
            uname_info = {
                'sysname': platform.system(),
                'machine': platform.machine(),
                'release': platform.release(),
                'version': platform.version(),
                'full': f"{platform.system()} {platform.machine()}"
            }
        except:
            pass

    return uname_info if uname_info else None

def main():
    """Gather all system information and output as JSON"""
    system_info = {
        'timestamp': time.time(),
        'os': get_os_info(),
        'uname': get_uname_info(),
        'cpu': get_cpu_usage(),
        'memory': get_memory_usage(),
        'temperature': get_temperature(),
        'gpu': get_gpu_info(),
        'power': get_power_info(),
        'disk': get_disk_usage(),
        'uptime': get_uptime(),
        'load_average': get_load_average(),
        'network_interfaces': get_network_info(),
        'processes': get_processes(),
        'services': get_services(),
        'system_processes': get_system_processes()
    }

    print(json.dumps(system_info, indent=2))

if __name__ == '__main__':
    main()
