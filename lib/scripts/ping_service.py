import subprocess
import sys
import platform
import re
import json

def parse_ping_output(output: str) -> dict:
    """Parse ping command output to extract statistics"""
    stats = {
        'packets_sent': 0,
        'packets_received': 0,
        'packet_loss': 100.0,
        'min_rtt': 0.0,
        'avg_rtt': 0.0,
        'max_rtt': 0.0
    }
    
    # Parse packet statistics with multiple pattern variations
    packet_patterns = [
        r'(\d+) packets transmitted, (\d+) received',  # Linux/Mac
        r'Packets: Sent = (\d+), Received = (\d+)',    # Windows
        r'(\d+) packets transmitted, (\d+) packets received'  # Alternative Linux
    ]
    
    for pattern in packet_patterns:
        packet_stats = re.search(pattern, output)
        if packet_stats:
            stats['packets_sent'] = int(packet_stats.group(1))
            stats['packets_received'] = int(packet_stats.group(2))
            if stats['packets_sent'] > 0:
                stats['packet_loss'] = 100.0 * (stats['packets_sent'] - stats['packets_received']) / stats['packets_sent']
            break
    
    # Parse RTT statistics with multiple pattern variations
    rtt_patterns = [
        r'(\d+\.\d+)/(\d+\.\d+)/(\d+\.\d+)/(\d+\.\d+) ms',  # Linux/Mac
        r'Minimum = (\d+)ms, Maximum = (\d+)ms, Average = (\d+)ms'  # Windows
    ]
    
    for pattern in rtt_patterns:
        rtt_stats = re.search(pattern, output)
        if rtt_stats:
            if platform.system().lower() == 'windows':
                stats['min_rtt'] = float(rtt_stats.group(1))
                stats['max_rtt'] = float(rtt_stats.group(2))
                stats['avg_rtt'] = float(rtt_stats.group(3))
            else:
                stats['min_rtt'] = float(rtt_stats.group(1))
                stats['avg_rtt'] = float(rtt_stats.group(2))
                stats['max_rtt'] = float(rtt_stats.group(3))
            break
    
    return stats

def ping_host(ip_address: str) -> dict:
    try:
        # Determine platform-specific ping command
        ping_cmd = ['ping', '-c', '4', '-W', '5']  # Increased timeout to 5 seconds
        if platform.system().lower() == 'windows':
            ping_cmd = ['ping', '-n', '4', '-w', '5000']  # Windows timeout in milliseconds
            
        result = subprocess.run(
            ping_cmd + [ip_address],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=15  # Increased overall command timeout to 15 seconds
        )
        
        output = result.stdout
        stats = parse_ping_output(output)
        
        # More robust status detection
        if result.returncode == 0:
            status = 'online'
        else:
            # Check if we got any successful pings despite non-zero return code
            if stats['packets_received'] > 0:
                status = 'online'
            else:
                status = 'offline'
        
        return {
            'status': status,
            'output': output,
            'statistics': stats,
            'ip_address': ip_address  # Include IP in response for better tracking
        }
            
    except Exception as e:
        return {
            'status': 'error',
            'output': str(e),
            'statistics': None
        }

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: python ping_service.py <ip_address>")
        sys.exit(1)
        
    ip_address = sys.argv[1]
    result = ping_host(ip_address)
    print(json.dumps(result))
