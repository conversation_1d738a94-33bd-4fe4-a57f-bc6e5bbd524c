import subprocess
import json
import sys
import xml.etree.ElementTree as ET
def run_with_sudo(command: list, password: str) -> subprocess.CompletedProcess:
    """Run a command with sudo privileges"""
    try:
        process = subprocess.Popen(
            ['sudo', '-S'] + command,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        stdout, stderr = process.communicate(input=password + '\n')

        if process.returncode != 0:
            raise subprocess.CalledProcessError(
                process.returncode,
                command,
                stdout,
                stderr
            )
        return subprocess.CompletedProcess(
            command,
            process.returncode,
            stdout,
            stderr
        )
    except Exception as e:
        print(f'Sudo command failed: {str(e)}')
        raise

def get_network_range():
    """Detect the current network range using ip command"""
    try:
        result = subprocess.run(
            ['ip', '-o', '-f', 'inet', 'addr', 'show'],
            capture_output=True,
            text=True
        )
        if result.returncode != 0:
            raise Exception(f'Failed to detect network: {result.stderr}')

        # Parse the output to get the first non-loopback interface
        for line in result.stdout.splitlines():
            if 'scope global' in line:
                parts = line.split()
                ip_with_mask = parts[3]
                return ip_with_mask
        raise Exception('No active network interface found')
    except Exception as e:
        print(f'Error detecting network range: {e}')
        return '***********/24'  # Fallback to default range

def scan_network(password: str):
    """Scan network using nmap with sudo privileges"""
    try:
        # Get the current network range
        network_range = get_network_range()
        print(f'Scanning network range: {network_range}')

        # Run nmap scan with sudo (including OS detection)
        result = run_with_sudo([
            'nmap', '-sn', '-O', '--osscan-guess', '-oX', '-', network_range
        ], password)

        devices = []
        root = ET.fromstring(result.stdout)

        for host in root.findall('host'):
            ip = host.find('address[@addrtype="ipv4"]').get('addr')
            mac_elem = host.find('address[@addrtype="mac"]')
            mac = mac_elem.get('addr') if mac_elem is not None else 'Unknown'
            vendor = mac_elem.get('vendor') if mac_elem is not None else 'Unknown'

            # Try to detect OS
            os_info = 'Unknown'
            os_elem = host.find('os')
            if os_elem is not None:
                osmatch = os_elem.find('osmatch')
                if osmatch is not None:
                    os_info = osmatch.get('name', 'Unknown')
                else:
                    # Fallback to osclass if osmatch not available
                    osclass = os_elem.find('osclass')
                    if osclass is not None:
                        os_family = osclass.get('osfamily', '')
                        os_gen = osclass.get('osgen', '')
                        if os_family:
                            os_info = f"{os_family} {os_gen}".strip()

            devices.append({
                'ip': ip,
                'mac': mac,
                'vendor': vendor,
                'os': os_info
            })
        return devices
    except Exception as e:
        print(f'Error scanning network: {e}')
        return []

def scan_network_simple():
    """Scan network using arp without requiring sudo"""
    try:
        import ipaddress
        from concurrent.futures import ThreadPoolExecutor, as_completed

        # Get network range
        network_range = get_network_range()
        print(f'Scanning network range: {network_range}')

        network = ipaddress.IPv4Network(network_range, strict=False)
        devices = []

        def ping_and_get_info(ip_str):
            try:
                # Ping the host
                result = subprocess.run(['ping', '-c', '1', '-W', '1', ip_str],
                                      capture_output=True, text=True, timeout=2)
                if result.returncode == 0:
                    # Get MAC from ARP table using multiple methods
                    mac = get_mac_address_enhanced(ip_str)

                    # If still unknown, try to force ARP entry
                    if mac == 'Unknown':
                        # Send additional pings to populate ARP table
                        for _ in range(3):
                            subprocess.run(['ping', '-c', '1', '-W', '1', ip_str],
                                         capture_output=True, text=True, timeout=1)
                        mac = get_mac_address_enhanced(ip_str)

                    # Get vendor from MAC
                    vendor = get_vendor_from_mac(mac)

                    # Get hostname
                    hostname = get_hostname(ip_str)

                    # Try to detect OS using simple methods
                    os_info = detect_os_simple(ip_str)

                    return {
                        'ip': ip_str,
                        'mac': mac,
                        'vendor': vendor,
                        'hostname': hostname,
                        'os': os_info
                    }
            except:
                pass
            return None

        # Use threading for faster scanning
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = {executor.submit(ping_and_get_info, str(ip)): ip for ip in network.hosts()}

            for future in as_completed(futures):
                result = future.result()
                if result:
                    devices.append(result)

        return devices
    except Exception as e:
        print(f'Error scanning network: {e}')
        return []

def get_mac_address_enhanced(ip):
    """Enhanced MAC address detection using multiple methods"""
    try:
        # Method 1: Standard arp command
        arp_result = subprocess.run(['arp', '-n', ip], capture_output=True, text=True)
        if arp_result.returncode == 0:
            for line in arp_result.stdout.split('\n'):
                if ip in line and not '(incomplete)' in line:
                    parts = line.split()
                    if len(parts) >= 3:
                        mac = parts[2]
                        # Validate MAC format
                        if ':' in mac and len(mac) == 17:
                            return mac.upper()

        # Method 2: ip neigh command (Linux)
        try:
            neigh_result = subprocess.run(['ip', 'neigh', 'show', ip],
                                        capture_output=True, text=True)
            if neigh_result.returncode == 0:
                for line in neigh_result.stdout.split('\n'):
                    if ip in line and 'lladdr' in line:
                        parts = line.split()
                        lladdr_index = parts.index('lladdr')
                        if lladdr_index + 1 < len(parts):
                            mac = parts[lladdr_index + 1]
                            if ':' in mac and len(mac) == 17:
                                return mac.upper()
        except (subprocess.SubprocessError, ValueError):
            pass

        # Method 3: Parse /proc/net/arp (Linux)
        try:
            with open('/proc/net/arp', 'r') as f:
                for line in f:
                    parts = line.split()
                    if len(parts) >= 4 and parts[0] == ip:
                        mac = parts[3]
                        if ':' in mac and len(mac) == 17 and mac != '00:00:00:00:00:00':
                            return mac.upper()
        except (IOError, IndexError):
            pass

        return 'Unknown'

    except Exception as e:
        print(f'Error getting MAC for {ip}: {e}')
        return 'Unknown'

def get_vendor_from_mac(mac):
    """Get vendor information from MAC address prefix using IEEE OUI database"""
    if mac == 'Unknown' or not mac:
        return 'Unknown'

    try:
        # Extract the OUI (first 3 bytes) from MAC address
        mac_clean = mac.replace(':', '').replace('-', '').upper()
        if len(mac_clean) < 6:
            return 'Unknown'

        oui = mac_clean[:6]

        # Try to get vendor from IEEE OUI database
        vendor = lookup_oui_vendor(oui)
        if vendor != 'Unknown':
            return vendor

        # Fallback to common Raspberry Pi MAC prefixes for older devices
        rpi_prefixes = {
            '28CDC1': 'Raspberry Pi Foundation',
            '3A3541': 'Raspberry Pi Foundation',
            'D83ADD': 'Raspberry Pi Foundation',
            'E45F01': 'Raspberry Pi Foundation',
            '2CCF67': 'Raspberry Pi Foundation',
            'B827EB': 'Raspberry Pi Foundation',
            'DCA632': 'Raspberry Pi Foundation'
        }

        return rpi_prefixes.get(oui, 'Unknown')

    except Exception as e:
        print(f'Error looking up vendor for MAC {mac}: {e}')
        return 'Unknown'

def lookup_oui_vendor(oui):
    """Lookup vendor from IEEE OUI database"""
    try:
        import urllib.request
        import os
        import tempfile
        import time

        # Path to store the OUI database
        oui_file = os.path.join(tempfile.gettempdir(), 'oui.txt')

        # Download OUI database if it doesn't exist or is older than 7 days
        if not os.path.exists(oui_file) or (time.time() - os.path.getmtime(oui_file)) > 7 * 24 * 3600:
            try:
                print('Downloading IEEE OUI database...')
                urllib.request.urlretrieve('http://standards-oui.ieee.org/oui/oui.txt', oui_file)
                print('OUI database downloaded successfully')
            except Exception as e:
                print(f'Failed to download OUI database: {e}')
                if not os.path.exists(oui_file):
                    return 'Unknown'

        # Search for the OUI in the database
        # Format the OUI with dashes for searching (XX-XX-XX)
        oui_formatted = f"{oui[:2]}-{oui[2:4]}-{oui[4:6]}"

        with open(oui_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line in f:
                line = line.strip()
                if line.startswith(oui_formatted):
                    # Extract vendor name from the line
                    # Format: "XX-XX-XX   (hex)		Vendor Name"
                    if '(hex)' in line:
                        # Split on tabs and get the vendor name
                        parts = line.split('\t')
                        if len(parts) >= 3:
                            vendor = parts[2].strip()
                            return vendor
                        else:
                            # Try splitting on multiple spaces
                            hex_pos = line.find('(hex)')
                            if hex_pos != -1:
                                vendor = line[hex_pos + 5:].strip()
                                return vendor

        return 'Unknown'

    except Exception as e:
        print(f'Error looking up OUI {oui}: {e}')
        return 'Unknown'

def get_hostname(ip):
    """Get hostname for an IP address"""
    try:
        import socket
        hostname = socket.gethostbyaddr(ip)[0]
        return hostname
    except:
        return 'Unknown'

def detect_os_simple(ip):
    """Simple OS detection using various methods"""
    try:
        # Try SSH banner grabbing (port 22)
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((ip, 22))
        if result == 0:
            try:
                banner = sock.recv(1024).decode('utf-8', errors='ignore')
                sock.close()

                # Parse SSH banner for OS hints
                if 'Ubuntu' in banner:
                    return 'Ubuntu Linux'
                elif 'Debian' in banner:
                    return 'Debian Linux'
                elif 'Raspbian' in banner:
                    return 'Raspbian Linux'
                elif 'OpenSSH' in banner:
                    return 'Linux'
                elif 'Windows' in banner:
                    return 'Windows'
                else:
                    return 'Linux'  # Default for SSH-enabled devices
            except:
                sock.close()
                return 'Linux'  # Default for SSH-enabled devices
        else:
            sock.close()

        # Try HTTP banner (port 80)
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((ip, 80))
        if result == 0:
            try:
                sock.send(b'HEAD / HTTP/1.0\r\n\r\n')
                response = sock.recv(1024).decode('utf-8', errors='ignore')
                sock.close()

                if 'Server:' in response:
                    if 'nginx' in response.lower():
                        return 'Linux'
                    elif 'apache' in response.lower():
                        return 'Linux'
                    elif 'iis' in response.lower():
                        return 'Windows'
            except:
                sock.close()

        # Default fallback
        return 'Unknown'

    except Exception as e:
        return 'Unknown'

def debug_mac_detection(ip):
    """Debug MAC detection for a specific IP"""
    print(f"Debugging MAC detection for {ip}")

    # Test ping first
    result = subprocess.run(['ping', '-c', '3', '-W', '1', ip],
                          capture_output=True, text=True, timeout=5)
    print(f"Ping result: {result.returncode}")

    # Check ARP table
    arp_result = subprocess.run(['arp', '-a'], capture_output=True, text=True)
    print("ARP table entries:")
    for line in arp_result.stdout.split('\n'):
        if ip in line:
            print(f"  {line}")

    # Check ip neigh
    try:
        neigh_result = subprocess.run(['ip', 'neigh'], capture_output=True, text=True)
        print("IP neighbor entries:")
        for line in neigh_result.stdout.split('\n'):
            if ip in line:
                print(f"  {line}")
    except:
        print("ip neigh command not available")

    # Try enhanced detection
    mac = get_mac_address_enhanced(ip)
    print(f"Enhanced MAC detection result: {mac}")

    if mac != 'Unknown':
        vendor = get_vendor_from_mac(mac)
        print(f"Vendor: {vendor}")

if __name__ == '__main__':
    if len(sys.argv) == 3 and sys.argv[1] == 'debug':
        debug_mac_detection(sys.argv[2])
        sys.exit(0)

    if len(sys.argv) != 2:
        print('Usage: python device_discovery.py <output_file>')
        print('       python device_discovery.py debug <ip_address>')
        sys.exit(1)

    output_file = sys.argv[1]
    try:
        devices = scan_network_simple()
        with open(output_file, 'w') as f:
            json.dump(devices, f)
    except Exception as e:
        print(f'Error: {str(e)}')
        sys.exit(1)
