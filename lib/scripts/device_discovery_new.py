#!/usr/bin/env python3
"""
Network device discovery script that works without sudo
Uses ping and arp to discover devices on the local network
"""

import subprocess
import json
import sys
import socket
import ipaddress
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

def get_network_range():
    """Get the current network range"""
    try:
        # Get default route
        result = subprocess.run(['ip', 'route', 'show', 'default'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            # Extract interface from default route
            for line in result.stdout.split('\n'):
                if 'default via' in line:
                    parts = line.split()
                    if 'dev' in parts:
                        interface = parts[parts.index('dev') + 1]
                        break
            else:
                interface = 'eth0'  # fallback
        else:
            interface = 'eth0'  # fallback

        # Get IP and netmask for the interface
        result = subprocess.run(['ip', 'addr', 'show', interface],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'inet ' in line and not '127.0.0.1' in line:
                    # Extract IP/CIDR
                    match = re.search(r'inet\s+([0-9.]+/[0-9]+)', line)
                    if match:
                        return match.group(1)

        # Fallback: try to get from hostname -I
        result = subprocess.run(['hostname', '-I'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            ip = result.stdout.strip().split()[0]
            # Assume /24 network
            ip_parts = ip.split('.')
            network = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.0/24"
            return network

    except Exception as e:
        print(f'Error detecting network range: {e}')

    return '***********/24'  # Final fallback

def get_hostname(ip):
    """Try to resolve hostname for an IP address using multiple methods"""
    hostname = None

    # Method 1: Try reverse DNS lookup
    try:
        hostname = socket.gethostbyaddr(ip)[0]
        # Return just the hostname part (before the first dot)
        hostname = hostname.split('.')[0]
        if hostname and hostname != ip:
            return hostname
    except (socket.herror, socket.gaierror):
        pass

    # Method 2: Try nslookup
    try:
        result = subprocess.run(['nslookup', ip], capture_output=True, text=True, timeout=2)
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'name =' in line:
                    hostname = line.split('name =')[1].strip().rstrip('.')
                    hostname = hostname.split('.')[0]
                    if hostname and hostname != ip:
                        return hostname
    except:
        pass

    # Method 3: Try host command
    try:
        result = subprocess.run(['host', ip], capture_output=True, text=True, timeout=2)
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if 'domain name pointer' in line:
                    hostname = line.split('domain name pointer')[1].strip().rstrip('.')
                    hostname = hostname.split('.')[0]
                    if hostname and hostname != ip:
                        return hostname
    except:
        pass

    # Method 4: Try NetBIOS name resolution (for Windows devices)
    try:
        result = subprocess.run(['nmblookup', '-A', ip], capture_output=True, text=True, timeout=2)
        if result.returncode == 0:
            for line in result.stdout.split('\n'):
                if '<00>' in line and 'GROUP' not in line:
                    hostname = line.split()[0].strip()
                    if hostname and hostname != ip and not hostname.startswith('Looking'):
                        return hostname
    except:
        pass

    return None

def get_vendor_from_mac(mac):
    """Get vendor information from MAC address prefix"""
    if mac == 'Unknown' or mac == '(incomplete)' or mac == '--':
        return 'Unknown'

    # Comprehensive MAC vendor database
    mac_vendors = {
        # Apple devices (common prefixes)
        '00:03:93': 'Apple',
        '00:05:02': 'Apple',
        '00:0A:27': 'Apple',
        '00:0A:95': 'Apple',
        '00:0D:93': 'Apple',
        '00:11:24': 'Apple',
        '00:14:51': 'Apple',
        '00:16:CB': 'Apple',
        '00:17:F2': 'Apple',
        '00:19:E3': 'Apple',
        '00:1B:63': 'Apple',
        '00:1C:B3': 'Apple',
        '00:1E:C2': 'Apple',
        '00:1F:F3': 'Apple',
        '00:21:E9': 'Apple',
        '00:22:41': 'Apple',
        '00:23:12': 'Apple',
        '00:23:DF': 'Apple',
        '00:24:36': 'Apple',
        '00:25:00': 'Apple',
        '00:25:4B': 'Apple',
        '00:25:BC': 'Apple',
        '00:26:08': 'Apple',
        '00:26:4A': 'Apple',
        '00:26:B0': 'Apple',
        '00:26:BB': 'Apple',
        '04:0C:CE': 'Apple',
        '04:15:52': 'Apple',
        '04:1E:64': 'Apple',
        '04:26:65': 'Apple',
        '04:48:9A': 'Apple',
        '04:4F:AA': 'Apple',
        '04:54:53': 'Apple',
        '04:69:F8': 'Apple',
        '04:DB:56': 'Apple',
        '04:E5:36': 'Apple',
        '04:F1:3E': 'Apple',
        '04:F7:E4': 'Apple',
        '08:00:07': 'Apple',
        '08:74:02': 'Apple',
        '0C:3E:9F': 'Apple',
        '0C:4D:E9': 'Apple',
        '0C:74:C2': 'Apple',
        '10:40:F3': 'Apple',
        '10:9A:DD': 'Apple',
        '10:DD:B1': 'Apple',
        '14:10:9F': 'Apple',
        '14:20:5E': 'Apple',
        '14:7D:DA': 'Apple',
        '14:BD:61': 'Apple',
        '18:34:51': 'Apple',
        '18:65:90': 'Apple',
        '18:AF:61': 'Apple',
        '1C:1A:C0': 'Apple',
        '1C:36:BB': 'Apple',
        '1C:AB:A7': 'Apple',
        '20:78:F0': 'Apple',
        '20:A2:E4': 'Apple',
        '24:A0:74': 'Apple',
        '24:AB:81': 'Apple',
        '28:37:37': 'Apple',
        '28:6A:BA': 'Apple',
        '28:E0:2C': 'Apple',
        '28:E7:CF': 'Apple',
        '2C:1F:23': 'Apple',
        '2C:B4:3A': 'Apple',
        '30:90:AB': 'Apple',
        '34:15:9E': 'Apple',
        '34:36:3B': 'Apple',
        '34:A3:95': 'Apple',
        '38:C9:86': 'Apple',
        '3C:15:C2': 'Apple',
        '3C:2E:F9': 'Apple',
        '40:33:1A': 'Apple',
        '40:B3:95': 'Apple',
        '44:00:10': 'Apple',
        '44:4C:0C': 'Apple',
        '48:43:7C': 'Apple',
        '48:74:6E': 'Apple',
        '4C:32:75': 'Apple',
        '4C:7C:5F': 'Apple',
        '4C:8D:79': 'Apple',
        '4C:A9:19': 'Apple',  # Found in scan results
        '50:EA:D6': 'Apple',
        '54:26:96': 'Apple',
        '54:72:4F': 'Apple',
        '58:55:CA': 'Apple',
        '5C:59:48': 'Apple',
        '5C:95:AE': 'Apple',
        '5C:F9:38': 'Apple',
        '60:03:08': 'Apple',
        '60:33:4B': 'Apple',
        '60:C5:47': 'Apple',
        '60:F4:45': 'Apple',
        '64:20:9F': 'Apple',
        '64:76:BA': 'Apple',
        '64:A3:CB': 'Apple',
        '64:B9:E8': 'Apple',
        '68:96:7B': 'Apple',
        '68:AB:1E': 'Apple',
        '6C:40:08': 'Apple',
        '6C:72:E7': 'Apple',
        '6C:94:66': 'Apple',
        '70:11:24': 'Apple',
        '70:48:0F': 'Apple',
        '70:56:81': 'Apple',
        '70:73:CB': 'Apple',
        '70:DE:E2': 'Apple',
        '74:E2:F5': 'Apple',
        '78:31:C1': 'Apple',
        '78:4F:43': 'Apple',
        '78:7B:8A': 'Apple',
        '78:CA:39': 'Apple',
        '7C:6D:62': 'Apple',
        '7C:C3:A1': 'Apple',
        '7C:D1:C3': 'Apple',
        '80:92:9F': 'Apple',
        '80:E6:50': 'Apple',
        '84:38:35': 'Apple',
        '84:FC:FE': 'Apple',
        '88:63:DF': 'Apple',
        '8C:58:77': 'Apple',
        '8C:7C:92': 'Apple',
        '90:72:40': 'Apple',
        '90:98:77': 'Apple',  # Found in scan results
        '90:B0:ED': 'Apple',
        '94:E9:6A': 'Apple',
        '98:03:D8': 'Apple',
        '9C:04:EB': 'Apple',
        '9C:20:7B': 'Apple',
        '9C:84:BF': 'Apple',
        'A0:99:9B': 'Apple',
        'A0:D7:95': 'Apple',
        'A4:5E:60': 'Apple',
        'A4:B1:97': 'Apple',
        'A8:20:66': 'Apple',
        'A8:51:AB': 'Apple',
        'A8:86:DD': 'Apple',
        'A8:96:8A': 'Apple',
        'AC:29:3A': 'Apple',
        'AC:3C:0B': 'Apple',
        'AC:87:A3': 'Apple',
        'B0:65:BD': 'Apple',
        'B4:18:D1': 'Apple',
        'B4:F0:AB': 'Apple',
        'B8:09:8A': 'Apple',
        'B8:17:C2': 'Apple',
        'B8:53:AC': 'Apple',
        'B8:C7:5D': 'Apple',
        'B8:E8:56': 'Apple',
        'B8:F6:B1': 'Apple',
        'BC:52:B7': 'Apple',
        'BC:67:1C': 'Apple',
        'BC:92:6B': 'Apple',
        'C0:84:7A': 'Apple',
        'C0:8D:51': 'Apple',  # Found in scan results
        'C4:2C:03': 'Apple',
        'C8:2A:14': 'Apple',
        'C8:33:4B': 'Apple',
        'C8:B5:B7': 'Apple',
        'CC:08:8D': 'Apple',
        'CC:25:EF': 'Apple',
        'D0:23:DB': 'Apple',
        'D0:81:7A': 'Apple',
        'D4:9A:20': 'Apple',
        'D8:30:62': 'Apple',
        'D8:96:95': 'Apple',
        'D8:A2:5E': 'Apple',
        'DC:2B:2A': 'Apple',
        'DC:37:45': 'Apple',
        'DC:56:E7': 'Apple',
        'DC:86:D8': 'Apple',
        'DC:9B:9C': 'Apple',
        'E0:AC:CB': 'Apple',
        'E0:B9:BA': 'Apple',
        'E0:F8:47': 'Apple',
        'E4:25:E7': 'Apple',
        'E4:8B:7F': 'Apple',
        'E4:C6:3D': 'Apple',
        'E8:06:88': 'Apple',
        'E8:80:2E': 'Apple',
        'EC:35:86': 'Apple',
        'F0:18:98': 'Apple',
        'F0:B4:79': 'Apple',
        'F0:D1:A9': 'Apple',
        'F4:0F:24': 'Apple',
        'F4:37:B7': 'Apple',
        'F4:5C:89': 'Apple',
        'F8:16:54': 'Apple',
        'F8:1E:DF': 'Apple',
        'F8:22:29': 'Apple',  # Found in scan results (router)
        'F8:27:93': 'Apple',
        'F8:2F:A8': 'Apple',
        'FC:25:3F': 'Apple',
        'FC:E9:98': 'Apple',

        # Samsung devices
        '00:12:FB': 'Samsung',
        '00:15:99': 'Samsung',
        '00:16:32': 'Samsung',
        '00:17:C9': 'Samsung',
        '00:1A:8A': 'Samsung',
        '00:1B:98': 'Samsung',
        '00:1D:25': 'Samsung',
        '00:1E:7D': 'Samsung',
        '00:21:19': 'Samsung',
        '00:23:39': 'Samsung',
        '00:24:54': 'Samsung',
        '00:26:37': 'Samsung',
        '08:08:C2': 'Samsung',
        '08:37:3D': 'Samsung',
        '08:EC:A9': 'Samsung',
        '0C:14:20': 'Samsung',
        '0C:89:10': 'Samsung',
        '10:1D:C0': 'Samsung',
        '14:7F:D1': 'Samsung',
        '18:3A:2D': 'Samsung',
        '18:3D:A2': 'Samsung',
        '1C:5A:3E': 'Samsung',
        '20:64:32': 'Samsung',
        '20:6E:9C': 'Samsung',
        '24:4B:81': 'Samsung',
        '28:39:26': 'Samsung',
        '28:BA:B5': 'Samsung',
        '2C:44:01': 'Samsung',
        '30:07:4D': 'Samsung',
        '30:19:66': 'Samsung',
        '34:23:87': 'Samsung',
        '34:BE:00': 'Samsung',
        '38:AA:3C': 'Samsung',
        '3C:5A:B4': 'Samsung',
        '40:0E:85': 'Samsung',
        '40:4E:36': 'Samsung',
        '44:5E:F3': 'Samsung',
        '44:78:3E': 'Samsung',
        '48:5A:3F': 'Samsung',
        '4C:3C:16': 'Samsung',
        '50:32:37': 'Samsung',
        '50:CC:F8': 'Samsung',
        '54:88:0E': 'Samsung',
        '58:21:EF': 'Samsung',
        '5C:0A:5B': 'Samsung',
        '60:6B:BD': 'Samsung',
        '64:B3:10': 'Samsung',
        '68:EB:C5': 'Samsung',
        '6C:2F:2C': 'Samsung',
        '6C:F3:73': 'Samsung',
        '70:F9:27': 'Samsung',
        '74:45:8A': 'Samsung',
        '78:1F:DB': 'Samsung',
        '78:25:AD': 'Samsung',
        '78:59:5E': 'Samsung',
        '7C:61:66': 'Samsung',
        '7C:A1:AE': 'Samsung',
        '80:57:19': 'Samsung',
        '84:38:38': 'Samsung',
        '84:A4:66': 'Samsung',
        '88:32:9B': 'Samsung',
        '8C:77:12': 'Samsung',
        '90:18:7C': 'Samsung',
        '94:35:0A': 'Samsung',
        '98:52:3D': 'Samsung',
        '9C:02:98': 'Samsung',
        'A0:0B:BA': 'Samsung',
        'A0:21:B7': 'Samsung',
        'A4:EB:D3': 'Samsung',
        'A8:F2:74': 'Samsung',
        'AC:5F:3E': 'Samsung',
        'B4:62:93': 'Samsung',
        'B8:5E:7B': 'Samsung',
        'BC:14:85': 'Samsung',
        'BC:20:A4': 'Samsung',
        'BC:72:B1': 'Samsung',
        'C0:BD:D1': 'Samsung',
        'C4:57:6E': 'Samsung',
        'C8:19:F7': 'Samsung',
        'CC:07:AB': 'Samsung',
        'CC:3A:61': 'Samsung',
        'D0:17:6A': 'Samsung',
        'D0:22:BE': 'Samsung',
        'D4:87:D8': 'Samsung',
        'D4:E8:B2': 'Samsung',
        'D8:90:E8': 'Samsung',
        'DC:71:96': 'Samsung',
        'E0:91:F5': 'Samsung',
        'E4:40:E2': 'Samsung',
        'E8:50:8B': 'Samsung',
        'EC:1F:72': 'Samsung',
        'EC:9B:F3': 'Samsung',
        'F0:25:B7': 'Samsung',
        'F4:09:D8': 'Samsung',
        'F8:04:2E': 'Samsung',
        'F8:A9:D0': 'Samsung',
        'FC:A6:21': 'Samsung',

        # Google devices
        '00:1A:11': 'Google',
        '04:F0:21': 'Google',
        '18:B4:30': 'Google',
        '20:DF:B9': 'Google',
        '30:8C:FB': 'Google',
        '3C:5A:B4': 'Google',
        '40:4E:36': 'Google',
        '44:07:0B': 'Google',
        '48:60:5F': 'Google',
        '4C:0B:BE': 'Google',
        '50:8F:4C': 'Google',
        '54:60:09': 'Google',
        '6C:AD:F8': 'Google',
        '74:E5:43': 'Google',
        '7C:2E:BD': 'Google',
        '84:1B:5E': 'Google',
        '90:E7:C4': 'Google',
        'A0:02:DC': 'Google',
        'AC:37:43': 'Google',
        'B4:F0:AB': 'Google',
        'CC:3A:61': 'Google',
        'D8:80:39': 'Google',
        'F4:F5:D8': 'Google',
        'F8:8F:CA': 'Google',

        # Raspberry Pi Foundation
        '28:CD:C1': 'Raspberry Pi Foundation',
        '3A:35:41': 'Raspberry Pi Foundation',
        'D8:3A:DD': 'Raspberry Pi Foundation',
        'E4:5F:01': 'Raspberry Pi Foundation',
        '2C:CF:67': 'Raspberry Pi Foundation',
        'B8:27:EB': 'Raspberry Pi Foundation',
        'DC:A6:32': 'Raspberry Pi Foundation',

        # Amazon devices
        '00:FC:8B': 'Amazon',
        '04:A8:2A': 'Amazon',
        '0C:47:C9': 'Amazon',
        '18:74:2E': 'Amazon',
        '24:FD:52': 'Amazon',
        '34:D2:70': 'Amazon',
        '38:F7:3D': 'Amazon',
        '44:65:0D': 'Amazon',
        '50:DC:E7': 'Amazon',
        '68:37:E9': 'Amazon',
        '6C:56:97': 'Amazon',
        '74:75:48': 'Amazon',
        '78:E1:03': 'Amazon',
        '84:D6:D0': 'Amazon',
        '8C:41:F2': 'Amazon',
        'AC:63:BE': 'Amazon',
        'B0:7B:25': 'Amazon',
        'CC:9E:A2': 'Amazon',
        'F0:27:2D': 'Amazon',
        'FC:65:DE': 'Amazon',

        # Microsoft devices
        '00:50:F2': 'Microsoft',
        '00:15:5D': 'Microsoft',
        '7C:1E:52': 'Microsoft',
        '00:0D:3A': 'Microsoft',
        '00:12:5A': 'Microsoft',
        '00:17:FA': 'Microsoft',
        '00:21:D8': 'Microsoft',
        '00:24:D7': 'Microsoft',
        '00:50:F2': 'Microsoft',
        '28:18:78': 'Microsoft',
        '30:59:B7': 'Microsoft',
        '34:2E:B7': 'Microsoft',
        '40:5B:D8': 'Microsoft',
        '60:45:BD': 'Microsoft',
        '64:00:6A': 'Microsoft',
        '7C:1E:52': 'Microsoft',
        '90:59:AF': 'Microsoft',
        'A0:CE:C8': 'Microsoft',
        'E0:CB:4E': 'Microsoft',

        # LG devices
        '00:1C:62': 'LG Electronics',
        '00:1E:75': 'LG Electronics',
        '00:22:A9': 'LG Electronics',
        '10:F9:6F': 'LG Electronics',
        '20:21:A5': 'LG Electronics',
        '34:FC:EF': 'LG Electronics',
        '3C:BD:D8': 'LG Electronics',
        '40:B8:9A': 'LG Electronics',
        '60:D0:A9': 'LG Electronics',
        '68:B5:99': 'LG Electronics',
        '6C:D6:8A': 'LG Electronics',
        '78:5D:C8': 'LG Electronics',
        '7C:1C:4E': 'LG Electronics',
        '84:C9:B2': 'LG Electronics',
        '88:C9:D0': 'LG Electronics',
        'A0:07:98': 'LG Electronics',
        'A8:16:B2': 'LG Electronics',
        'B8:BB:AF': 'LG Electronics',
        'C0:14:FE': 'LG Electronics',
        'E8:5B:5B': 'LG Electronics',
        'F8:A9:D0': 'LG Electronics',

        # Sony devices
        '00:02:D1': 'Sony',
        '00:04:1F': 'Sony',
        '00:0A:D9': 'Sony',
        '00:13:A9': 'Sony',
        '00:16:FE': 'Sony',
        '00:19:C1': 'Sony',
        '00:1B:FB': 'Sony',
        '00:1D:BA': 'Sony',
        '00:1F:E4': 'Sony',
        '00:22:CF': 'Sony',
        '00:24:BE': 'Sony',
        '04:4B:ED': 'Sony',
        '08:00:46': 'Sony',
        '0C:A6:94': 'Sony',
        '10:4F:A8': 'Sony',
        '14:AB:C5': 'Sony',
        '18:F0:E4': 'Sony',
        '1C:99:4C': 'Sony',
        '20:02:AF': 'Sony',
        '24:0A:64': 'Sony',
        '28:3B:82': 'Sony',
        '2C:1F:23': 'Sony',
        '30:F7:72': 'Sony',
        '34:13:E8': 'Sony',
        '38:C9:86': 'Sony',
        '3C:07:54': 'Sony',
        '40:4D:8E': 'Sony',
        '44:87:FC': 'Sony',
        '48:D6:D5': 'Sony',
        '4C:0F:6E': 'Sony',
        '50:56:BF': 'Sony',
        '54:84:1B': 'Sony',
        '58:BD:A3': 'Sony',
        '5C:96:9D': 'Sony',
        '60:38:E0': 'Sony',
        '64:D4:DA': 'Sony',
        '68:EB:AE': 'Sony',
        '6C:5C:E5': 'Sony',
        '70:1C:E7': 'Sony',
        '74:90:50': 'Sony',
        '78:CA:04': 'Sony',
        '7C:BB:8A': 'Sony',
        '80:C5:F2': 'Sony',
        '84:17:66': 'Sony',
        '88:75:56': 'Sony',
        '8C:64:22': 'Sony',
        '90:AB:7C': 'Sony',
        '94:53:30': 'Sony',
        '98:F1:70': 'Sony',
        '9C:3A:AF': 'Sony',
        'A0:1D:48': 'Sony',
        'A4:77:33': 'Sony',
        'A8:06:00': 'Sony',
        'AC:9B:0A': 'Sony',
        'B0:38:29': 'Sony',
        'B4:CE:F6': 'Sony',
        'B8:78:2E': 'Sony',
        'BC:60:A7': 'Sony',
        'C0:CB:38': 'Sony',
        'C4:04:15': 'Sony',
        'C8:AA:21': 'Sony',
        'CC:B8:A8': 'Sony',
        'D0:E7:82': 'Sony',
        'D4:4B:5E': 'Sony',
        'D8:D4:3C': 'Sony',
        'DC:0B:1A': 'Sony',
        'E0:75:7D': 'Sony',
        'E4:22:A5': 'Sony',
        'E8:65:D4': 'Sony',
        'EC:22:80': 'Sony',
        'F0:7D:68': 'Sony',
        'F4:81:39': 'Sony',
        'F8:46:1C': 'Sony',
        'FC:0F:E6': 'Sony',
    }

    # Normalize MAC address format (remove colons and convert to uppercase)
    clean_mac = mac.replace(':', '').upper()

    # Try 6-character prefix first (more specific) - first 3 bytes
    mac_prefix_6 = clean_mac[:6]
    mac_prefix_6_formatted = f"{mac_prefix_6[:2]}:{mac_prefix_6[2:4]}:{mac_prefix_6[4:6]}"

    if mac_prefix_6_formatted in mac_vendors:
        return mac_vendors[mac_prefix_6_formatted]

    # Try 3-character prefix (less specific) - first 1.5 bytes
    mac_prefix_3 = clean_mac[:3]
    mac_prefix_3_formatted = f"{mac_prefix_3[:2]}:{mac_prefix_3[2:]}"
    if mac_prefix_3_formatted in mac_vendors:
        return mac_vendors[mac_prefix_3_formatted]

    return 'Unknown'

def ping_and_get_info(ip_str):
    """Ping a single IP and get its info"""
    try:
        # Ping the host
        result = subprocess.run(['ping', '-c', '1', '-W', '1', ip_str],
                              capture_output=True, text=True, timeout=3)
        if result.returncode == 0:
            # Get MAC from ARP table
            arp_result = subprocess.run(['arp', '-n', ip_str],
                                      capture_output=True, text=True, timeout=2)
            mac = 'Unknown'
            if arp_result.returncode == 0:
                for line in arp_result.stdout.split('\n'):
                    if ip_str in line and 'incomplete' not in line:
                        parts = line.split()
                        if len(parts) >= 3:
                            mac = parts[2]
                            break

            # Get vendor from MAC
            vendor = get_vendor_from_mac(mac)

            # Get hostname
            hostname = get_hostname(ip_str)

            return {
                'ip': ip_str,
                'mac': mac,
                'vendor': vendor,
                'hostname': hostname
            }
    except Exception as e:
        print(f'Error checking {ip_str}: {e}')
    return None

def scan_network():
    """Scan network using ping and arp without requiring sudo"""
    try:
        # Get network range
        network_range = get_network_range()
        print(f'Scanning network range: {network_range}')

        network = ipaddress.IPv4Network(network_range, strict=False)
        devices = []

        # Use threading for faster scanning
        with ThreadPoolExecutor(max_workers=50) as executor:
            futures = {executor.submit(ping_and_get_info, str(ip)): ip for ip in network.hosts()}

            for future in as_completed(futures):
                result = future.result()
                if result:
                    devices.append(result)
                    print(f'Found device: {result["ip"]} - {result.get("hostname", "Unknown")}')

        print(f'Scan complete. Found {len(devices)} devices.')
        return devices
    except Exception as e:
        print(f'Error scanning network: {e}')
        return []

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print('Usage: python device_discovery_new.py <output_file>')
        sys.exit(1)

    output_file = sys.argv[1]
    try:
        devices = scan_network()
        with open(output_file, 'w') as f:
            json.dump(devices, f, indent=2)
        print(f'Results written to {output_file}')
    except Exception as e:
        print(f'Error: {str(e)}')
        sys.exit(1)
