import subprocess
import re
import json
from typing import List, Dict

def scan_network() -> List[Dict[str, str]]:
    """Scan the local network for devices using arp-scan"""
    try:
        result = subprocess.run(
            ['arp-scan', '--localnet'],
            capture_output=True,
            text=True,
            check=True
        )
        
        devices = []
        for line in result.stdout.splitlines():
            match = re.match(r'^(\d+\.\d+\.\d+\.\d+)\s+([0-9A-Fa-f:]{17})\s+(.*)$', line)
            if match:
                ip, mac, vendor = match.groups()
                devices.append({
                    'ip': ip,
                    'mac': mac,
                    'vendor': vendor
                })
                
        return devices
    except subprocess.CalledProcessError as e:
        print(f"Scan failed: {e.stderr}")
        return []

if __name__ == "__main__":
    devices = scan_network()
    print(json.dumps(devices))
