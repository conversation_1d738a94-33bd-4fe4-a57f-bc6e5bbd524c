import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/app_state.dart';
import '../widgets/device_card.dart';
import '../widgets/add_device_dialog.dart';

class DevicesScreen extends StatefulWidget {
  const DevicesScreen({super.key});

  @override
  State<DevicesScreen> createState() => _DevicesScreenState();
}

class _DevicesScreenState extends State<DevicesScreen> {
  String _searchQuery = '';
  DeviceStatus? _statusFilter;

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final filteredDevices = _filterDevices(appState.devices);
        final connectedDevices =
            appState.devices.where((d) => d.isConnected).toList();
        final selectedDevice = appState.selectedDevice;

        return Scaffold(
          appBar: AppBar(
            title: const Text('My Devices'),
            actions: [
              // Refresh button
              IconButton(
                onPressed: appState.refreshAllDevices,
                icon: const Icon(Icons.refresh),
                tooltip: 'Refresh all devices',
              ),
              // Add device button
              IconButton(
                onPressed: () => _showAddDeviceDialog(context),
                icon: const Icon(Icons.add),
                tooltip: 'Add device',
              ),
            ],
          ),
          body: Column(
            children: [
              // Active Device Section
              if (connectedDevices.isNotEmpty) ...[
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.settings_remote,
                            color: Theme.of(context).colorScheme.primary,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Active Device',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      if (selectedDevice != null &&
                          selectedDevice.isConnected) ...[
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    selectedDevice.name,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(
                                          fontWeight: FontWeight.w600,
                                        ),
                                  ),
                                  Text(
                                    selectedDevice.ip,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurfaceVariant,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            if (connectedDevices.length > 1) ...[
                              const SizedBox(width: 16),
                              DropdownButton<String>(
                                value: selectedDevice.id,
                                underline: Container(),
                                icon: Icon(
                                  Icons.keyboard_arrow_down,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                items: connectedDevices
                                    .map((device) => DropdownMenuItem(
                                          value: device.id,
                                          child: Text(
                                            device.name,
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium,
                                          ),
                                        ))
                                    .toList(),
                                onChanged: (deviceId) {
                                  if (deviceId != null) {
                                    appState.selectDevice(deviceId);
                                  }
                                },
                              ),
                            ],
                          ],
                        ),
                      ] else ...[
                        Text(
                          'No device selected',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant,
                                  ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () {
                            if (connectedDevices.isNotEmpty) {
                              appState.autoSelectBestDevice();
                            }
                          },
                          icon: const Icon(Icons.auto_fix_high, size: 16),
                          label: const Text('Auto-select'),
                        ),
                      ],
                    ],
                  ),
                ),
              ],

              // Search and Filter Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Search Bar
                    TextField(
                      decoration: InputDecoration(
                        hintText: 'Search devices...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value.toLowerCase();
                        });
                      },
                    ),
                    const SizedBox(height: 12),

                    // Filter Chips
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          FilterChip(
                            label: const Text('All'),
                            selected: _statusFilter == null,
                            onSelected: (selected) {
                              setState(() {
                                _statusFilter = selected ? null : _statusFilter;
                              });
                            },
                          ),
                          const SizedBox(width: 8),
                          ...DeviceStatus.values.map((status) => Padding(
                                padding: const EdgeInsets.only(right: 8),
                                child: FilterChip(
                                  label: Text(_getStatusLabel(status)),
                                  selected: _statusFilter == status,
                                  onSelected: (selected) {
                                    setState(() {
                                      _statusFilter = selected ? status : null;
                                    });
                                  },
                                ),
                              )),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Device List
              Expanded(
                child: filteredDevices.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: filteredDevices.length,
                        itemBuilder: (context, index) {
                          final device = filteredDevices[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: DeviceCard(
                              device: device,
                              isSelected:
                                  appState.selectedDeviceId == device.id,
                              onTap: () => appState.selectDevice(device.id),
                              onEdit: () =>
                                  _showEditDeviceDialog(context, device),
                              onDelete: () =>
                                  _showDeleteConfirmation(context, device),
                              onConnect: () =>
                                  _connectToDevice(context, device),
                              onDisconnect: () =>
                                  _disconnectFromDevice(context, device),
                              onRefresh: () =>
                                  appState.refreshDeviceStatus(device.id),
                              onSetDefault: () =>
                                  _setDefaultDevice(context, device),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        );
      },
    );
  }

  List<Device> _filterDevices(List<Device> devices) {
    return devices.where((device) {
      // Search filter
      final matchesSearch = _searchQuery.isEmpty ||
          device.name.toLowerCase().contains(_searchQuery) ||
          device.ip.contains(_searchQuery) ||
          device.vendor.toLowerCase().contains(_searchQuery);

      // Status filter
      final matchesStatus =
          _statusFilter == null || device.status == _statusFilter;

      return matchesSearch && matchesStatus;
    }).toList();
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.devices_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No devices found',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Add devices manually or discover them on your network',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showAddDeviceDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Add Device'),
          ),
        ],
      ),
    );
  }

  String _getStatusLabel(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.online:
        return 'Online';
      case DeviceStatus.offline:
        return 'Offline';
      case DeviceStatus.connected:
        return 'Connected';
      case DeviceStatus.connecting:
        return 'Connecting';
      case DeviceStatus.disconnecting:
        return 'Disconnecting';
      case DeviceStatus.rebooting:
        return 'Rebooting';
      case DeviceStatus.shuttingDown:
        return 'Shutting Down';
      case DeviceStatus.error:
        return 'Error';
      case DeviceStatus.unknown:
        return 'Unknown';
    }
  }

  void _showAddDeviceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddDeviceDialog(),
    );
  }

  void _showEditDeviceDialog(BuildContext context, Device device) {
    showDialog(
      context: context,
      builder: (context) => AddDeviceDialog(device: device),
    );
  }

  void _showDeleteConfirmation(BuildContext context, Device device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Device'),
        content: Text('Are you sure you want to delete "${device.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              context.read<AppState>().removeDevice(device.id);
              Navigator.of(context).pop();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _connectToDevice(BuildContext context, Device device) {
    final usernameController = TextEditingController(text: 'pi');
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Connect to ${device.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: usernameController,
              decoration: const InputDecoration(
                labelText: 'Username',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await context.read<AppState>().connectSSH(
                      device.id,
                      usernameController.text,
                      passwordController.text,
                    );
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Connected to ${device.name}')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Failed to connect: $e')),
                  );
                }
              }
            },
            child: const Text('Connect'),
          ),
        ],
      ),
    );
  }

  void _disconnectFromDevice(BuildContext context, Device device) async {
    try {
      await context.read<AppState>().disconnectSSH(device.id);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Disconnected from ${device.name}')),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to disconnect: $e')),
        );
      }
    }
  }

  void _setDefaultDevice(BuildContext context, Device device) async {
    try {
      await context.read<AppState>().setDefaultDevice(device.id);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${device.name} set as default device')),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to set default device: $e')),
        );
      }
    }
  }
}
