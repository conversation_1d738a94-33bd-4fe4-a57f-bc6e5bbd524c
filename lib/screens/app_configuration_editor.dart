import 'package:flutter/material.dart';
import '../models/app_module.dart';
import '../services/app_editor_service.dart';

class AppConfigurationEditor extends StatefulWidget {
  final AppModule module;

  const AppConfigurationEditor({super.key, required this.module});

  @override
  State<AppConfigurationEditor> createState() => _AppConfigurationEditorState();
}

class _AppConfigurationEditorState extends State<AppConfigurationEditor>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _appJsonController = TextEditingController();
  final _configJsonController = TextEditingController();
  final _editorService = AppEditorService();
  bool _isLoading = true;
  bool _hasAppJsonChanges = false;
  bool _hasConfigJsonChanges = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadConfiguration();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _appJsonController.dispose();
    _configJsonController.dispose();
    super.dispose();
  }

  Future<void> _loadConfiguration() async {
    try {
      final appJsonContent =
          await _editorService.getAppConfiguration(widget.module);
      final configJsonContent =
          await _editorService.getAppConfigJson(widget.module);

      _appJsonController.text = appJsonContent;
      _configJsonController.text = configJsonContent;

      _appJsonController.addListener(() {
        if (!_hasAppJsonChanges) {
          setState(() {
            _hasAppJsonChanges = true;
          });
        }
      });

      _configJsonController.addListener(() {
        if (!_hasConfigJsonChanges) {
          setState(() {
            _hasConfigJsonChanges = true;
          });
        }
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading configuration: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveConfiguration() async {
    setState(() {
      _isSaving = true;
    });

    try {
      if (_hasAppJsonChanges) {
        await _editorService.saveAppConfiguration(
            widget.module, _appJsonController.text);
      }
      if (_hasConfigJsonChanges) {
        await _editorService.saveAppConfigJson(
            widget.module, _configJsonController.text);
      }

      setState(() {
        _hasAppJsonChanges = false;
        _hasConfigJsonChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configuration saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  bool get _hasChanges => _hasAppJsonChanges || _hasConfigJsonChanges;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Configure ${widget.module.title}'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          if (_hasChanges)
            IconButton(
              onPressed: _isSaving ? null : _saveConfiguration,
              icon: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              tooltip: 'Save',
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.settings, size: 16),
                  const SizedBox(width: 8),
                  const Text('app.json'),
                  if (_hasAppJsonChanges) ...[
                    const SizedBox(width: 4),
                    Container(
                      width: 6,
                      height: 6,
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.tune, size: 16),
                  const SizedBox(width: 8),
                  const Text('config.json'),
                  if (_hasConfigJsonChanges) ...[
                    const SizedBox(width: 4),
                    Container(
                      width: 6,
                      height: 6,
                      decoration: const BoxDecoration(
                        color: Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildJsonEditor(
                  controller: _appJsonController,
                  title: 'App Configuration (app.json)',
                  description: 'Main app metadata and configuration',
                ),
                _buildJsonEditor(
                  controller: _configJsonController,
                  title: 'App Settings (config.json)',
                  description: 'Runtime settings and preferences',
                ),
              ],
            ),
      bottomNavigationBar: _hasChanges
          ? Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber,
                    size: 16,
                    color: Colors.orange[700],
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'You have unsaved changes',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const Spacer(),
                  FilledButton.icon(
                    onPressed: _isSaving ? null : _saveConfiguration,
                    icon: _isSaving
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Icon(Icons.save, size: 16),
                    label: const Text('Save Changes'),
                  ),
                ],
              ),
            )
          : null,
    );
  }

  Widget _buildJsonEditor({
    required TextEditingController controller,
    required String title,
    required String description,
  }) {
    return Column(
      children: [
        // Header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),

        // Editor
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: controller,
              maxLines: null,
              expands: true,
              style: const TextStyle(
                fontFamily: 'monospace',
                fontSize: 14,
              ),
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Enter JSON configuration...',
                contentPadding: EdgeInsets.all(16),
              ),
              textAlignVertical: TextAlignVertical.top,
            ),
          ),
        ),
      ],
    );
  }
}
