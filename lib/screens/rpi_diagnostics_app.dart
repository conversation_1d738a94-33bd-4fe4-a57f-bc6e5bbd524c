import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'dart:async';
import 'dart:convert';

/// Raspberry Pi Diagnostics App Entry Point
class RpiDiagnosticsApp {
  static const String id = 'rpi_diagnostics';
  static const String title = 'RPi Diagnostics';
  static const String version = '1.0.0';

  /// Launch the RPi Diagnostics app
  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const RpiDiagnosticsScreen(),
      ),
    );
  }

  /// Check if the app can run (always true since it works with local devices)
  static bool canRun(BuildContext context) {
    return true;
  }
}

/// Raspberry Pi Diagnostics Screen
class RpiDiagnosticsScreen extends StatefulWidget {
  const RpiDiagnosticsScreen({super.key});

  @override
  State<RpiDiagnosticsScreen> createState() => _RpiDiagnosticsScreenState();
}

class _RpiDiagnosticsScreenState extends State<RpiDiagnosticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedDevicePath;
  bool _isScanning = false;
  bool _isSdTesting = false;
  bool _deviceValidated = false;
  final Map<String, dynamic> _diagnosticResults = {};
  final List<DiagnosticIssue> _issues = [];
  List<String> _availableDevices = [];
  String? _detectedPiModel;
  String _selectedPiModel = 'auto';
  bool _showManualSelection = false;
  String? _cachedSudoPassword;
  final List<String> _piModels = [
    'auto',
    'Pi 1 Model A',
    'Pi 1 Model B',
    'Pi 2 Model B',
    'Pi 3 Model A+',
    'Pi 3 Model B',
    'Pi 3 Model B+',
    'Pi 4 Model B',
    'Pi 5',
    'Pi Zero',
    'Pi Zero W',
    'Pi Zero 2 W',
  ];

  final List<DiagnosticTab> _tabs = [
    DiagnosticTab(
      id: 'device_selection',
      title: 'Device Selection',
      icon: Icons.sd_card,
    ),
    DiagnosticTab(
      id: 'diagnostics',
      title: 'Diagnostics',
      icon: Icons.health_and_safety,
    ),
    DiagnosticTab(
      id: 'repairs',
      title: 'Repairs',
      icon: Icons.build_circle,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _scanForDevices();
    // Prompt for sudo password on app launch
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _promptForInitialSudoPassword();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _scanForDevices() async {
    setState(() {
      _isScanning = true;
    });

    try {
      final devices = await _detectRaspberryPiDevices();
      setState(() {
        _availableDevices = devices;
        _isScanning = false;
      });
    } catch (e) {
      setState(() {
        _isScanning = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error scanning for devices: $e')),
        );
      }
    }
  }

  Future<List<String>> _detectRaspberryPiDevices() async {
    final devices = <String>[];

    try {
      // On Linux, scan for block devices
      if (Platform.isLinux) {
        final result = await Process.run(
            'lsblk', ['-J', '-o', 'NAME,SIZE,TYPE,MOUNTPOINT,MODEL']);
        if (result.exitCode == 0) {
          // Parse lsblk JSON output to find removable storage devices
          // Debug: lsblk output available in result.stdout

          // For now, add common device paths that might be RPi devices
          final commonDevices = [
            '/dev/sda',
            '/dev/sdb',
            '/dev/sdc',
            '/dev/sdd',
            '/dev/mmcblk0',
            '/dev/mmcblk1'
          ];

          for (final device in commonDevices) {
            if (await File(device).exists()) {
              // Check if device has partitions that might contain RPi boot files
              final hasRpiPartitions =
                  await _checkDeviceForRpiPartitions(device);
              if (hasRpiPartitions) {
                devices.add(device);
              }
            }
          }
        }
      }

      // On macOS, check /dev/disk*
      if (Platform.isMacOS) {
        final result = await Process.run('diskutil', ['list', '-plist']);
        if (result.exitCode == 0) {
          // Parse diskutil output for external disks
          final commonDevices = [
            '/dev/disk1',
            '/dev/disk2',
            '/dev/disk3',
            '/dev/disk4'
          ];

          for (final device in commonDevices) {
            if (await File(device).exists()) {
              final hasRpiPartitions =
                  await _checkDeviceForRpiPartitions(device);
              if (hasRpiPartitions) {
                devices.add(device);
              }
            }
          }
        }
      }

      // On Windows, check for removable drives
      if (Platform.isWindows) {
        final driveLetters = ['D:', 'E:', 'F:', 'G:', 'H:', 'I:', 'J:', 'K:'];
        for (final drive in driveLetters) {
          final dir = Directory(drive);
          if (await dir.exists()) {
            final hasRpiFiles = await _checkMountPointForRpiFiles(drive);
            if (hasRpiFiles) {
              devices.add(drive);
            }
          }
        }
      }
    } catch (e) {
      // Error detecting devices: $e
    }

    return devices;
  }

  Future<bool> _checkDeviceForRpiPartitions(String devicePath) async {
    try {
      // Try to find mounted partitions of this device
      final result = await Process.run('mount', []);
      if (result.exitCode == 0) {
        final mountOutput = result.stdout as String;
        final lines = mountOutput.split('\n');

        for (final line in lines) {
          if (line.contains(devicePath)) {
            // Extract mount point
            final parts = line.split(' on ');
            if (parts.length >= 2) {
              final mountPoint = parts[1].split(' type ')[0];
              final hasRpiFiles = await _checkMountPointForRpiFiles(mountPoint);
              if (hasRpiFiles) {
                return true;
              }
            }
          }
        }
      }

      // If not mounted, try to check if it's a known RPi device by size/type
      return await _isLikelyRpiDevice(devicePath);
    } catch (e) {
      return false;
    }
  }

  Future<bool> _checkMountPointForRpiFiles(String mountPoint) async {
    try {
      final configPaths = [
        '$mountPoint/config.txt',
        '$mountPoint/boot/config.txt',
        '$mountPoint/boot/firmware/config.txt',
      ];

      for (final path in configPaths) {
        if (await File(path).exists()) {
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<bool> _isLikelyRpiDevice(String devicePath) async {
    try {
      // Check device size - RPi SD cards are typically 4GB-128GB
      final result = await Process.run('blockdev', ['--getsize64', devicePath]);
      if (result.exitCode == 0) {
        final sizeBytes = int.tryParse(result.stdout.toString().trim());
        if (sizeBytes != null) {
          final sizeGB = sizeBytes / (1024 * 1024 * 1024);
          // Typical RPi SD card sizes
          return sizeGB >= 2 && sizeGB <= 256;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50), // Green for diagnostics
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Icon(
                Icons.health_and_safety,
                color: Colors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
            const Text('RPi Diagnostics'),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _scanForDevices,
            tooltip: 'Refresh Device List',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _tabs
              .map((tab) => Tab(
                    icon: Icon(tab.icon),
                    text: tab.title,
                  ))
              .toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildDeviceSelectionTab(),
          _buildDiagnosticsTab(),
          _buildRepairsTab(),
        ],
      ),
    );
  }

  Widget _buildDeviceSelectionTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDeviceSelectionCard(),
          const SizedBox(height: 16),
          if (_availableDevices.isNotEmpty && !_showManualSelection)
            _buildDetectedDevicesCard(),
          if (_availableDevices.isNotEmpty && !_showManualSelection)
            const SizedBox(height: 16),
          _buildDeviceSelectionToggle(),
          const SizedBox(height: 16),
          _buildPiModelSelectionCard(),
        ],
      ),
    );
  }

  Widget _buildDeviceSelectionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.sd_card, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Device Selection',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Select the block device (e.g., /dev/sda) containing your Raspberry Pi OS to run diagnostics.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            if (_selectedDevicePath != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border:
                      Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.check_circle, color: Colors.green),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Selected Device:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(_selectedDevicePath!),
                          if (_deviceValidated)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  '✓ Raspberry Pi boot files detected',
                                  style: TextStyle(
                                      color: Colors.green, fontSize: 12),
                                ),
                                if (_detectedPiModel != null)
                                  Text(
                                    '✓ Detected: $_detectedPiModel',
                                    style: const TextStyle(
                                        color: Colors.blue, fontSize: 12),
                                  ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPiModelSelectionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.memory, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Raspberry Pi Model',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Select your Raspberry Pi model for accurate diagnostics and default configurations.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedPiModel,
              decoration: const InputDecoration(
                labelText: 'Pi Model',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.developer_board),
              ),
              items: _piModels.map((model) {
                return DropdownMenuItem(
                  value: model,
                  child: Text(model == 'auto' ? 'Auto-detect' : model),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPiModel = value!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetectedDevicesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.search, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Detected Devices',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isScanning)
              const Center(child: CircularProgressIndicator())
            else
              ...(_availableDevices.map((device) => _buildDeviceItem(device))),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceItem(String devicePath) {
    final isSelected = _selectedDevicePath == devicePath;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          Icons.sd_card,
          color: isSelected ? Colors.green : Colors.grey,
        ),
        title: Text(devicePath),
        subtitle: const Text('Block device with Raspberry Pi partitions'),
        trailing: isSelected
            ? const Icon(Icons.check_circle, color: Colors.green)
            : null,
        onTap: () => _selectDevice(devicePath),
        tileColor: isSelected ? Colors.green.withValues(alpha: 0.1) : null,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color:
                isSelected ? Colors.green : Colors.grey.withValues(alpha: 0.3),
          ),
        ),
      ),
    );
  }

  Widget _buildDeviceSelectionToggle() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Device Selection Method',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                Switch(
                  value: _showManualSelection,
                  onChanged: (value) {
                    setState(() {
                      _showManualSelection = value;
                    });
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              _showManualSelection
                  ? 'Manual selection mode: Browse for device directory or mount point'
                  : 'Auto-detection mode: Select from detected block devices',
              style: const TextStyle(fontSize: 14),
            ),
            if (_showManualSelection) ...[
              const SizedBox(height: 16),
              const Text(
                'If your device is not automatically detected, you can manually select the mount point or directory.',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: _selectDeviceManually,
                icon: const Icon(Icons.folder_open, color: Colors.white),
                label: const Text('Browse for Device'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDiagnosticsTab() {
    if (_selectedDevicePath == null || !_deviceValidated) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.warning, size: 64, color: Colors.orange),
            SizedBox(height: 16),
            Text(
              'Please select a valid Raspberry Pi device first',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDiagnosticSummaryCard(),
          const SizedBox(height: 16),
          _buildRunDiagnosticsCard(),
          const SizedBox(height: 16),
          if (_issues.isNotEmpty) _buildIssuesCard(),
        ],
      ),
    );
  }

  Widget _buildRepairsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_diagnosticResults.isEmpty) ...[
            _buildRunDiagnosticsFirstCard(),
            const SizedBox(height: 16),
          ],
          _buildQuickRepairsCard(),
          const SizedBox(height: 16),
          _buildDefaultConfigCard(),
          const SizedBox(height: 16),
          if (_issues.isNotEmpty) _buildAutoFixCard(),
        ],
      ),
    );
  }

  Widget _buildDiagnosticSummaryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Diagnostic Summary',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_diagnosticResults.isEmpty)
              const Text(
                  'No diagnostics run yet. Click "Run Diagnostics" to start.')
            else
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildVerboseSummaryItem(
                    'Boot Configuration',
                    _diagnosticResults['boot_config'],
                  ),
                  const SizedBox(height: 12),
                  _buildVerboseSummaryItem(
                    'Overclocking Safety',
                    _diagnosticResults['overclocking'],
                  ),
                  const SizedBox(height: 12),
                  _buildVerboseSummaryItem(
                    'File System',
                    _diagnosticResults['filesystem'],
                  ),
                  const SizedBox(height: 12),
                  _buildVerboseSummaryItem(
                    'Hardware Configuration',
                    _diagnosticResults['hardware'],
                  ),
                  const SizedBox(height: 12),
                  _buildVerboseSummaryItem(
                    'SD Card Health',
                    _diagnosticResults['sd_card'],
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRunDiagnosticsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.play_circle, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Run Diagnostics',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Perform comprehensive diagnostics on your Raspberry Pi boot configuration.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _isScanning ? null : _runDiagnostics,
                  icon: _isScanning
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.health_and_safety,
                          color: Colors.white),
                  label: Text(_isScanning ? 'Scanning...' : 'Run Diagnostics'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(200, 48),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _selectedDevicePath != null &&
                          _deviceValidated &&
                          !_isScanning &&
                          !_isSdTesting
                      ? _runSdCardBadSectorTest
                      : null,
                  icon: _isSdTesting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(Icons.storage, color: Colors.white),
                  label: Text(_isSdTesting ? 'Testing...' : 'Test SD Card'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(200, 48),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIssuesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _issues.isEmpty ? Icons.check_circle : Icons.warning,
                  color: _issues.isEmpty ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Issues Found (${_issues.length})',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_issues.isEmpty)
              const Text(
                  'No issues detected. Your Raspberry Pi configuration looks good!')
            else
              Column(
                children:
                    _issues.map((issue) => _buildIssueItem(issue)).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerboseSummaryItem(String label, Map<String, dynamic>? results) {
    if (results == null) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            const Icon(Icons.help, color: Colors.grey, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text('$label: Not checked')),
          ],
        ),
      );
    }

    final status = results['status'] ?? 'Unknown';
    Color color = Colors.grey;
    IconData icon = Icons.help;

    switch (status) {
      case 'OK':
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case 'Warning':
        color = Colors.orange;
        icon = Icons.warning;
        break;
      case 'Error':
        color = Colors.red;
        icon = Icons.error;
        break;
      case 'Info':
        color = Colors.blue;
        icon = Icons.info;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: color.withValues(alpha: 0.05),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          if (results['details'] != null) ...[
            const SizedBox(height: 8),
            Text(
              results['details'],
              style: const TextStyle(fontSize: 14),
            ),
          ],
          if (results['checks_performed'] != null) ...[
            const SizedBox(height: 8),
            Text(
              'Checks performed: ${(results['checks_performed'] as List).join(', ')}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          if (results['model_detected'] != null) ...[
            const SizedBox(height: 4),
            Text(
              'Model: ${results['model_detected']}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          if (results['settings_found'] != null) ...[
            const SizedBox(height: 4),
            Text(
              'Settings analyzed: ${results['settings_found']}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildIssueItem(DiagnosticIssue issue) {
    Color severityColor = Colors.grey;
    IconData severityIcon = Icons.info;

    switch (issue.severity.toLowerCase()) {
      case 'critical':
        severityColor = Colors.red;
        severityIcon = Icons.error;
        break;
      case 'warning':
        severityColor = Colors.orange;
        severityIcon = Icons.warning;
        break;
      case 'info':
        severityColor = Colors.blue;
        severityIcon = Icons.info;
        break;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: severityColor.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
        color: severityColor.withValues(alpha: 0.1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(severityIcon, color: severityColor, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  issue.title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              if (issue.canAutoFix)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Auto-fixable',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            issue.description,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDevice(String devicePath) async {
    setState(() {
      _selectedDevicePath = devicePath;
    });
    await _validateDevice(devicePath);
  }

  Future<void> _selectDeviceManually() async {
    final result = await FilePicker.platform.getDirectoryPath(
      dialogTitle: 'Select Raspberry Pi Device Directory',
    );

    if (result != null) {
      await _selectDevice(result);
    }
  }

  Future<void> _validateDevice(String devicePath) async {
    try {
      bool hasBootFiles = false;

      // If it's a block device (like /dev/sda), check its partitions
      if (devicePath.startsWith('/dev/')) {
        hasBootFiles = await _checkDeviceForRpiPartitions(devicePath);
      } else {
        // If it's a mount point or directory, check directly
        hasBootFiles = await _checkMountPointForRpiFiles(devicePath);
      }

      // Try to detect Pi model if validation successful
      if (hasBootFiles && _selectedPiModel == 'auto') {
        _detectedPiModel = await _detectPiModel(devicePath);
      }

      setState(() {
        _deviceValidated = hasBootFiles;
      });

      if (!hasBootFiles) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('No Raspberry Pi boot files found on selected device'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _deviceValidated = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error validating device: $e')),
        );
      }
    }
  }

  Future<String?> _detectPiModel(String devicePath) async {
    try {
      String? configPath;

      if (devicePath.startsWith('/dev/')) {
        configPath = await _findConfigFileOnDevice(devicePath);
      } else {
        final configPaths = [
          '$devicePath/config.txt',
          '$devicePath/boot/config.txt',
          '$devicePath/boot/firmware/config.txt',
        ];

        for (final path in configPaths) {
          if (await File(path).exists()) {
            configPath = path;
            break;
          }
        }
      }

      if (configPath != null) {
        final configContent = await File(configPath).readAsString();

        // Look for model-specific indicators in config.txt
        if (configContent.contains('pi5') || configContent.contains('2712')) {
          return 'Pi 5';
        } else if (configContent.contains('pi4') ||
            configContent.contains('2711')) {
          return 'Pi 4 Model B';
        } else if (configContent.contains('pi3') ||
            configContent.contains('2837')) {
          return 'Pi 3 Model B+';
        } else if (configContent.contains('pi2') ||
            configContent.contains('2836')) {
          return 'Pi 2 Model B';
        } else if (configContent.contains('zero')) {
          return 'Pi Zero W';
        }

        // Try to detect from other files if available
        final rootPath = configPath.contains('/boot/firmware/')
            ? configPath.replaceAll('/boot/firmware/config.txt', '')
            : configPath
                .replaceAll('/boot/config.txt', '')
                .replaceAll('/config.txt', '');

        // Check for device tree files that might indicate model
        final dtbPath = '$rootPath/boot/firmware/';
        if (await Directory(dtbPath).exists()) {
          final dtbFiles = await Directory(dtbPath).list().toList();
          for (final file in dtbFiles) {
            final name = file.path.toLowerCase();
            if (name.contains('pi5') || name.contains('2712')) {
              return 'Pi 5';
            } else if (name.contains('pi4') || name.contains('2711')) {
              return 'Pi 4 Model B';
            }
          }
        }
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> _runDiagnostics() async {
    if (_selectedDevicePath == null || !_deviceValidated) return;

    if (_cachedSudoPassword == null) {
      _showErrorSnackBar(
          'Administrator access required. Please restart the app.');
      return;
    }

    setState(() {
      _isScanning = true;
      _issues.clear();
      _diagnosticResults.clear();
    });

    try {
      // Run comprehensive diagnostics
      await _analyzeBootConfiguration();
      await _checkOverclockingSafety();
      await _validateFileSystem();
      await _checkHardwareConfiguration();
      await _testSdCardHealth();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Diagnostics completed. Found ${_issues.length} issues.'),
            backgroundColor: _issues.isEmpty ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error running diagnostics: $e')),
        );
      }
    } finally {
      setState(() {
        _isScanning = false;
      });
    }
  }

  Future<void> _runSdCardBadSectorTest() async {
    if (_selectedDevicePath == null || !_deviceValidated) return;

    if (_cachedSudoPassword == null) {
      _showErrorSnackBar(
          'Administrator access required. Please restart the app.');
      return;
    }

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SD Card Bad Sector Test'),
        content: const Text(
            'This will perform a comprehensive read-only test of your SD card using badblocks. '
            'This test is non-destructive but can take several hours for large cards.\n\n'
            'The test will check every sector on the device for hardware defects. '
            'Continue?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Start Real Test'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // Clear previous issues related to SD card testing
    setState(() {
      _isSdTesting = true;
      _issues.removeWhere((issue) => issue.title.contains('Bad Sectors'));
    });

    try {
      await _performBadSectorTest();
    } catch (e) {
      _showErrorSnackBar('Error during SD card test: $e');
    } finally {
      setState(() {
        _isSdTesting = false;
      });
    }
  }

  Future<void> _analyzeBootConfiguration() async {
    try {
      String? configPath;
      String? configContent;

      // If it's a block device, find the mounted boot partition
      if (_selectedDevicePath!.startsWith('/dev/')) {
        configPath = await _findConfigFileOnDevice(_selectedDevicePath!);
      } else {
        // If it's a mount point, check directly
        final configPaths = [
          '$_selectedDevicePath/config.txt',
          '$_selectedDevicePath/boot/config.txt',
          '$_selectedDevicePath/boot/firmware/config.txt',
        ];

        for (final path in configPaths) {
          if (await File(path).exists()) {
            configPath = path;
            break;
          }
        }
      }

      if (configPath == null) {
        _diagnosticResults['boot_config'] = {
          'status': 'Error',
          'details':
              'No config.txt file found on device - this will prevent the Pi from booting',
          'checks_performed': ['Boot partition scan', 'Config file detection'],
          'recommendations': [
            'Ensure SD card is properly flashed with Raspberry Pi OS'
          ]
        };
        _issues.add(DiagnosticIssue(
          title: 'Boot Configuration Missing',
          description:
              'No config.txt file found on device - Pi will not boot without this critical file',
          severity: 'Critical',
          canAutoFix: false,
        ));
        return;
      }

      configContent = await File(configPath).readAsString();
      final lines = configContent.split('\n');

      // Get current Pi model for safe limits
      final currentModel = _selectedPiModel == 'auto'
          ? _detectedPiModel ?? 'Pi 4 Model B'
          : _selectedPiModel;
      final safeLimits = _getSafeLimitsForModel(currentModel);

      // Comprehensive analysis
      bool hasIssues = false;
      int issueCount = 0;
      final checksPerformed = <String>[];
      final configSettings = <String, dynamic>{};

      // Parse all configuration settings
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.isEmpty || trimmedLine.startsWith('#')) continue;

        if (trimmedLine.contains('=')) {
          final parts = trimmedLine.split('=');
          if (parts.length == 2) {
            configSettings[parts[0]] = parts[1];
          }
        }
      }

      // CPU frequency analysis
      checksPerformed.add('CPU frequency limits');
      if (configSettings.containsKey('arm_freq')) {
        final freq = int.tryParse(configSettings['arm_freq']);
        if (freq != null) {
          final maxCpuFreq = safeLimits['max_cpu_freq']!;
          if (freq > maxCpuFreq) {
            _issues.add(DiagnosticIssue(
              title: 'Dangerous CPU Overclocking',
              description:
                  'CPU frequency ${freq}MHz exceeds safe limit of ${maxCpuFreq}MHz for $currentModel',
              severity: freq > maxCpuFreq + 200 ? 'Critical' : 'Warning',
              canAutoFix: true,
            ));
            hasIssues = true;
            issueCount++;
          }
        }
      }

      // GPU frequency analysis
      checksPerformed.add('GPU frequency limits');
      if (configSettings.containsKey('gpu_freq')) {
        final freq = int.tryParse(configSettings['gpu_freq']);
        final maxGpuFreq = safeLimits['max_gpu_freq']!;
        if (freq != null && freq > maxGpuFreq) {
          _issues.add(DiagnosticIssue(
            title: 'High GPU Overclocking',
            description:
                'GPU frequency ${freq}MHz exceeds recommended limit of ${maxGpuFreq}MHz',
            severity: 'Warning',
            canAutoFix: true,
          ));
          hasIssues = true;
          issueCount++;
        }
      }

      // Voltage analysis
      checksPerformed.add('Voltage safety');
      if (configSettings.containsKey('over_voltage')) {
        final voltage = int.tryParse(configSettings['over_voltage']);
        final maxVoltage = safeLimits['max_voltage']!;
        if (voltage != null && voltage > maxVoltage) {
          _issues.add(DiagnosticIssue(
            title: 'Dangerous Overvoltage',
            description:
                'Overvoltage $voltage exceeds safe limit of $maxVoltage - risk of permanent damage',
            severity: 'Critical',
            canAutoFix: true,
          ));
          hasIssues = true;
          issueCount++;
        }
      }

      // Memory split analysis
      checksPerformed.add('Memory allocation');
      if (configSettings.containsKey('gpu_mem')) {
        final mem = int.tryParse(configSettings['gpu_mem']);
        if (mem != null) {
          if (mem > 512) {
            _issues.add(DiagnosticIssue(
              title: 'Excessive GPU Memory',
              description:
                  'GPU memory ${mem}MB is very high - may cause system instability',
              severity: 'Warning',
              canAutoFix: true,
            ));
            issueCount++;
          } else if (mem < 16 &&
              (configSettings.containsKey('camera_auto_detect') ||
                  configSettings.containsKey('start_x'))) {
            _issues.add(DiagnosticIssue(
              title: 'Insufficient GPU Memory for Camera',
              description:
                  'GPU memory ${mem}MB too low for camera operation - minimum 64MB recommended',
              severity: 'Warning',
              canAutoFix: true,
            ));
            issueCount++;
          }
        }
      }

      // Boot failure checks
      checksPerformed.add('Boot failure prevention');

      // Check for missing essential settings
      if (!configSettings.containsKey('gpu_mem') &&
          !configSettings.containsKey('gpu_mem_256') &&
          !configSettings.containsKey('gpu_mem_512')) {
        _issues.add(DiagnosticIssue(
          title: 'No GPU Memory Configuration',
          description:
              'No GPU memory split defined - may cause boot issues on some models',
          severity: 'Info',
          canAutoFix: true,
        ));
        issueCount++;
      }

      // If no issues found, perform additional comprehensive checks
      if (issueCount == 0) {
        checksPerformed.addAll([
          'Power supply compatibility',
          'Thermal management',
          'Hardware interface conflicts'
        ]);

        // Check for potential power issues
        if (currentModel.contains('Pi 4') || currentModel.contains('Pi 5')) {
          if (!configSettings.containsKey('usb_max_current_enable') &&
              (configSettings.containsKey('arm_freq') ||
                  configSettings.containsKey('gpu_freq'))) {
            _issues.add(DiagnosticIssue(
              title: 'Power Supply Optimization',
              description:
                  'Overclocking detected without USB current boost - consider enabling for stability',
              severity: 'Info',
              canAutoFix: true,
            ));
            issueCount++;
          }
        }
      }

      _diagnosticResults['boot_config'] = {
        'status': hasIssues ? 'Warning' : (issueCount > 0 ? 'Info' : 'OK'),
        'path': configPath,
        'issues': issueCount,
        'details': hasIssues
            ? 'Found $issueCount configuration issues that may affect stability or boot'
            : issueCount > 0
                ? 'Found $issueCount optimization suggestions'
                : 'Boot configuration appears healthy - all critical settings within safe parameters',
        'checks_performed': checksPerformed,
        'model_detected': currentModel,
        'settings_found': configSettings.keys.length,
      };

      setState(() {}); // Update UI
    } catch (e) {
      _diagnosticResults['boot_config'] = {
        'status': 'Error',
        'details': 'Failed to analyze config.txt: $e',
        'checks_performed': ['File access attempt'],
      };
      _issues.add(DiagnosticIssue(
        title: 'Boot Configuration Analysis Failed',
        description: 'Error reading config.txt: $e',
        severity: 'Critical',
        canAutoFix: false,
      ));
    }
  }

  Map<String, int> _getSafeLimitsForModel(String model) {
    switch (model) {
      case 'Pi 5':
        return {'max_cpu_freq': 2400, 'max_gpu_freq': 900, 'max_voltage': 8};
      case 'Pi 4 Model B':
        return {'max_cpu_freq': 2000, 'max_gpu_freq': 750, 'max_voltage': 6};
      case 'Pi 3 Model B+':
      case 'Pi 3 Model B':
      case 'Pi 3 Model A+':
        return {'max_cpu_freq': 1400, 'max_gpu_freq': 500, 'max_voltage': 6};
      case 'Pi 2 Model B':
        return {'max_cpu_freq': 1000, 'max_gpu_freq': 400, 'max_voltage': 6};
      case 'Pi Zero 2 W':
        return {'max_cpu_freq': 1200, 'max_gpu_freq': 400, 'max_voltage': 6};
      case 'Pi Zero W':
      case 'Pi Zero':
        return {'max_cpu_freq': 1000, 'max_gpu_freq': 400, 'max_voltage': 6};
      default:
        return {
          'max_cpu_freq': 1800,
          'max_gpu_freq': 600,
          'max_voltage': 6
        }; // Conservative defaults
    }
  }

  Future<String?> _findConfigFileOnDevice(String devicePath) async {
    try {
      // Find mounted partitions of this device
      final result = await Process.run('mount', []);
      if (result.exitCode == 0) {
        final mountOutput = result.stdout as String;
        final lines = mountOutput.split('\n');

        for (final line in lines) {
          if (line.contains(devicePath)) {
            // Extract mount point
            final parts = line.split(' on ');
            if (parts.length >= 2) {
              final mountPoint = parts[1].split(' type ')[0];

              // Check for config files in this mount point
              final configPaths = [
                '$mountPoint/config.txt',
                '$mountPoint/boot/config.txt',
                '$mountPoint/boot/firmware/config.txt',
              ];

              for (final path in configPaths) {
                if (await File(path).exists()) {
                  return path;
                }
              }
            }
          }
        }
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> _checkOverclockingSafety() async {
    try {
      // This method is now integrated into _analyzeBootConfiguration
      // but we can add additional safety checks here
      bool hasOverclockingIssues = false;

      // Check if any critical overclocking issues were found
      for (final issue in _issues) {
        if (issue.title.contains('Dangerous') && issue.severity == 'Critical') {
          hasOverclockingIssues = true;
          break;
        }
      }

      _diagnosticResults['overclocking'] = {
        'status': hasOverclockingIssues ? 'Warning' : 'OK'
      };

      setState(() {}); // Update UI
    } catch (e) {
      _diagnosticResults['overclocking'] = {'status': 'Error'};
    }
  }

  Future<void> _validateFileSystem() async {
    try {
      String devicePath = _selectedDevicePath!;
      List<String> checksPerformed = [];
      List<String> issues = [];
      bool hasFileSystemIssues = false;

      // If it's a mount point, find the underlying device
      if (!devicePath.startsWith('/dev/')) {
        final result =
            await Process.run('findmnt', ['-n', '-o', 'SOURCE', devicePath]);
        if (result.exitCode == 0) {
          devicePath = result.stdout.toString().trim();
        }
      }

      checksPerformed.add('Device path resolution');

      // Check filesystem type
      final fsTypeResult =
          await Process.run('lsblk', ['-n', '-o', 'FSTYPE', devicePath]);
      String fsType = 'unknown';
      if (fsTypeResult.exitCode == 0) {
        fsType = fsTypeResult.stdout.toString().trim();
        checksPerformed.add('Filesystem type detection');
      }

      // Check if device is mounted
      final mountResult = await Process.run('findmnt', [devicePath]);
      bool isMounted = mountResult.exitCode == 0;
      checksPerformed.add('Mount status verification');

      // For mounted filesystems, check read/write access
      if (isMounted) {
        try {
          // Find mount point
          final mountPointResult =
              await Process.run('findmnt', ['-n', '-o', 'TARGET', devicePath]);
          if (mountPointResult.exitCode == 0) {
            final mountPoint = mountPointResult.stdout.toString().trim();

            // Test read access
            final testFile = File('$mountPoint/.rpi_diag_test');
            await testFile.writeAsString('test');
            await testFile.readAsString();
            await testFile.delete();
            checksPerformed.add('Read/write access test');
          }
        } catch (e) {
          issues.add('Read/write access failed');
          hasFileSystemIssues = true;
          _issues.add(DiagnosticIssue(
            title: 'Filesystem Access Error',
            description:
                'Cannot read/write to filesystem - check permissions or corruption',
            severity: 'Critical',
            canAutoFix: false,
          ));
        }
      } else {
        // For unmounted filesystems, run fsck if possible
        if (fsType.isNotEmpty && fsType != 'unknown') {
          try {
            final fsckResult = await Process.run(
              'fsck',
              ['-n', devicePath],
              runInShell: true,
            );
            checksPerformed.add('Filesystem integrity check (fsck)');

            if (fsckResult.exitCode != 0) {
              final errorOutput = fsckResult.stderr.toString();
              if (errorOutput.contains('errors') ||
                  errorOutput.contains('corrupt')) {
                issues.add('Filesystem corruption detected');
                hasFileSystemIssues = true;
                _issues.add(DiagnosticIssue(
                  title: 'Filesystem Corruption',
                  description:
                      'Filesystem check found errors that may prevent proper operation',
                  severity: 'Critical',
                  canAutoFix: false,
                ));
              }
            }
          } catch (e) {
            checksPerformed
                .add('Filesystem check failed (insufficient permissions)');
          }
        }
      }

      // Check if we can access the boot partition
      if (_selectedDevicePath!.startsWith('/dev/')) {
        final configPath = await _findConfigFileOnDevice(_selectedDevicePath!);
        if (configPath == null) {
          hasFileSystemIssues = true;
          issues.add('Boot partition access failed');
          _issues.add(DiagnosticIssue(
            title: 'Boot Partition Access Issue',
            description: 'Cannot access boot partition or config files',
            severity: 'Critical',
            canAutoFix: false,
          ));
        } else {
          checksPerformed.add('Boot partition access verification');
        }
      }

      _diagnosticResults['filesystem'] = {
        'status': hasFileSystemIssues ? 'Error' : 'OK',
        'details': issues.isEmpty
            ? 'Filesystem appears healthy and accessible'
            : 'Found ${issues.length} filesystem issues: ${issues.join(', ')}',
        'checks_performed': checksPerformed,
        'filesystem_type': fsType,
        'mounted': isMounted,
        'issues': issues,
      };

      setState(() {}); // Update UI
    } catch (e) {
      _diagnosticResults['filesystem'] = {
        'status': 'Error',
        'details': 'Could not validate filesystem: $e',
        'checks_performed': ['Basic filesystem validation'],
      };
      _issues.add(DiagnosticIssue(
        title: 'File System Check Failed',
        description: 'Error checking file system: $e',
        severity: 'Warning',
        canAutoFix: false,
      ));
    }
  }

  Future<void> _checkHardwareConfiguration() async {
    try {
      // Check for common hardware configuration issues
      String? configPath;

      if (_selectedDevicePath!.startsWith('/dev/')) {
        configPath = await _findConfigFileOnDevice(_selectedDevicePath!);
      }

      if (configPath != null) {
        final configContent = await File(configPath).readAsString();
        final lines = configContent.split('\n');

        // Check for common hardware issues
        bool hasCamera = false;
        bool hasSPI = false;
        bool hasI2C = false;

        for (final line in lines) {
          final trimmedLine = line.trim();
          if (trimmedLine.isEmpty || trimmedLine.startsWith('#')) continue;

          if (trimmedLine.contains('camera_auto_detect=1') ||
              trimmedLine.contains('start_x=1')) {
            hasCamera = true;
          }
          if (trimmedLine.contains('dtparam=spi=on')) {
            hasSPI = true;
          }
          if (trimmedLine.contains('dtparam=i2c_arm=on')) {
            hasI2C = true;
          }
        }

        // Add informational issues about enabled features
        if (hasCamera) {
          _issues.add(DiagnosticIssue(
            title: 'Camera Module Enabled',
            description:
                'Camera module is enabled - ensure camera is properly connected',
            severity: 'Info',
            canAutoFix: false,
          ));
        }

        _diagnosticResults['hardware'] = {
          'status': 'OK',
          'camera': hasCamera,
          'spi': hasSPI,
          'i2c': hasI2C,
        };
      } else {
        _diagnosticResults['hardware'] = {'status': 'Error'};
      }

      setState(() {}); // Update UI
    } catch (e) {
      _diagnosticResults['hardware'] = {'status': 'Error'};
    }
  }

  Future<void> _testSdCardHealth() async {
    try {
      bool hasIssues = false;
      int issueCount = 0;
      final checksPerformed = <String>[];
      final cardInfo = <String, dynamic>{};

      // Get device info
      String devicePath = _selectedDevicePath!;
      if (!devicePath.startsWith('/dev/')) {
        // If it's a mount point, try to find the underlying device
        final result =
            await Process.run('findmnt', ['-n', '-o', 'SOURCE', devicePath]);
        if (result.exitCode == 0) {
          devicePath = result.stdout.toString().trim();
          // Extract base device (e.g., /dev/sda1 -> /dev/sda)
          devicePath = devicePath.replaceAll(RegExp(r'\d+$'), '');
        }
      }

      // Check if it's a block device
      if (devicePath.startsWith('/dev/')) {
        // Test 1: Check device size and type
        checksPerformed.add('Device size and type validation');
        final sizeResult =
            await Process.run('blockdev', ['--getsize64', devicePath]);
        if (sizeResult.exitCode == 0) {
          final sizeBytes = int.tryParse(sizeResult.stdout.toString().trim());
          if (sizeBytes != null) {
            final sizeGB = sizeBytes / (1024 * 1024 * 1024);
            cardInfo['size_gb'] = sizeGB.toStringAsFixed(1);

            // Check if size is appropriate for RPi
            if (sizeGB < 4) {
              _issues.add(DiagnosticIssue(
                title: 'SD Card Too Small',
                description:
                    'SD card is ${sizeGB.toStringAsFixed(1)}GB - minimum 8GB recommended for Raspberry Pi OS',
                severity: 'Warning',
                canAutoFix: false,
              ));
              hasIssues = true;
              issueCount++;
            } else if (sizeGB > 512) {
              _issues.add(DiagnosticIssue(
                title: 'Unusually Large Storage Device',
                description:
                    'Device is ${sizeGB.toStringAsFixed(1)}GB - verify this is the correct Raspberry Pi device',
                severity: 'Info',
                canAutoFix: false,
              ));
              issueCount++;
            }
          }
        }

        // Test 2: Check read/write speed (basic test)
        checksPerformed.add('Read/write performance test');
        try {
          final tempFile =
              '/tmp/rpi_diag_test_${DateTime.now().millisecondsSinceEpoch}';
          final stopwatch = Stopwatch()..start();

          // Write test (1MB)
          final writeResult = await Process.run('dd', [
            'if=/dev/zero',
            'of=$tempFile',
            'bs=1M',
            'count=1',
            'conv=fsync'
          ]);

          if (writeResult.exitCode == 0) {
            stopwatch.stop();
            final writeSpeed =
                1.0 / (stopwatch.elapsedMilliseconds / 1000.0); // MB/s
            cardInfo['write_speed_mbs'] = writeSpeed.toStringAsFixed(1);

            // Clean up test file
            await Process.run('rm', ['-f', tempFile]);

            // Check if write speed is adequate
            if (writeSpeed < 5) {
              _issues.add(DiagnosticIssue(
                title: 'Slow SD Card Write Speed',
                description:
                    'Write speed is ${writeSpeed.toStringAsFixed(1)}MB/s - Class 10 or better recommended',
                severity: 'Warning',
                canAutoFix: false,
              ));
              hasIssues = true;
              issueCount++;
            }
          }
        } catch (e) {
          // Speed test failed, not critical
        }

        // Test 3: Check for filesystem errors
        checksPerformed.add('Filesystem integrity check');
        if (devicePath.contains('sd') || devicePath.contains('mmcblk')) {
          cardInfo['type'] = 'SD Card';

          // Check if it's mounted (we shouldn't fsck mounted filesystems)
          final mountResult = await Process.run('mount', []);
          if (mountResult.exitCode == 0) {
            final mountOutput = mountResult.stdout as String;
            if (!mountOutput.contains(devicePath)) {
              // Not mounted, safe to check
              final fsckResult =
                  await Process.run('fsck', ['-n', '${devicePath}1']);
              if (fsckResult.exitCode != 0) {
                _issues.add(DiagnosticIssue(
                  title: 'Filesystem Errors Detected',
                  description:
                      'Filesystem check found errors - SD card may be corrupted',
                  severity: 'Critical',
                  canAutoFix: false,
                ));
                hasIssues = true;
                issueCount++;
              }
            }
          }
        } else {
          cardInfo['type'] = 'USB/Other Storage';
        }

        // Test 4: Check SD card class/speed rating (if available)
        checksPerformed.add('SD card specifications check');
        if (devicePath.contains('mmcblk')) {
          // Try to get SD card info from /sys
          final cardPath = devicePath.replaceAll('/dev/', '/sys/block/');
          final cidFile = File('$cardPath/device/cid');
          if (await cidFile.exists()) {
            try {
              final cid = await cidFile.readAsString();
              cardInfo['cid'] = cid.trim();
            } catch (e) {
              // CID read failed, not critical
            }
          }
        }
      }

      _diagnosticResults['sd_card'] = {
        'status': hasIssues ? 'Warning' : (issueCount > 0 ? 'Info' : 'OK'),
        'issues': issueCount,
        'details': hasIssues
            ? 'Found $issueCount SD card issues that may affect performance'
            : issueCount > 0
                ? 'Found $issueCount SD card recommendations'
                : 'SD card appears healthy and suitable for Raspberry Pi use',
        'checks_performed': checksPerformed,
        'card_info': cardInfo,
      };

      setState(() {}); // Update UI
    } catch (e) {
      _diagnosticResults['sd_card'] = {
        'status': 'Error',
        'details': 'Failed to test SD card: $e',
        'checks_performed': ['Device access attempt'],
      };
    }
  }

  Widget _buildRunDiagnosticsFirstCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Run Diagnostics First',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Please run diagnostics first to identify issues that can be automatically repaired.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () =>
                  _tabController.animateTo(1), // Go to diagnostics tab
              icon: const Icon(Icons.health_and_safety, color: Colors.white),
              label: const Text('Go to Diagnostics'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickRepairsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.build_circle, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Quick Repairs',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Common repair actions that can fix boot issues.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _selectedDevicePath != null && _deviceValidated
                      ? _resetOverclocking
                      : null,
                  icon: const Icon(Icons.speed, color: Colors.white),
                  label: const Text('Reset Overclocking'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _selectedDevicePath != null && _deviceValidated
                      ? _fixMemorySplit
                      : null,
                  icon: const Icon(Icons.memory, color: Colors.white),
                  label: const Text('Fix Memory Split'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _selectedDevicePath != null && _deviceValidated
                      ? _removeProblematicSettings
                      : null,
                  icon:
                      const Icon(Icons.cleaning_services, color: Colors.white),
                  label: const Text('Remove Problematic Settings'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.restore, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Restore Default Configuration',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Restore a clean, default configuration for your ${_selectedPiModel == 'auto' ? _detectedPiModel ?? 'Raspberry Pi' : _selectedPiModel}.',
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _selectedDevicePath != null && _deviceValidated
                  ? _restoreDefaultConfig
                  : null,
              icon: const Icon(Icons.restore, color: Colors.white),
              label: const Text('Restore Default Config'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAutoFixCard() {
    final autoFixableIssues =
        _issues.where((issue) => issue.canAutoFix).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.auto_fix_high, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Auto-Fix Issues (${autoFixableIssues.length})',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'The following issues can be automatically fixed:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 12),
            ...autoFixableIssues.map((issue) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.check_circle_outline,
                        color: Colors.green,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                          child: Text(issue.title,
                              style: const TextStyle(fontSize: 14))),
                    ],
                  ),
                )),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: autoFixableIssues.isNotEmpty ? _autoFixIssues : null,
              icon: const Icon(Icons.auto_fix_high, color: Colors.white),
              label: Text('Fix ${autoFixableIssues.length} Issues'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _promptForInitialSudoPassword() async {
    if (!mounted) return;

    final passwordController = TextEditingController();
    bool isValidating = false;
    String? errorMessage;
    bool hasPassword = false;

    // Helper function to validate password
    Future<void> validatePassword(String password, StateSetter setState) async {
      setState(() {
        isValidating = true;
        errorMessage = null;
      });

      try {
        // Test the password with a simple sudo command
        final process = await Process.start('sudo', ['-S', 'echo', 'test']);
        process.stdin.writeln(password);
        await process.stdin.close();

        final exitCode = await process.exitCode;

        if (exitCode == 0) {
          // Password is correct
          _cachedSudoPassword = password;
          if (mounted) {
            Navigator.of(context).pop(true);
          }
        } else {
          // Password is incorrect
          setState(() {
            isValidating = false;
            errorMessage = 'Incorrect password. Please try again.';
          });
        }
      } catch (e) {
        setState(() {
          isValidating = false;
          errorMessage = 'Failed to validate password. Please try again.';
        });
      }
    }

    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.security, color: Colors.orange),
              SizedBox(width: 8),
              Text('Administrator Access Required'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'RPi Diagnostics requires administrator privileges to access system devices, run diagnostic commands, and modify configuration files.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              const Text(
                'Please enter your system password to continue:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: passwordController,
                obscureText: true,
                autofocus: true,
                enabled: !isValidating,
                decoration: InputDecoration(
                  labelText: 'Password',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.lock),
                  errorText: errorMessage,
                  helperText:
                      'This will be stored securely for this session only',
                ),
                onChanged: (value) {
                  setState(() {
                    hasPassword = value.isNotEmpty;
                  });
                },
                onSubmitted: (value) async {
                  if (value.isNotEmpty && !isValidating) {
                    await validatePassword(value, setState);
                  }
                },
              ),
              if (isValidating) ...[
                const SizedBox(height: 16),
                const Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                    SizedBox(width: 8),
                    Text('Validating password...'),
                  ],
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: isValidating
                  ? null
                  : () {
                      Navigator.of(context).pop(false);
                    },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: isValidating || !hasPassword
                  ? null
                  : () async {
                      await validatePassword(passwordController.text, setState);
                    },
              child: Text(isValidating ? 'Validating...' : 'Continue'),
            ),
          ],
        ),
      ),
    );

    if (confirmed != true) {
      // User cancelled, navigate back to apps page
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<SdCardInfo?> _getSdCardInfo(String devicePath) async {
    try {
      // Get device size
      final sizeResult =
          await Process.run('blockdev', ['--getsize64', devicePath]);
      String size = 'Unknown';
      if (sizeResult.exitCode == 0) {
        final sizeBytes = int.tryParse(sizeResult.stdout.toString().trim());
        if (sizeBytes != null) {
          final sizeGB = sizeBytes / (1024 * 1024 * 1024);
          size = '${sizeGB.toStringAsFixed(1)} GB';
        }
      }

      // Get device model and manufacturer info
      String model = 'Storage Device';
      String manufacturer = 'Mass';
      String serialNumber = 'Unknown';

      // Try multiple methods to get device information
      try {
        // Method 1: Try udevadm
        final udevResult = await Process.run(
            'udevadm', ['info', '--query=all', '--name=$devicePath']);
        if (udevResult.exitCode == 0) {
          final output = udevResult.stdout.toString();
          final lines = output.split('\n');

          for (final line in lines) {
            if (line.contains('ID_MODEL=')) {
              final value = line.split('=')[1].trim();
              if (value.isNotEmpty && value != 'Unknown') {
                model = value.replaceAll('_', ' ');
              }
            } else if (line.contains('ID_VENDOR=')) {
              final value = line.split('=')[1].trim();
              if (value.isNotEmpty && value != 'Unknown') {
                manufacturer = value.replaceAll('_', ' ');
              }
            } else if (line.contains('ID_SERIAL_SHORT=')) {
              final value = line.split('=')[1].trim();
              if (value.isNotEmpty && value != 'Unknown') {
                serialNumber = value;
              }
            }
          }
        }
      } catch (e) {
        // Continue to fallback methods
      }

      // Method 2: Try lsblk for additional info if still unknown
      if (model == 'Storage Device' || manufacturer == 'Mass') {
        try {
          final lsblkResult = await Process.run(
              'lsblk', ['-o', 'NAME,MODEL,VENDOR', '-n', devicePath]);
          if (lsblkResult.exitCode == 0) {
            final output = lsblkResult.stdout.toString().trim();
            final parts = output.split(RegExp(r'\s+'));
            if (parts.length >= 3) {
              if (parts[1].isNotEmpty &&
                  parts[1] != '-' &&
                  model == 'Storage Device') {
                model = parts[1];
              }
              if (parts[2].isNotEmpty &&
                  parts[2] != '-' &&
                  manufacturer == 'Mass') {
                manufacturer = parts[2];
              }
            }
          }
        } catch (e) {
          // Continue to next method
        }
      }

      // Method 3: Try reading from /sys filesystem
      if (model == 'Storage Device' || manufacturer == 'Mass') {
        try {
          final deviceName = devicePath.split('/').last;
          final sysPath = '/sys/block/$deviceName/device';

          // Try to read model
          final modelFile = File('$sysPath/model');
          if (await modelFile.exists() && model == 'Storage Device') {
            final modelContent = await modelFile.readAsString();
            final modelValue = modelContent.trim();
            if (modelValue.isNotEmpty) {
              model = modelValue;
            }
          }

          // Try to read vendor
          final vendorFile = File('$sysPath/vendor');
          if (await vendorFile.exists() && manufacturer == 'Mass') {
            final vendorContent = await vendorFile.readAsString();
            final vendorValue = vendorContent.trim();
            if (vendorValue.isNotEmpty) {
              manufacturer = vendorValue;
            }
          }
        } catch (e) {
          // Keep defaults
        }
      }

      // Get filesystem info
      String filesystem = 'Unknown';
      try {
        final fsResult = await Process.run('lsblk', ['-f', devicePath]);
        if (fsResult.exitCode == 0) {
          final output = fsResult.stdout.toString();
          if (output.contains('ext4')) {
            filesystem = 'ext4';
          } else if (output.contains('fat32')) {
            filesystem = 'FAT32';
          } else if (output.contains('vfat')) {
            filesystem = 'FAT32';
          } else if (output.contains('ntfs')) {
            filesystem = 'NTFS';
          }
        }
      } catch (e) {
        // Keep default
      }

      return SdCardInfo(
        devicePath: devicePath,
        size: size,
        model: model,
        manufacturer: manufacturer,
        serialNumber: serialNumber,
        filesystem: filesystem,
      );
    } catch (e) {
      return null;
    }
  }

  Future<void> _performBadSectorTest() async {
    try {
      String devicePath = _selectedDevicePath!;

      // If it's a mount point, find the underlying device
      if (!devicePath.startsWith('/dev/')) {
        final result =
            await Process.run('findmnt', ['-n', '-o', 'SOURCE', devicePath]);
        if (result.exitCode == 0) {
          devicePath = result.stdout.toString().trim();
          // Extract base device (e.g., /dev/sda1 -> /dev/sda)
          devicePath = devicePath.replaceAll(RegExp(r'\d+$'), '');
        }
      }

      if (!devicePath.startsWith('/dev/')) {
        _showErrorSnackBar('Could not determine block device for testing');
        return;
      }

      // Get SD card info
      final sdCardInfo = await _getSdCardInfo(devicePath);

      // Show modern progress dialog
      if (!mounted) return;

      await _showSectorTestDialog(devicePath, sdCardInfo);
    } catch (e) {
      _showErrorSnackBar('Bad sector test failed: $e');
    }
  }

  Future<void> _showSectorTestDialog(
      String devicePath, SdCardInfo? sdCardInfo) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return SectorTestDialog(
          devicePath: devicePath,
          sdCardInfo: sdCardInfo,
          sudoPassword: _cachedSudoPassword!,
          onComplete: (results) {
            // Handle test completion
            if (results.badSectors > 0) {
              _issues.add(DiagnosticIssue(
                title: 'Bad Sectors Detected',
                description:
                    'Found ${results.badSectors} bad sectors on SD card - consider replacing',
                severity: results.badSectors > 10 ? 'Critical' : 'Warning',
                canAutoFix: false,
              ));
            }

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(results.badSectors == 0
                      ? 'SD card test completed: No bad sectors found'
                      : 'SD card test completed: Found ${results.badSectors} bad sectors'),
                  backgroundColor:
                      results.badSectors == 0 ? Colors.green : Colors.orange,
                ),
              );
            }
          },
        );
      },
    );
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Helper methods for repairs
  Future<String?> _getConfigPath() async {
    if (_selectedDevicePath!.startsWith('/dev/')) {
      return await _findConfigFileOnDevice(_selectedDevicePath!);
    } else {
      final configPaths = [
        '$_selectedDevicePath/config.txt',
        '$_selectedDevicePath/boot/config.txt',
        '$_selectedDevicePath/boot/firmware/config.txt',
      ];

      for (final path in configPaths) {
        if (await File(path).exists()) {
          return path;
        }
      }
    }
    return null;
  }

  Future<void> _createBackup(String configPath) async {
    try {
      final configFile = File(configPath);
      final backupPath =
          '$configPath.backup.${DateTime.now().millisecondsSinceEpoch}';
      await configFile.copy(backupPath);
    } catch (e) {
      // Backup failed, but continue with repair
      // Warning: Could not create backup: $e
    }
  }

  int _getRecommendedGpuMemory(String model) {
    switch (model) {
      case 'Pi 5':
        return 128; // Pi 5 has more memory, can afford higher GPU allocation
      case 'Pi 4 Model B':
        return 64; // Balanced for Pi 4
      case 'Pi 3 Model B+':
      case 'Pi 3 Model B':
      case 'Pi 3 Model A+':
        return 64; // Standard for Pi 3
      case 'Pi 2 Model B':
        return 64; // Conservative for Pi 2
      case 'Pi Zero 2 W':
        return 64; // Balanced for Zero 2 W
      case 'Pi Zero W':
      case 'Pi Zero':
        return 64; // Conservative for original Zero
      default:
        return 64; // Safe default
    }
  }

  String _getDefaultConfigForModel(String model) {
    final gpuMem = _getRecommendedGpuMemory(model);
    final timestamp = DateTime.now().toIso8601String();

    return '''# Raspberry Pi Configuration
# Generated by RPi Diagnostics on $timestamp
# Model: $model

# GPU Memory Split
gpu_mem=$gpuMem

# Enable camera (if needed)
camera_auto_detect=1

# Enable I2C and SPI (commonly used)
dtparam=i2c_arm=on
dtparam=spi=on

# Audio
dtparam=audio=on

# Additional display settings
# Uncomment if you need to force a specific resolution
# hdmi_force_hotplug=1
# hdmi_group=1
# hdmi_mode=1

# Overclock settings (commented out for safety)
# arm_freq=1500
# gpu_freq=500
# over_voltage=2

[pi4]
# Pi 4 specific settings
dtoverlay=vc4-kms-v3d
max_framebuffers=2

[all]
''';
  }

  // Repair functionality methods
  Future<void> _resetOverclocking() async {
    try {
      final configPath = await _getConfigPath();
      if (configPath == null) {
        _showErrorSnackBar('Could not find config.txt file');
        return;
      }

      // Create backup first
      await _createBackup(configPath);

      // Read current config
      final configContent = await File(configPath).readAsString();
      final lines = configContent.split('\n');
      final newLines = <String>[];

      // Remove overclocking settings
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.startsWith('arm_freq=') ||
            trimmedLine.startsWith('gpu_freq=') ||
            trimmedLine.startsWith('over_voltage=') ||
            trimmedLine.startsWith('core_freq=') ||
            trimmedLine.startsWith('sdram_freq=')) {
          newLines.add('# $line (removed by RPi Diagnostics)');
        } else {
          newLines.add(line);
        }
      }

      // Write updated config
      await File(configPath).writeAsString(newLines.join('\n'));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✓ Overclocking settings reset successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Re-run diagnostics to update results
      await _runDiagnostics();
    } catch (e) {
      _showErrorSnackBar('Error resetting overclocking: $e');
    }
  }

  Future<void> _fixMemorySplit() async {
    try {
      final configPath = await _getConfigPath();
      if (configPath == null) {
        _showErrorSnackBar('Could not find config.txt file');
        return;
      }

      // Create backup first
      await _createBackup(configPath);

      // Get current Pi model for appropriate memory split
      final currentModel = _selectedPiModel == 'auto'
          ? _detectedPiModel ?? 'Pi 4 Model B'
          : _selectedPiModel;
      final recommendedGpuMem = _getRecommendedGpuMemory(currentModel);

      // Read current config
      final configContent = await File(configPath).readAsString();
      final lines = configContent.split('\n');
      final newLines = <String>[];
      bool hasGpuMem = false;

      // Update or add GPU memory setting
      for (final line in lines) {
        final trimmedLine = line.trim();
        if (trimmedLine.startsWith('gpu_mem=')) {
          newLines.add('gpu_mem=$recommendedGpuMem');
          hasGpuMem = true;
        } else if (trimmedLine.startsWith('gpu_mem_256=') ||
            trimmedLine.startsWith('gpu_mem_512=') ||
            trimmedLine.startsWith('gpu_mem_1024=')) {
          newLines.add('# $line (replaced by RPi Diagnostics)');
        } else {
          newLines.add(line);
        }
      }

      // Add GPU memory setting if not found
      if (!hasGpuMem) {
        newLines.add('');
        newLines.add('# GPU memory split (added by RPi Diagnostics)');
        newLines.add('gpu_mem=$recommendedGpuMem');
      }

      // Write updated config
      await File(configPath).writeAsString(newLines.join('\n'));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '✓ GPU memory split set to ${recommendedGpuMem}MB for $currentModel'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Re-run diagnostics to update results
      await _runDiagnostics();
    } catch (e) {
      _showErrorSnackBar('Error fixing memory split: $e');
    }
  }

  Future<void> _removeProblematicSettings() async {
    try {
      final configPath = await _getConfigPath();
      if (configPath == null) {
        _showErrorSnackBar('Could not find config.txt file');
        return;
      }

      // Create backup first
      await _createBackup(configPath);

      // Read current config
      final configContent = await File(configPath).readAsString();
      final lines = configContent.split('\n');
      final newLines = <String>[];
      int removedCount = 0;

      // List of potentially problematic settings
      final problematicSettings = [
        'avoid_warnings=',
        'force_turbo=',
        'temp_limit=',
        'initial_turbo=',
        'disable_splash=',
        'boot_delay=',
        'disable_overscan=',
        'overscan_',
        'framebuffer_',
        'max_usb_current=',
      ];

      for (final line in lines) {
        final trimmedLine = line.trim();
        bool isProblematic = false;

        for (final setting in problematicSettings) {
          if (trimmedLine.startsWith(setting)) {
            newLines.add(
                '# $line (removed by RPi Diagnostics - potentially problematic)');
            isProblematic = true;
            removedCount++;
            break;
          }
        }

        if (!isProblematic) {
          newLines.add(line);
        }
      }

      // Write updated config
      await File(configPath).writeAsString(newLines.join('\n'));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '✓ Removed $removedCount potentially problematic settings'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Re-run diagnostics to update results
      await _runDiagnostics();
    } catch (e) {
      _showErrorSnackBar('Error removing problematic settings: $e');
    }
  }

  Future<void> _restoreDefaultConfig() async {
    try {
      final configPath = await _getConfigPath();
      if (configPath == null) {
        _showErrorSnackBar('Could not find config.txt file');
        return;
      }

      // Create backup first
      await _createBackup(configPath);

      // Get current Pi model for model-specific defaults
      final currentModel = _selectedPiModel == 'auto'
          ? _detectedPiModel ?? 'Pi 4 Model B'
          : _selectedPiModel;
      final defaultConfig = _getDefaultConfigForModel(currentModel);

      // Write default config
      await File(configPath).writeAsString(defaultConfig);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✓ Restored default configuration for $currentModel'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Re-run diagnostics to update results
      await _runDiagnostics();
    } catch (e) {
      _showErrorSnackBar('Error restoring default config: $e');
    }
  }

  Future<void> _autoFixIssues() async {
    try {
      final autoFixableIssues =
          _issues.where((issue) => issue.canAutoFix).toList();
      if (autoFixableIssues.isEmpty) {
        _showErrorSnackBar('No auto-fixable issues found');
        return;
      }

      final configPath = await _getConfigPath();
      if (configPath == null) {
        _showErrorSnackBar('Could not find config.txt file');
        return;
      }

      // Create backup first
      await _createBackup(configPath);

      int fixedCount = 0;
      final currentModel = _selectedPiModel == 'auto'
          ? _detectedPiModel ?? 'Pi 4 Model B'
          : _selectedPiModel;
      final safeLimits = _getSafeLimitsForModel(currentModel);

      // Read current config
      final configContent = await File(configPath).readAsString();
      final lines = configContent.split('\n');
      final newLines = <String>[];

      for (final line in lines) {
        final trimmedLine = line.trim();
        String newLine = line;

        // Fix overclocking issues
        if (trimmedLine.startsWith('arm_freq=')) {
          final freq = int.tryParse(trimmedLine.split('=')[1]);
          if (freq != null && freq > safeLimits['max_cpu_freq']!) {
            newLine =
                'arm_freq=${safeLimits['max_cpu_freq']} # Fixed by RPi Diagnostics';
            fixedCount++;
          }
        } else if (trimmedLine.startsWith('gpu_freq=')) {
          final freq = int.tryParse(trimmedLine.split('=')[1]);
          if (freq != null && freq > safeLimits['max_gpu_freq']!) {
            newLine =
                'gpu_freq=${safeLimits['max_gpu_freq']} # Fixed by RPi Diagnostics';
            fixedCount++;
          }
        } else if (trimmedLine.startsWith('over_voltage=')) {
          final voltage = int.tryParse(trimmedLine.split('=')[1]);
          if (voltage != null && voltage > safeLimits['max_voltage']!) {
            newLine =
                'over_voltage=${safeLimits['max_voltage']} # Fixed by RPi Diagnostics';
            fixedCount++;
          }
        } else if (trimmedLine.startsWith('gpu_mem=')) {
          final mem = int.tryParse(trimmedLine.split('=')[1]);
          if (mem != null && mem > 512) {
            newLine =
                'gpu_mem=${_getRecommendedGpuMemory(currentModel)} # Fixed by RPi Diagnostics';
            fixedCount++;
          }
        }

        newLines.add(newLine);
      }

      // Add missing essential settings
      bool hasGpuMem =
          newLines.any((line) => line.trim().startsWith('gpu_mem='));
      if (!hasGpuMem) {
        newLines.add('');
        newLines.add('# Essential settings (added by RPi Diagnostics)');
        newLines.add('gpu_mem=${_getRecommendedGpuMemory(currentModel)}');
        fixedCount++;
      }

      // Write updated config
      await File(configPath).writeAsString(newLines.join('\n'));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('✓ Auto-fixed $fixedCount issues successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Re-run diagnostics to update results
      await _runDiagnostics();
    } catch (e) {
      _showErrorSnackBar('Error auto-fixing issues: $e');
    }
  }
}

// Helper classes
class DiagnosticTab {
  final String id;
  final String title;
  final IconData icon;

  DiagnosticTab({
    required this.id,
    required this.title,
    required this.icon,
  });
}

class DiagnosticIssue {
  final String title;
  final String description;
  final String severity;
  final bool canAutoFix;

  DiagnosticIssue({
    required this.title,
    required this.description,
    required this.severity,
    required this.canAutoFix,
  });
}

class SdCardInfo {
  final String devicePath;
  final String size;
  final String model;
  final String manufacturer;
  final String serialNumber;
  final String filesystem;

  SdCardInfo({
    required this.devicePath,
    required this.size,
    required this.model,
    required this.manufacturer,
    required this.serialNumber,
    required this.filesystem,
  });
}

class SectorTestProgress {
  final int totalSectors;
  final int testedSectors;
  final int badSectors;
  final int goodSectors;
  final Duration elapsed;
  final Duration? estimated;
  final List<SectorStatus> sectorMap;
  final bool isComplete;
  final bool isCancelled;

  SectorTestProgress({
    required this.totalSectors,
    required this.testedSectors,
    required this.badSectors,
    required this.goodSectors,
    required this.elapsed,
    this.estimated,
    required this.sectorMap,
    required this.isComplete,
    required this.isCancelled,
  });

  double get progressPercentage =>
      totalSectors > 0 ? (testedSectors / totalSectors) * 100 : 0;
}

enum SectorStatus {
  untested,
  good,
  bad,
  testing,
}

class SectorTestDialog extends StatefulWidget {
  final String devicePath;
  final SdCardInfo? sdCardInfo;
  final String sudoPassword;
  final Function(SectorTestProgress) onComplete;

  const SectorTestDialog({
    super.key,
    required this.devicePath,
    required this.sdCardInfo,
    required this.sudoPassword,
    required this.onComplete,
  });

  @override
  State<SectorTestDialog> createState() => _SectorTestDialogState();
}

class _SectorTestDialogState extends State<SectorTestDialog> {
  late SectorTestProgress _progress;
  late Timer _timer;
  Process? _testProcess;
  final Stopwatch _stopwatch = Stopwatch();
  SdCardInfo? _currentDeviceInfo;
  bool _showTerminal = false;
  final List<String> _terminalOutput = [];
  final ScrollController _terminalScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _currentDeviceInfo = widget.sdCardInfo;
    _initializeProgress();
    _startTest();
  }

  void _initializeProgress() {
    // Initialize with grid size for visualization
    final gridSize = 6400; // 80x80 grid

    // Try to estimate total sectors based on device size
    int estimatedSectors = 1000;
    if (_currentDeviceInfo != null) {
      final sizeStr = _currentDeviceInfo!.size;
      final sizeMatch = RegExp(r'(\d+\.?\d*)\s*GB').firstMatch(sizeStr);
      if (sizeMatch != null) {
        final sizeGB = double.tryParse(sizeMatch.group(1)!) ?? 1.0;
        // Rough estimate: 1GB ≈ 2,000,000 sectors (512 bytes each)
        estimatedSectors = (sizeGB * 2000000).round();
      }
    }

    _progress = SectorTestProgress(
      totalSectors: estimatedSectors,
      testedSectors: 0,
      badSectors: 0,
      goodSectors: 0,
      elapsed: Duration.zero,
      sectorMap: List.generate(gridSize, (index) => SectorStatus.untested),
      isComplete: false,
      isCancelled: false,
    );
  }

  @override
  void dispose() {
    _timer.cancel();
    _testProcess?.kill();
    _terminalScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxDialogHeight = screenHeight * 0.9; // Use 90% of screen height

    return Dialog(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 800,
          maxHeight: maxDialogHeight,
          minHeight: 600,
        ),
        child: Container(
          width: 800,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              const SizedBox(height: 16),
              if (_currentDeviceInfo != null) _buildSdCardInfo(),
              const SizedBox(height: 16),
              _buildProgressStats(),
              const SizedBox(height: 16),
              // Sector map temporarily hidden - will be implemented later
              // Expanded(child: _buildSectorMap()),
              // const SizedBox(height: 16),
              _buildProgressBar(),
              const SizedBox(height: 16),
              Flexible(
                  child: _buildTerminalDropdown()), // Make terminal flexible
              const SizedBox(height: 16),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.purple,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.storage, color: Colors.white, size: 24),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'SD Card Sector Test',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              Text(
                'Testing: ${widget.devicePath}',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSdCardInfo() {
    final info = _currentDeviceInfo!;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'SD Card Information',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
            ),
            const SizedBox(height: 6),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Size', info.size),
                ),
                Expanded(
                  child: _buildInfoItem('Model', info.model),
                ),
                Expanded(
                  child: _buildInfoItem('Manufacturer', info.manufacturer),
                ),
                Expanded(
                  child: _buildInfoItem('Filesystem', info.filesystem),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildProgressStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Pass',
            '${_progress.goodSectors}',
            Colors.green,
            Icons.check_circle,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Fail',
            '${_progress.badSectors}',
            Colors.red,
            Icons.error,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
      String label, String count, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: 2),
          Text(
            count,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectorMap() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Sector Map',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 80,
                  childAspectRatio: 1,
                  crossAxisSpacing: 0.5,
                  mainAxisSpacing: 0.5,
                ),
                itemCount: _progress.sectorMap.length,
                itemBuilder: (context, index) {
                  return Container(
                    decoration: BoxDecoration(
                      color: _getSectorColor(_progress.sectorMap[index]),
                      borderRadius: BorderRadius.circular(0.5),
                    ),
                    child: _progress.sectorMap[index] == SectorStatus.testing
                        ? Container(
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(0.5),
                            ),
                            child: const Center(
                              child: SizedBox(
                                width: 1,
                                height: 1,
                                child: CircularProgressIndicator(
                                  strokeWidth: 0.3,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          )
                        : null,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getSectorColor(SectorStatus status) {
    switch (status) {
      case SectorStatus.untested:
        return Colors.grey[300]!;
      case SectorStatus.good:
        return Colors.green;
      case SectorStatus.bad:
        return Colors.red;
      case SectorStatus.testing:
        return Colors.blue;
    }
  }

  Widget _buildProgressBar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Time Elapsed: ${_formatDuration(_progress.elapsed)}',
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            Text(
              'Remaining: ${_getEstimatedTimeText()}',
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: _progress.progressPercentage / 100,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  _progress.badSectors > 0 ? Colors.orange : Colors.green,
                ),
                minHeight: 8,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              '${_progress.progressPercentage.toStringAsFixed(1)}%',
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTerminalDropdown() {
    return Card(
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _showTerminal = !_showTerminal;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Icon(
                    Icons.terminal,
                    size: 20,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Terminal Output',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const Spacer(),
                  Icon(
                    _showTerminal ? Icons.expand_less : Icons.expand_more,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ),
          ),
          if (_showTerminal) _buildTerminalView(),
        ],
      ),
    );
  }

  Widget _buildTerminalView() {
    return Expanded(
      // Allow terminal view to take available space
      child: Container(
        margin: const EdgeInsets.fromLTRB(12, 0, 12, 12),
        decoration: BoxDecoration(
          color: const Color(0xFF0D1117), // Match app terminal background
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: _terminalOutput.isEmpty
              ? Center(
                  child: Text(
                    'Waiting for badblocks output...',
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 14,
                      fontFamily: 'Menlo',
                    ),
                  ),
                )
              : Scrollbar(
                  controller: _terminalScrollController,
                  thumbVisibility: true,
                  child: ListView.builder(
                    controller: _terminalScrollController,
                    padding: const EdgeInsets.all(12),
                    itemCount: _terminalOutput.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 1),
                        child: SelectableText(
                          _terminalOutput[index],
                          style: const TextStyle(
                            color: Color(
                                0xFFE8EAED), // White text to match app theme
                            fontSize: 14, // Larger font size
                            fontFamily: 'Menlo', // Match app terminal font
                            height: 1.2,
                          ),
                        ),
                      );
                    },
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (!_progress.isComplete && !_progress.isCancelled)
          TextButton(
            onPressed: _cancelTest,
            child: const Text('Cancel'),
          ),
        const SizedBox(width: 12),
        ElevatedButton(
          onPressed: _progress.isComplete || _progress.isCancelled
              ? () {
                  widget.onComplete(_progress);
                  Navigator.of(context).pop();
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
          ),
          child: Text(_progress.isComplete || _progress.isCancelled
              ? 'Close'
              : 'Testing...'),
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  String _getEstimatedTimeText() {
    if (_progress.estimated != null) {
      return _formatDuration(_progress.estimated!);
    } else if (_stopwatch.elapsedMilliseconds < 5000) {
      return "Initializing...";
    } else if (_progress.testedSectors == 0) {
      return "Starting test...";
    } else {
      return "Calculating...";
    }
  }

  void _startTest() {
    _stopwatch.start();

    // Start the timer for UI updates
    _timer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (mounted) {
        setState(() {
          _progress = SectorTestProgress(
            totalSectors: _progress.totalSectors,
            testedSectors: _progress.testedSectors,
            badSectors: _progress.badSectors,
            goodSectors: _progress.goodSectors,
            elapsed: _stopwatch.elapsed,
            estimated: _calculateEstimatedTime(),
            sectorMap: _progress.sectorMap,
            isComplete: _progress.isComplete,
            isCancelled: _progress.isCancelled,
          );
        });
      }
    });

    // Start the actual test process
    _runBadBlocksTest();
  }

  Duration? _calculateEstimatedTime() {
    final elapsedMs = _stopwatch.elapsedMilliseconds;

    // If we have tested some sectors, use actual rate
    if (_progress.testedSectors > 0 && elapsedMs > 1000) {
      final sectorsPerMs = _progress.testedSectors / elapsedMs;
      final remainingSectors = _progress.totalSectors - _progress.testedSectors;
      final estimatedRemainingMs = remainingSectors / sectorsPerMs;
      return Duration(milliseconds: estimatedRemainingMs.round());
    }

    // Early estimate based on typical badblocks performance
    // Badblocks typically tests at ~10-50 MB/s depending on device
    // Assume 20 MB/s average (40,000 sectors/second)
    if (_currentDeviceInfo != null && elapsedMs > 100) {
      final sizeStr = _currentDeviceInfo!.size;
      final sizeMatch = RegExp(r'(\d+\.?\d*)\s*GB').firstMatch(sizeStr);
      if (sizeMatch != null) {
        final sizeGB = double.tryParse(sizeMatch.group(1)!) ?? 1.0;
        // Estimate: 20 MB/s = 40,000 sectors/second
        final estimatedSeconds =
            (sizeGB * 1024 / 20).round(); // GB to MB, then divide by 20 MB/s
        return Duration(seconds: estimatedSeconds);
      }
    }

    return null;
  }

  void _cancelTest() {
    _testProcess?.kill();
    _timer.cancel();
    _stopwatch.stop();

    setState(() {
      _progress = SectorTestProgress(
        totalSectors: _progress.totalSectors,
        testedSectors: _progress.testedSectors,
        badSectors: _progress.badSectors,
        goodSectors: _progress.goodSectors,
        elapsed: _stopwatch.elapsed,
        estimated: _progress.estimated,
        sectorMap: _progress.sectorMap,
        isComplete: false,
        isCancelled: true,
      );
    });
  }

  Future<void> _runBadBlocksTest() async {
    try {
      // Run the real Python backend for sector testing
      await _runRealSectorTest();
    } catch (e) {
      _cancelTest();
    }
  }

  Future<void> _runRealSectorTest() async {
    try {
      // Add initial terminal output
      _addTerminalOutput('Initializing badblocks sector test...');
      _addTerminalOutput('Device: ${widget.devicePath}');
      _addTerminalOutput('');

      // Get device size for progress calculation
      final sizeResult =
          await Process.run('blockdev', ['--getsize64', widget.devicePath]);
      int totalSectors = 1000;
      if (sizeResult.exitCode == 0) {
        final sizeBytes = int.tryParse(sizeResult.stdout.toString().trim());
        if (sizeBytes != null) {
          totalSectors = (sizeBytes / 512).round(); // 512 bytes per sector
          final sizeGB = sizeBytes / (1024 * 1024 * 1024);
          _addTerminalOutput('Device size: ${sizeGB.toStringAsFixed(2)} GB');
          _addTerminalOutput('Total sectors: $totalSectors');

          // Update device info with correct size
          if (_currentDeviceInfo != null) {
            setState(() {
              _currentDeviceInfo = SdCardInfo(
                devicePath: _currentDeviceInfo!.devicePath,
                size: '${sizeGB.toStringAsFixed(1)} GB',
                model: _currentDeviceInfo!.model,
                manufacturer: _currentDeviceInfo!.manufacturer,
                serialNumber: _currentDeviceInfo!.serialNumber,
                filesystem: _currentDeviceInfo!.filesystem,
              );
            });
          }
        }
      } else {
        _addTerminalOutput('Warning: Could not determine device size');
      }

      // Update progress with real sector count
      setState(() {
        _progress = SectorTestProgress(
          totalSectors: totalSectors,
          testedSectors: 0,
          badSectors: 0,
          goodSectors: 0,
          elapsed: Duration.zero,
          sectorMap: _progress.sectorMap,
          isComplete: false,
          isCancelled: false,
        );
      });

      _addTerminalOutput('');
      _addTerminalOutput('Starting SD card sector test via Python backend...');

      // Run the Python backend script
      _testProcess = await Process.start(
        'python3',
        [
          'backend/sector_test.py',
          widget.devicePath,
          widget.sudoPassword,
        ],
        mode: ProcessStartMode.normal,
      );

      if (_testProcess == null) {
        throw Exception('Failed to start Python backend process');
      }

      // Listen to stdout for JSON progress updates from the Python script
      _testProcess!.stdout
          .transform(const SystemEncoding().decoder)
          .transform(const LineSplitter())
          .listen(
        (line) {
          try {
            final jsonMessage = json.decode(line);
            final type = jsonMessage['type'];
            final data = jsonMessage['data'];

            if (type == 'device_info') {
              // Update device info from backend
              if (mounted) {
                setState(() {
                  _currentDeviceInfo = SdCardInfo(
                    devicePath: data['device_path'] ?? widget.devicePath,
                    size: data['size'] ?? 'Unknown',
                    model: data['model'] ?? 'Unknown',
                    manufacturer: data['manufacturer'] ?? 'Unknown',
                    serialNumber: data['serial'] ?? 'Unknown',
                    filesystem: data['filesystem'] ?? 'Unknown',
                  );
                  _progress = SectorTestProgress(
                    totalSectors:
                        data['total_sectors'] ?? _progress.totalSectors,
                    testedSectors: _progress.testedSectors,
                    badSectors: _progress.badSectors,
                    goodSectors: _progress.goodSectors,
                    elapsed: _progress.elapsed,
                    sectorMap: _progress.sectorMap,
                    isComplete: _progress.isComplete,
                    isCancelled: _progress.isCancelled,
                  );
                });
              }
            } else if (type == 'progress') {
              if (mounted) {
                setState(() {
                  _progress = SectorTestProgress(
                    totalSectors: data['total_sectors'],
                    testedSectors: data['tested_sectors'],
                    badSectors: data['bad_sectors'],
                    goodSectors: data['good_sectors'],
                    elapsed: Duration(seconds: data['elapsed_seconds'].round()),
                    estimated: Duration(
                        seconds: data['estimated_remaining_seconds'].round()),
                    sectorMap: _updateSectorMap(data['tested_sectors'],
                        data['bad_sectors'], data['total_sectors']),
                    isComplete: data['is_complete'],
                    isCancelled: data['is_cancelled'],
                  );
                });
              }
            } else if (type == 'final_results') {
              if (mounted) {
                _completeTest(data);
              }
            } else if (type == 'error') {
              _addTerminalOutput('Backend Error: ${data['message']}');
              if (mounted) {
                _cancelTest();
              }
            }
          } catch (e) {
            // If it's not JSON, treat as regular terminal output
            _addTerminalOutput(line);
          }
        },
        onError: (error) {
          _addTerminalOutput('Stream Error: $error');
          if (mounted) {
            _cancelTest();
          }
        },
        onDone: () {
          // Process finished, if not already marked complete by final_results
          if (mounted && !_progress.isComplete && !_progress.isCancelled) {
            _addTerminalOutput('Backend process finished unexpectedly.');
            _completeTest(null); // Pass null to indicate unexpected completion
          }
        },
      );

      // Listen to stderr for raw badblocks output and errors
      _testProcess!.stderr
          .transform(const SystemEncoding().decoder)
          .transform(const LineSplitter())
          .listen(
        (line) {
          if (line.trim().isNotEmpty) {
            _addTerminalOutput(line);
          }
        },
        onError: (error) {
          _addTerminalOutput('Backend Stderr Error: $error');
        },
      );
    } catch (e) {
      if (mounted) {
        _addTerminalOutput('Error: $e');
        _cancelTest();
      }
    }
  }

  void _completeTest(Map<String, dynamic>? finalResults) {
    _timer.cancel();
    _stopwatch.stop();

    if (finalResults != null) {
      setState(() {
        _progress = SectorTestProgress(
          totalSectors: finalResults['total_sectors'],
          testedSectors: finalResults['tested_sectors'],
          badSectors: finalResults['bad_sectors'],
          goodSectors: finalResults['good_sectors'],
          elapsed: Duration(seconds: finalResults['elapsed_seconds'].round()),
          estimated: Duration.zero,
          sectorMap: _updateSectorMap(finalResults['tested_sectors'],
              finalResults['bad_sectors'], finalResults['total_sectors']),
          isComplete: true,
          isCancelled: finalResults['is_cancelled'],
        );
      });

      // Add completion message to terminal
      _addTerminalOutput('');
      _addTerminalOutput('=== Test Completed ===');
      _addTerminalOutput('Total sectors tested: ${_progress.testedSectors}');
      _addTerminalOutput('Bad sectors found: ${_progress.badSectors}');
      _addTerminalOutput('Good sectors: ${_progress.goodSectors}');
      _addTerminalOutput(
          'Test duration: ${_formatDuration(_progress.elapsed)}');
    } else {
      // Unexpected completion or cancellation
      _addTerminalOutput('');
      _addTerminalOutput('=== Test Interrupted / Completed Unexpectedly ===');
      setState(() {
        _progress = SectorTestProgress(
          totalSectors: _progress.totalSectors,
          testedSectors: _progress.testedSectors,
          badSectors: _progress.badSectors,
          goodSectors: _progress.goodSectors,
          elapsed: _stopwatch.elapsed,
          estimated: Duration.zero,
          sectorMap: _progress.sectorMap,
          isComplete: true, // Mark as complete even if unexpected
          isCancelled: true,
        );
      });
    }
  }

  List<SectorStatus> _updateSectorMap(
      int testedSectors, int badSectors, int totalSectors) {
    final gridSize = 6400; // 80x80 grid

    // Ensure testedSectors doesn't exceed totalSectors
    testedSectors = testedSectors.clamp(0, totalSectors);

    // Calculate how many grid cells should be filled based on progress
    final progressRatio = totalSectors > 0 ? testedSectors / totalSectors : 0.0;
    final filledCells = (gridSize * progressRatio).round();

    final sectorMap = List<SectorStatus>.generate(gridSize, (index) {
      if (index < filledCells) {
        // Distribute bad sectors visually
        // This is a simplification; actual bad sectors are reported by badblocks
        // For visualization, we can just mark some cells as bad if badSectors > 0
        if (badSectors > 0 &&
            (index % (gridSize ~/ (badSectors + 1).clamp(1, gridSize))) == 0 &&
            index < badSectors * 10) {
          // Scale bad sectors for visibility
          return SectorStatus.bad;
        } else {
          return SectorStatus.good;
        }
      } else if (index == filledCells && testedSectors < totalSectors) {
        return SectorStatus.testing;
      } else {
        return SectorStatus.untested;
      }
    });
    return sectorMap;
  }

  void _addTerminalOutput(String line) {
    if (!mounted) return;

    setState(() {
      _terminalOutput.add(line);

      // Keep only the last 100 lines to prevent memory issues
      if (_terminalOutput.length > 100) {
        _terminalOutput.removeAt(0);
      }
    });

    // Auto-scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_terminalScrollController.hasClients) {
        _terminalScrollController.animateTo(
          _terminalScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 100),
          curve: Curves.easeOut,
        );
      }
    });
  }
}
