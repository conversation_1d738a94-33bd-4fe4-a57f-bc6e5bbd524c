import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/app_state.dart';
import '../services/rpi_command_service.dart';

class WiFiManagerScreen extends StatefulWidget {
  final String deviceId;

  const WiFiManagerScreen({super.key, required this.deviceId});

  @override
  State<WiFiManagerScreen> createState() => _WiFiManagerScreenState();
}

class _WiFiManagerScreenState extends State<WiFiManagerScreen> {
  late RpiCommandService _commandService;
  List<WiFiNetwork> _availableNetworks = [];
  List<WiFiNetwork> _savedNetworks = [];
  bool _isScanning = false;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    final appState = Provider.of<AppState>(context, listen: false);
    _commandService = RpiCommandService(appState);
    _loadWiFiInfo();
  }

  Future<void> _loadWiFiInfo() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await Future.wait([
        _scanNetworks(),
        _loadSavedNetworks(),
      ]);
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _scanNetworks() async {
    setState(() {
      _isScanning = true;
    });

    try {
      final result = await _commandService.executeCommand(
        widget.deviceId,
        'sudo iwlist wlan0 scan | grep -E "(ESSID|Quality|Encryption)" | sed "N;N;s/\\n/|/g"',
        progressTitle: 'Scanning WiFi Networks',
        progressSubtitle: 'Searching for available networks...',
      );

      if (result.success) {
        final networks = <WiFiNetwork>[];
        final lines = result.stdout.split('\n');

        for (final line in lines) {
          if (line.trim().isEmpty) continue;

          final parts = line.split('|');
          if (parts.length >= 3) {
            final essidMatch = RegExp(r'ESSID:"([^"]*)"').firstMatch(parts[0]);
            final qualityMatch =
                RegExp(r'Quality=(\d+)/(\d+)').firstMatch(parts[1]);
            final encrypted = parts[2].contains('Encryption key:on');

            if (essidMatch != null && essidMatch.group(1)!.isNotEmpty) {
              final quality = qualityMatch != null
                  ? (int.parse(qualityMatch.group(1)!) /
                          int.parse(qualityMatch.group(2)!) *
                          100)
                      .round()
                  : 0;

              networks.add(WiFiNetwork(
                ssid: essidMatch.group(1)!,
                signalStrength: quality,
                isSecured: encrypted,
                isConnected: false,
                isSaved: false,
              ));
            }
          }
        }

        // Remove duplicates and sort by signal strength
        final uniqueNetworks = <String, WiFiNetwork>{};
        for (final network in networks) {
          if (!uniqueNetworks.containsKey(network.ssid) ||
              uniqueNetworks[network.ssid]!.signalStrength <
                  network.signalStrength) {
            uniqueNetworks[network.ssid] = network;
          }
        }

        setState(() {
          _availableNetworks = uniqueNetworks.values.toList()
            ..sort((a, b) => b.signalStrength.compareTo(a.signalStrength));
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error scanning networks: $e')),
        );
      }
    } finally {
      setState(() {
        _isScanning = false;
      });
    }
  }

  Future<void> _loadSavedNetworks() async {
    try {
      final result = await _commandService.executeCommand(
        widget.deviceId,
        'cat /etc/wpa_supplicant/wpa_supplicant.conf | grep -A 3 "network=" | grep ssid',
        showProgress: false,
      );

      if (result.success) {
        final savedNetworks = <WiFiNetwork>[];
        final lines = result.stdout.split('\n');

        for (final line in lines) {
          final ssidMatch = RegExp(r'ssid="([^"]*)"').firstMatch(line);
          if (ssidMatch != null) {
            savedNetworks.add(WiFiNetwork(
              ssid: ssidMatch.group(1)!,
              signalStrength: 0,
              isSecured: true,
              isConnected: false,
              isSaved: true,
            ));
          }
        }

        setState(() {
          _savedNetworks = savedNetworks;

          // Mark saved networks in available networks
          for (final saved in savedNetworks) {
            final index =
                _availableNetworks.indexWhere((n) => n.ssid == saved.ssid);
            if (index != -1) {
              _availableNetworks[index] =
                  _availableNetworks[index].copyWith(isSaved: true);
            }
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading saved networks: $e');
    }
  }

  void _connectToNetwork(WiFiNetwork network) {
    if (network.isSaved) {
      _connectToSavedNetwork(network);
    } else {
      _showPasswordDialog(network);
    }
  }

  Future<void> _connectToSavedNetwork(WiFiNetwork network) async {
    try {
      await _commandService.executeCommand(
        widget.deviceId,
        'sudo wpa_cli -i wlan0 select_network \$(sudo wpa_cli -i wlan0 list_networks | grep "${network.ssid}" | cut -f1)',
        progressTitle: 'Connecting to ${network.ssid}',
        progressSubtitle: 'Establishing connection...',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connected to ${network.ssid}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error connecting: $e')),
        );
      }
    }
  }

  void _showPasswordDialog(WiFiNetwork network) {
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Connect to ${network.ssid}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (network.isSecured) ...[
              const Text('This network requires a password:'),
              const SizedBox(height: 16),
              TextField(
                controller: passwordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Password',
                  border: OutlineInputBorder(),
                ),
              ),
            ] else
              const Text('Connect to this open network?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _addAndConnectNetwork(network, passwordController.text);
            },
            child: const Text('Connect'),
          ),
        ],
      ),
    );
  }

  Future<void> _addAndConnectNetwork(
      WiFiNetwork network, String password) async {
    try {
      // Add network to wpa_supplicant configuration
      final networkConfig = network.isSecured
          ? '''
network={
    ssid="${network.ssid}"
    psk="$password"
}'''
          : '''
network={
    ssid="${network.ssid}"
    key_mgmt=NONE
}''';

      await _commandService.executeCommand(
        widget.deviceId,
        'echo \'$networkConfig\' | sudo tee -a /etc/wpa_supplicant/wpa_supplicant.conf',
        progressTitle: 'Adding Network',
        progressSubtitle: 'Configuring ${network.ssid}...',
      );

      // Reconfigure wpa_supplicant
      await _commandService.executeCommand(
        widget.deviceId,
        'sudo wpa_cli -i wlan0 reconfigure',
        showProgress: false,
      );

      // Wait a moment and try to connect
      await Future.delayed(const Duration(seconds: 2));

      await _commandService.executeCommand(
        widget.deviceId,
        'sudo wpa_cli -i wlan0 select_network \$(sudo wpa_cli -i wlan0 list_networks | grep "${network.ssid}" | tail -1 | cut -f1)',
        progressTitle: 'Connecting to ${network.ssid}',
        progressSubtitle: 'Establishing connection...',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully connected to ${network.ssid}'),
            backgroundColor: Colors.green,
          ),
        );
        _loadWiFiInfo(); // Refresh the list
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error connecting: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('WiFi Manager'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          IconButton(
            icon: _isScanning
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.refresh),
            onPressed: _isScanning ? null : _scanNetworks,
            tooltip: 'Scan Networks',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error loading WiFi information'),
                      const SizedBox(height: 8),
                      Text(_error!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadWiFiInfo,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : DefaultTabController(
                  length: 2,
                  child: Column(
                    children: [
                      const TabBar(
                        tabs: [
                          Tab(icon: Icon(Icons.wifi), text: 'Available'),
                          Tab(icon: Icon(Icons.bookmark), text: 'Saved'),
                        ],
                      ),
                      Expanded(
                        child: TabBarView(
                          children: [
                            _buildAvailableNetworks(),
                            _buildSavedNetworks(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }

  Widget _buildAvailableNetworks() {
    if (_availableNetworks.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.wifi_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No networks found'),
            SizedBox(height: 8),
            Text('Click refresh to scan for networks'),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _scanNetworks,
      child: ListView.builder(
        itemCount: _availableNetworks.length,
        itemBuilder: (context, index) {
          final network = _availableNetworks[index];
          return _buildNetworkTile(network);
        },
      ),
    );
  }

  Widget _buildSavedNetworks() {
    if (_savedNetworks.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bookmark_border, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No saved networks'),
            SizedBox(height: 8),
            Text('Connect to networks to save them'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _savedNetworks.length,
      itemBuilder: (context, index) {
        final network = _savedNetworks[index];
        return _buildNetworkTile(network, showSignalStrength: false);
      },
    );
  }

  Widget _buildNetworkTile(WiFiNetwork network,
      {bool showSignalStrength = true}) {
    return ListTile(
      leading: Icon(
        network.isSecured ? Icons.wifi_lock : Icons.wifi,
        color: showSignalStrength
            ? _getSignalColor(network.signalStrength)
            : Theme.of(context).colorScheme.primary,
      ),
      title: Text(network.ssid),
      subtitle: Row(
        children: [
          if (showSignalStrength) ...[
            Text('${network.signalStrength}%'),
            const SizedBox(width: 8),
          ],
          if (network.isSecured) const Icon(Icons.lock, size: 16),
          if (network.isSaved) ...[
            const SizedBox(width: 4),
            const Icon(Icons.bookmark, size: 16, color: Colors.blue),
          ],
        ],
      ),
      trailing: network.isConnected
          ? const Icon(Icons.check_circle, color: Colors.green)
          : const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () => _connectToNetwork(network),
    );
  }

  Color _getSignalColor(int strength) {
    if (strength > 70) return Colors.green;
    if (strength > 40) return Colors.orange;
    return Colors.red;
  }
}

class WiFiNetwork {
  final String ssid;
  final int signalStrength;
  final bool isSecured;
  final bool isConnected;
  final bool isSaved;

  WiFiNetwork({
    required this.ssid,
    required this.signalStrength,
    required this.isSecured,
    required this.isConnected,
    required this.isSaved,
  });

  WiFiNetwork copyWith({
    String? ssid,
    int? signalStrength,
    bool? isSecured,
    bool? isConnected,
    bool? isSaved,
  }) {
    return WiFiNetwork(
      ssid: ssid ?? this.ssid,
      signalStrength: signalStrength ?? this.signalStrength,
      isSecured: isSecured ?? this.isSecured,
      isConnected: isConnected ?? this.isConnected,
      isSaved: isSaved ?? this.isSaved,
    );
  }
}
