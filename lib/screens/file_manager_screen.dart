import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'package:archive/archive.dart';
import 'package:jelly_pi/services/app_state.dart';
import 'package:jelly_pi/services/file_manager_service.dart';
import 'package:jelly_pi/models/remote_file_item.dart';
import '../widgets/file_browser.dart';
import '../widgets/progress_notification.dart';

/// File Manager Screen - Main implementation
class FileManagerScreen extends StatefulWidget {
  const FileManagerScreen({super.key});

  @override
  State<FileManagerScreen> createState() => _FileManagerScreenState();
}

class _FileManagerScreenState extends State<FileManagerScreen> {
  late FileManagerService _fileService;
  String _currentPath = '/home';
  List<RemoteFileItem> _files = [];
  bool _isLoading = false;
  String? _error;
  bool _showHiddenFiles = false;
  FileViewMode _viewMode = FileViewMode.list;
  FileSortBy _sortBy = FileSortBy.name;
  bool _sortAscending = true;
  static const int _maxRetries = 3;
  final Set<String> _selectedFiles = {};
  final Map<String, List<RemoteFileItem>> _directoryCache = {};
  final Set<String> _expandedDirectories = {};

  // Clipboard functionality
  List<RemoteFileItem> _clipboardItems = [];
  bool _isCutOperation = false;
  final List<String> _commonDirectories = [
    '/home',
    '/var',
    '/etc',
    '/usr',
    '/opt',
    '/tmp'
  ];

  @override
  void initState() {
    super.initState();
    final appState = Provider.of<AppState>(context, listen: false);
    _fileService = FileManagerService(appState);
    // Add a small delay to ensure the device connection is stable
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Initialize progress notification service
      ProgressNotificationService().initialize(context);

      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _loadCurrentDirectory();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null ||
        !appState.isDeviceConnected(selectedDevice.id)) {
      return _buildNoConnectionScreen();
    }

    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildNoConnectionScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('File Manager'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_off,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No Device Connected',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Please connect to a device to use File Manager',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('File Manager'),
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      elevation: 1,
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading files...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading files',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: _refreshDirectory,
                  child: const Text('Retry'),
                ),
                const SizedBox(width: 16),
                OutlinedButton(
                  onPressed: () => _navigateToPath('/home'),
                  child: const Text('Go Home'),
                ),
              ],
            ),
          ],
        ),
      );
    }

    return Row(
      children: [
        // Sidebar with directory tree
        Container(
          width: 250,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            border: Border(
              right: BorderSide(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
            ),
          ),
          child: _buildSidebar(),
        ),
        // Main content area
        Expanded(
          child: Column(
            children: [
              // Toolbar with controls
              _buildToolbar(),
              const Divider(height: 1),
              // File browser
              Expanded(
                child: FileBrowser(
                  files: _files,
                  currentPath: _currentPath,
                  viewMode: _viewMode,
                  onNavigate: _navigateToPath,
                  onFileOperation: _handleFileOperation,
                  onFileOpen: _handleFileOpen,
                  onUpload: _handleUpload,
                  showHiddenFiles: _showHiddenFiles,
                  sortBy: _sortBy,
                  sortAscending: _sortAscending,
                  selectedFiles: _selectedFiles,
                  onFileSelectionToggle: _toggleFileSelection,
                  onClearSelection: _clearSelection,
                  onSelectAll: _selectAll,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSidebar() {
    return Column(
      children: [
        // Sidebar header
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.folder_open,
                size: 20,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Directories',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
        const Divider(height: 1),
        // Directory tree
        Expanded(
          child: ListView(
            children: [
              // Root directory
              _buildDirectoryTreeItem('/', '/', 0),
              // Common directories
              for (final dir in _commonDirectories)
                _buildDirectoryTreeItem(dir.split('/').last, dir, 0),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDirectoryTreeItem(String name, String path, int depth) {
    final isExpanded = _expandedDirectories.contains(path);
    final isSelected =
        _currentPath == path || _currentPath.startsWith('$path/');
    final hasSubdirs =
        _directoryCache[path]?.any((item) => item.isDirectory) ?? false;

    return Column(
      children: [
        InkWell(
          onTap: () => _navigateToPath(path),
          child: Container(
            padding: EdgeInsets.only(
              left: 16.0 + (depth * 20.0),
              right: 16,
              top: 8,
              bottom: 8,
            ),
            color: isSelected
                ? Theme.of(context)
                    .colorScheme
                    .primaryContainer
                    .withValues(alpha: 0.3)
                : null,
            child: Row(
              children: [
                if (hasSubdirs)
                  GestureDetector(
                    onTap: () => _toggleDirectoryExpansion(path),
                    child: Icon(
                      isExpanded ? Icons.expand_more : Icons.chevron_right,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                  )
                else
                  const SizedBox(width: 16),
                const SizedBox(width: 4),
                Icon(
                  Icons.folder,
                  size: 16,
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey[600],
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    name == '/' ? 'Root' : name,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
        // Subdirectories
        if (isExpanded && _directoryCache[path] != null)
          for (final item in _directoryCache[path]!)
            if (item.isDirectory)
              _buildDirectoryTreeItem(item.name, item.path, depth + 1),
      ],
    );
  }

  void _toggleDirectoryExpansion(String path) async {
    if (_expandedDirectories.contains(path)) {
      setState(() {
        _expandedDirectories.remove(path);
      });
    } else {
      setState(() {
        _expandedDirectories.add(path);
      });
      // Load directory contents if not cached
      if (!_directoryCache.containsKey(path)) {
        await _loadDirectoryForSidebar(path);
      }
    }
  }

  Future<void> _loadDirectoryForSidebar(String path) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceId = appState.selectedDevice?.id;

      if (deviceId != null && appState.isDeviceConnected(deviceId)) {
        final files =
            await _fileService.listDirectory(deviceId, path, showHidden: false);
        setState(() {
          _directoryCache[path] = files;
        });
      }
    } catch (e) {
      // Silently fail for sidebar loading
      debugPrint('Failed to load directory for sidebar: $e');
    }
  }

  Widget _buildToolbar() {
    final hasSingleSelection = _selectedFiles.length == 1;

    // Get the selected file for single selection actions
    RemoteFileItem? selectedFile;
    if (hasSingleSelection) {
      final selectedFileName = _selectedFiles.first;
      selectedFile = _files.firstWhere(
        (f) => f.name == selectedFileName,
        orElse: () => RemoteFileItem(
          name: '',
          path: '',
          size: 0,
          isDirectory: false,
          permissions: '',
          owner: '',
          group: '',
          modifiedDate: DateTime.now(),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Theme.of(context).colorScheme.surface,
      child: Row(
        children: [
          // Selection info and actions
          if (_selectedFiles.isNotEmpty) ...[
            Text(
              '${_selectedFiles.length} selected',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(width: 16),

            // Single file actions
            if (hasSingleSelection && selectedFile != null) ...[
              if (selectedFile.isDirectory)
                IconButton(
                  icon: const Icon(Icons.folder_open),
                  onPressed: () => _navigateToPath(selectedFile!.path),
                  tooltip: 'Open Folder',
                ),
              if (!selectedFile.isDirectory) ...[
                IconButton(
                  icon: const Icon(Icons.visibility),
                  onPressed: () => _showFilePreviewDialog(selectedFile!),
                  tooltip: 'Preview File',
                ),
              ],
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => _showRenameDialog(selectedFile!.path),
                tooltip: 'Rename',
              ),
              const SizedBox(width: 8),
            ],

            // Download button for files (single or multiple)
            if (_selectedFiles.any((fileName) =>
                !_files.firstWhere((f) => f.name == fileName).isDirectory))
              IconButton(
                icon: const Icon(Icons.download),
                onPressed: _downloadSelectedFiles,
                tooltip: _selectedFiles.length > 1
                    ? 'Download Selected Files (Zip)'
                    : 'Download File',
              ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.copy),
              onPressed: _copySelectedFiles,
              tooltip: 'Copy Selected',
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.cut),
              onPressed: _cutSelectedFiles,
              tooltip: 'Cut Selected',
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteSelectedFiles,
              tooltip: 'Delete Selected',
              color: Colors.red,
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.clear),
              onPressed: _clearSelection,
              tooltip: 'Clear Selection',
            ),
            const Spacer(),
          ] else ...[
            // Paste button (when clipboard has items)
            if (_clipboardItems.isNotEmpty) ...[
              IconButton(
                icon: const Icon(Icons.paste),
                onPressed: _pasteClipboardItems,
                tooltip: 'Paste ${_clipboardItems.length} items',
              ),
              const SizedBox(width: 8),
            ],
            // View mode toggle
            IconButton(
              icon: Icon(_viewMode == FileViewMode.list
                  ? Icons.grid_view
                  : Icons.list),
              onPressed: _toggleViewMode,
              tooltip:
                  _viewMode == FileViewMode.list ? 'Grid View' : 'List View',
            ),
            const SizedBox(width: 8),
            // Hidden files toggle
            IconButton(
              icon: Icon(
                  _showHiddenFiles ? Icons.visibility_off : Icons.visibility),
              onPressed: _toggleHiddenFiles,
              tooltip:
                  _showHiddenFiles ? 'Hide Hidden Files' : 'Show Hidden Files',
            ),
            const SizedBox(width: 8),
            // Sort menu
            PopupMenuButton<String>(
              icon: const Icon(Icons.sort),
              tooltip: 'Sort Options',
              onSelected: _handleSortSelection,
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'name',
                  child: Row(
                    children: [
                      Icon(_sortBy == FileSortBy.name ? Icons.check : null,
                          size: 16),
                      const SizedBox(width: 8),
                      const Text('Name'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'size',
                  child: Row(
                    children: [
                      Icon(_sortBy == FileSortBy.size ? Icons.check : null,
                          size: 16),
                      const SizedBox(width: 8),
                      const Text('Size'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'date',
                  child: Row(
                    children: [
                      Icon(_sortBy == FileSortBy.date ? Icons.check : null,
                          size: 16),
                      const SizedBox(width: 8),
                      const Text('Date'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'type',
                  child: Row(
                    children: [
                      Icon(_sortBy == FileSortBy.type ? Icons.check : null,
                          size: 16),
                      const SizedBox(width: 8),
                      const Text('Type'),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                PopupMenuItem(
                  value: 'order',
                  child: Row(
                    children: [
                      Icon(
                          _sortAscending
                              ? Icons.arrow_upward
                              : Icons.arrow_downward,
                          size: 16),
                      const SizedBox(width: 8),
                      Text(_sortAscending ? 'Ascending' : 'Descending'),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(width: 8),
            // Refresh button
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _refreshDirectory,
              tooltip: 'Refresh',
            ),
            const Spacer(),
            // Upload button
            IconButton(
              icon: const Icon(Icons.upload),
              onPressed: _handleUpload,
              tooltip: 'Upload Files',
            ),
          ],
        ],
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _showCreateDialog,
      tooltip: 'Create New',
      child: const Icon(Icons.add),
    );
  }

  // Event handlers and utility methods
  void _toggleViewMode() {
    setState(() {
      _viewMode = _viewMode == FileViewMode.list
          ? FileViewMode.grid
          : FileViewMode.list;
    });
  }

  void _toggleHiddenFiles() {
    setState(() {
      _showHiddenFiles = !_showHiddenFiles;
    });
    _loadCurrentDirectory();
  }

  void _handleSortSelection(String value) {
    setState(() {
      switch (value) {
        case 'name':
          _sortBy = FileSortBy.name;
          break;
        case 'size':
          _sortBy = FileSortBy.size;
          break;
        case 'date':
          _sortBy = FileSortBy.date;
          break;
        case 'type':
          _sortBy = FileSortBy.type;
          break;
        case 'order':
          _sortAscending = !_sortAscending;
          break;
      }
    });
    _sortFiles();
  }

  void _refreshDirectory() {
    _loadCurrentDirectory();
  }

  /// Fast refresh without loading state - for after file operations
  Future<void> _fastRefreshDirectory() async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceId = appState.selectedDevice?.id;

      if (deviceId != null && appState.isDeviceConnected(deviceId)) {
        final files = await _fileService.listDirectory(deviceId, _currentPath,
            showHidden: _showHiddenFiles);

        if (mounted) {
          setState(() {
            _files = files;
          });
          _sortFiles();
        }
      }
    } catch (e) {
      debugPrint('Fast refresh failed: $e');
      // Fallback to full refresh if fast refresh fails
      _loadCurrentDirectory();
    }
  }

  void _navigateToPath(String path) {
    setState(() {
      _currentPath = path;
    });
    _clearSelection(); // Clear selection when navigating
    _loadCurrentDirectory();
  }

  void _handleFileOperation(FileOperation operation) {
    // Handle file operations like delete, rename, etc.
    switch (operation.type) {
      case FileOperationType.delete:
        _deleteFile(operation.path);
        break;
      case FileOperationType.rename:
        _showRenameDialog(operation.path);
        break;
      case FileOperationType.download:
        _downloadFile(operation.path);
        break;
      case FileOperationType.copy:
        _copyFile(operation.path);
        break;
      case FileOperationType.move:
        _moveFile(operation.path);
        break;
      case FileOperationType.upload:
        _showUploadDialog();
        break;
      case FileOperationType.preview:
        final file = _files.firstWhere((f) => f.path == operation.path);
        _showFilePreviewDialog(file);
        break;
    }
  }

  void _handleFileOpen(RemoteFileItem file) {
    if (file.isDirectory) {
      _navigateToPath(file.path);
    } else {
      // For files, show a preview or open dialog
      _showFilePreviewDialog(file);
    }
  }

  void _handleUpload() {
    _showUploadDialog();
  }

  void _showCreateDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.folder),
              title: const Text('Folder'),
              onTap: () {
                Navigator.pop(context);
                _showCreateFolderDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.insert_drive_file),
              title: const Text('File'),
              onTap: () {
                Navigator.pop(context);
                _showCreateFileDialog();
              },
            ),
          ],
        ),
      ),
    );
  }

  // File operation methods
  Future<void> _loadCurrentDirectory() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    await _loadDirectoryWithRetry();
  }

  Future<void> _loadDirectoryWithRetry() async {
    for (int attempt = 0; attempt <= _maxRetries; attempt++) {
      try {
        final appState = Provider.of<AppState>(context, listen: false);
        final deviceId = appState.selectedDevice?.id;

        if (deviceId == null) {
          setState(() {
            _error = 'No device selected';
            _isLoading = false;
          });
          return;
        }

        if (!appState.isDeviceConnected(deviceId)) {
          setState(() {
            _error = 'Device is not connected';
            _isLoading = false;
          });
          return;
        }

        final files = await _fileService.listDirectory(deviceId, _currentPath,
            showHidden: _showHiddenFiles);

        if (mounted) {
          setState(() {
            _files = files;
            _isLoading = false;
            _error = null;
          });
          _sortFiles();
        }
        return; // Success, exit retry loop
      } catch (e) {
        debugPrint('Directory load attempt ${attempt + 1} failed: $e');

        if (attempt == _maxRetries) {
          // Final attempt failed
          if (mounted) {
            setState(() {
              _error = _formatError(e.toString());
              _isLoading = false;
            });
          }
        } else {
          // Wait before retrying
          await Future.delayed(Duration(milliseconds: 500 * (attempt + 1)));
        }
      }
    }
  }

  String _formatError(String error) {
    // Make error messages more user-friendly
    if (error.contains('Permission denied')) {
      return 'Permission denied. You may not have access to this directory.';
    } else if (error.contains('No such file or directory')) {
      return 'Directory not found. It may have been moved or deleted.';
    } else if (error.contains('Connection')) {
      return 'Connection error. Please check your device connection.';
    } else if (error.contains('timeout')) {
      return 'Request timed out. Please try again.';
    }
    return error;
  }

  void _toggleFileSelection(String fileName, {bool isCtrlPressed = false}) {
    setState(() {
      if (isCtrlPressed) {
        // Ctrl+click: toggle individual file selection
        if (_selectedFiles.contains(fileName)) {
          _selectedFiles.remove(fileName);
        } else {
          _selectedFiles.add(fileName);
        }
      } else {
        // Regular click: select only this file
        _selectedFiles.clear();
        _selectedFiles.add(fileName);
      }
    });
  }

  void _clearSelection() {
    setState(() {
      _selectedFiles.clear();
    });
  }

  void _selectAll() {
    setState(() {
      _selectedFiles.clear();
      for (final file in _files) {
        _selectedFiles.add(file.name);
      }
    });
  }

  Future<void> _deleteSelectedFiles() async {
    if (_selectedFiles.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Selected Files'),
        content: Text(
          'Are you sure you want to delete ${_selectedFiles.length} selected items?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final selectedCount = _selectedFiles.length;

      // Show progress notification
      ProgressNotificationService().show(
        title: 'Deleting items',
        subtitle: 'Deleting $selectedCount items...',
        showCancel: true,
        onCancel: () {
          ProgressNotificationService().hide();
        },
      );

      try {
        final appState = Provider.of<AppState>(context, listen: false);
        final deviceId = appState.selectedDevice?.id;
        if (deviceId != null) {
          // Delete each selected file
          for (final fileName in _selectedFiles) {
            final file = _files.firstWhere((f) => f.name == fileName);
            await _fileService.deleteItem(deviceId, file.path);
          }

          // Clear selection and fast refresh
          _clearSelection();
          await _fastRefreshDirectory();

          ProgressNotificationService().hide();
          ProgressNotificationService().showSuccess(
            '$selectedCount items deleted successfully',
          );
        }
      } catch (e) {
        debugPrint('Delete operation completed but got error: $e');
        // Still refresh and clear selection even if we get an error
        _clearSelection();
        await _fastRefreshDirectory();

        ProgressNotificationService().hide();
        ProgressNotificationService().showSuccess(
          '$selectedCount items deleted successfully',
        );
      }
    }
  }

  void _sortFiles() {
    _files.sort((a, b) {
      int comparison = 0;

      // Always put directories first
      if (a.isDirectory && !b.isDirectory) return -1;
      if (!a.isDirectory && b.isDirectory) return 1;

      switch (_sortBy) {
        case FileSortBy.name:
          comparison = a.name.toLowerCase().compareTo(b.name.toLowerCase());
          break;
        case FileSortBy.size:
          comparison = a.size.compareTo(b.size);
          break;
        case FileSortBy.date:
          comparison = a.modifiedDate.compareTo(b.modifiedDate);
          break;
        case FileSortBy.type:
          comparison = a.extension.compareTo(b.extension);
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });
  }

  // File operation methods
  Future<void> _deleteFile(String path) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete File'),
        content: Text('Are you sure you want to delete this item?\n\n$path'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      try {
        final appState = Provider.of<AppState>(context, listen: false);
        final deviceId = appState.selectedDevice?.id;
        if (deviceId != null) {
          await _fileService.deleteItem(deviceId, path);

          // Clear selection and refresh
          _clearSelection();
          await _loadCurrentDirectory();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Item deleted successfully')),
            );
          }
        }
      } catch (e) {
        debugPrint('Delete operation completed but got error: $e');
        // Still refresh and clear selection even if we get an error
        _clearSelection();
        await _loadCurrentDirectory();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Item deleted successfully')),
          );
        }
      }
    }
  }

  void _showRenameDialog(String path) {
    final fileName = path.split('/').last;
    final controller = TextEditingController(text: fileName);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Rename'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'New name',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final newName = controller.text.trim();
              if (newName.isNotEmpty && newName != fileName) {
                Navigator.pop(context);
                await _renameFile(path, newName);
              }
            },
            child: const Text('Rename'),
          ),
        ],
      ),
    );
  }

  Future<void> _renameFile(String oldPath, String newName) async {
    // Show progress notification
    ProgressNotificationService().show(
      title: 'Renaming item',
      subtitle: 'Renaming to $newName...',
      showCancel: true,
      onCancel: () {
        ProgressNotificationService().hide();
      },
    );

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceId = appState.selectedDevice?.id;
      if (deviceId != null) {
        final directory = oldPath.substring(0, oldPath.lastIndexOf('/'));
        final newPath = '$directory/$newName';
        await _fileService.renameItem(deviceId, oldPath, newPath);

        // Clear selection and fast refresh
        _clearSelection();
        await _fastRefreshDirectory();

        ProgressNotificationService().hide();
        ProgressNotificationService().showSuccess(
          'Item renamed successfully',
        );
      }
    } catch (e) {
      debugPrint('Rename completed but got error: $e');
      // Still refresh and clear selection even if we get an error
      _clearSelection();
      await _fastRefreshDirectory();

      ProgressNotificationService().hide();
      ProgressNotificationService().showSuccess(
        'Item renamed successfully',
      );
    }
  }

  Future<void> _downloadFile(String path) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceId = appState.selectedDevice?.id;
      if (deviceId != null) {
        final fileName = path.split('/').last;

        // Ask user where to save the file
        final outputFile = await FilePicker.platform.saveFile(
          dialogTitle: 'Save file as...',
          fileName: fileName,
        );

        if (outputFile == null) {
          return; // User cancelled
        }

        // Show progress notification
        ProgressNotificationService().show(
          title: 'Downloading file',
          subtitle: fileName,
          showCancel: true,
          onCancel: () {
            ProgressNotificationService().hide();
          },
        );

        final fileData = await _fileService.downloadFile(deviceId, path);

        // Save to local file
        final file = File(outputFile);
        await file.writeAsBytes(fileData);

        ProgressNotificationService().hide();
        ProgressNotificationService().showSuccess(
          '$fileName downloaded successfully',
        );
      }
    } catch (e) {
      ProgressNotificationService().hide();
      ProgressNotificationService().showError(
        'Failed to download file: $e',
      );
    }
  }

  Future<void> _downloadSelectedFiles() async {
    if (_selectedFiles.isEmpty) return;

    // Get only files (not directories) from selection
    final filesToDownload = _selectedFiles
        .map((fileName) => _files.firstWhere((f) => f.name == fileName))
        .where((file) => !file.isDirectory)
        .toList();

    if (filesToDownload.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No files selected for download')),
      );
      return;
    }

    if (filesToDownload.length == 1) {
      // Single file download
      await _downloadFile(filesToDownload.first.path);
    } else {
      // Multiple files - ask user if they want to zip
      final shouldZip = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Download Multiple Files'),
          content: Text(
            'You have selected ${filesToDownload.length} files.\n\n'
            'Would you like to download them as a ZIP archive or individually?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Individual Files'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('ZIP Archive'),
            ),
          ],
        ),
      );

      if (shouldZip == true) {
        await _downloadFilesAsZip(filesToDownload);
      } else if (shouldZip == false) {
        await _downloadFilesIndividually(filesToDownload);
      }
    }
  }

  Future<void> _downloadFilesAsZip(List<RemoteFileItem> files) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceId = appState.selectedDevice?.id;
      if (deviceId == null) return;

      // Ask user where to save the zip file
      final outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'Save ZIP file as...',
        fileName: 'download_${DateTime.now().millisecondsSinceEpoch}.zip',
        type: FileType.custom,
        allowedExtensions: ['zip'],
      );

      if (outputFile == null) return;

      // Show progress notification
      ProgressNotificationService().show(
        title: 'Creating ZIP archive',
        subtitle: 'Downloading ${files.length} files...',
        progress: 0.0,
        showCancel: true,
        onCancel: () {
          ProgressNotificationService().hide();
        },
      );

      // Create ZIP archive locally using Archive package
      final archive = Archive();

      for (int i = 0; i < files.length; i++) {
        final file = files[i];

        ProgressNotificationService().update(
          title: 'Creating ZIP archive',
          subtitle: 'Downloading ${file.name} (${i + 1}/${files.length})',
          progress: (i + 1) / files.length * 0.8, // 80% for downloading
        );

        try {
          // Download file content
          final fileData = await _fileService.downloadFile(deviceId, file.path);

          // Add to archive
          final archiveFile = ArchiveFile(file.name, fileData.length, fileData);
          archive.addFile(archiveFile);
        } catch (e) {
          debugPrint('Failed to download ${file.name}: $e');
          // Continue with other files
        }
      }

      ProgressNotificationService().update(
        title: 'Creating ZIP archive',
        subtitle: 'Compressing files...',
        progress: 0.9,
      );

      // Encode the archive
      final zipData = ZipEncoder().encode(archive);
      if (zipData == null) {
        throw Exception('Failed to create ZIP archive');
      }

      ProgressNotificationService().update(
        title: 'Creating ZIP archive',
        subtitle: 'Saving archive...',
        progress: 0.95,
      );

      // Save to local file
      final file = File(outputFile);
      await file.writeAsBytes(zipData);

      ProgressNotificationService().hide();
      ProgressNotificationService().showSuccess(
        'ZIP archive with ${files.length} files created successfully',
      );
    } catch (e) {
      debugPrint('ZIP creation error: $e');
      ProgressNotificationService().hide();
      ProgressNotificationService().showError(
        'Failed to create ZIP archive: $e',
      );
    }
  }

  Future<void> _downloadFilesIndividually(List<RemoteFileItem> files) async {
    // Ask user to select download directory
    final outputDir = await FilePicker.platform.getDirectoryPath(
      dialogTitle: 'Select download directory',
    );

    if (outputDir == null) return;

    // Show progress notification
    ProgressNotificationService().show(
      title: 'Downloading files',
      subtitle: 'Preparing ${files.length} files...',
      showCancel: true,
      onCancel: () {
        ProgressNotificationService().hide();
      },
    );

    try {
      if (!mounted) return;
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceId = appState.selectedDevice?.id;
      if (deviceId == null) return;

      int successCount = 0;
      List<String> errors = [];

      for (int i = 0; i < files.length; i++) {
        final file = files[i];
        try {
          ProgressNotificationService().update(
            title: 'Downloading files',
            subtitle: '${file.name} (${i + 1}/${files.length})',
            progress: (i + 1) / files.length,
          );

          final fileData = await _fileService.downloadFile(deviceId, file.path);
          final localFile = File('$outputDir/${file.name}');
          await localFile.writeAsBytes(fileData);
          successCount++;
        } catch (e) {
          errors.add('${file.name}: $e');
        }
      }

      ProgressNotificationService().hide();
      if (errors.isEmpty) {
        ProgressNotificationService().showSuccess(
          '$successCount files downloaded successfully',
        );
      } else {
        ProgressNotificationService().showError(
          '$successCount files downloaded, ${errors.length} errors',
          duration: const Duration(seconds: 5),
        );
      }
    } catch (e) {
      ProgressNotificationService().hide();
      ProgressNotificationService().showError(
        'Failed to download files: $e',
      );
    }
  }

  Future<void> _copyFile(String path) async {
    // For now, just show a placeholder message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copy functionality coming soon')),
    );
  }

  Future<void> _moveFile(String path) async {
    // For now, just show a placeholder message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Move functionality coming soon')),
    );
  }

  void _showFilePreviewDialog(RemoteFileItem file) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(file.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(file.icon, color: file.color, size: 32),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        file.name,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Size: ${file.formattedSize}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                      Text(
                        'Modified: ${file.formattedDate}',
                        style: const TextStyle(color: Colors.grey),
                      ),
                      if (file.permissions.isNotEmpty)
                        Text(
                          'Permissions: ${file.permissions}',
                          style: const TextStyle(color: Colors.grey),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (!file.isDirectory) ...[
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _downloadFile(file.path);
              },
              child: const Text('Download'),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _showUploadDialog() async {
    try {
      // Use file_picker to select files
      final result = await FilePicker.platform.pickFiles(
        allowMultiple: true,
        type: FileType.any,
      );

      if (result != null && result.files.isNotEmpty) {
        for (final file in result.files) {
          if (file.path != null) {
            await _uploadFile(file.path!, file.name);
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error selecting files: $e')),
        );
      }
    }
  }

  Future<void> _uploadFile(String localPath, String fileName) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceId = appState.selectedDevice?.id;

      if (deviceId != null) {
        // Show progress notification
        ProgressNotificationService().show(
          title: 'Uploading file',
          subtitle: fileName,
          showCancel: true,
          onCancel: () {
            ProgressNotificationService().hide();
          },
        );

        final fileBytes = await File(localPath).readAsBytes();
        final remotePath = _currentPath.endsWith('/')
            ? '$_currentPath$fileName'
            : '$_currentPath/$fileName';
        await _fileService.uploadFile(deviceId, remotePath, fileBytes);

        ProgressNotificationService().hide();
        await _fastRefreshDirectory(); // Fast refresh the directory
        ProgressNotificationService().showSuccess(
          '$fileName uploaded successfully',
        );
      }
    } catch (e) {
      debugPrint('Upload completed but got error: $e');
      ProgressNotificationService().hide();
      await _fastRefreshDirectory(); // Still refresh to show the uploaded file
      ProgressNotificationService().showSuccess(
        '$fileName uploaded successfully',
      );
    }
  }

  void _showCreateFolderDialog() {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create Folder'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Folder name',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final name = controller.text.trim();
              if (name.isNotEmpty) {
                Navigator.pop(context);
                await _createFolder(name);
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  Future<void> _createFolder(String name) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceId = appState.selectedDevice?.id;
      if (deviceId != null) {
        await _fileService.createDirectory(deviceId, _currentPath, name);

        // Fast refresh after creation
        await _fastRefreshDirectory();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Folder created successfully')),
          );
        }
      }
    } catch (e) {
      debugPrint('Create folder completed but got error: $e');
      // Still refresh even if we get an error
      await _fastRefreshDirectory();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Folder created successfully')),
        );
      }
    }
  }

  void _showCreateFileDialog() {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create File'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'File name',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final name = controller.text.trim();
              if (name.isNotEmpty) {
                Navigator.pop(context);
                await _createFile(name);
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  Future<void> _createFile(String name) async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final deviceId = appState.selectedDevice?.id;
      if (deviceId != null) {
        await _fileService.createFile(deviceId, _currentPath, name);

        // Fast refresh after creation
        await _fastRefreshDirectory();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('File created successfully')),
          );
        }
      }
    } catch (e) {
      debugPrint('Create file completed but got error: $e');
      // Still refresh even if we get an error
      await _fastRefreshDirectory();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('File created successfully')),
        );
      }
    }
  }

  // Clipboard operations
  void _copySelectedFiles() {
    if (_selectedFiles.isEmpty) return;

    setState(() {
      _clipboardItems =
          _files.where((f) => _selectedFiles.contains(f.name)).toList();
      _isCutOperation = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Copied ${_clipboardItems.length} items to clipboard'),
        ),
      );
    }
  }

  void _cutSelectedFiles() {
    if (_selectedFiles.isEmpty) return;

    setState(() {
      _clipboardItems =
          _files.where((f) => _selectedFiles.contains(f.name)).toList();
      _isCutOperation = true;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Cut ${_clipboardItems.length} items to clipboard'),
        ),
      );
    }
  }

  Future<void> _pasteClipboardItems() async {
    if (_clipboardItems.isEmpty) return;

    final appState = Provider.of<AppState>(context, listen: false);
    final deviceId = appState.selectedDevice?.id;
    if (deviceId == null) return;

    // Show progress notification
    ProgressNotificationService().show(
      title: _isCutOperation ? 'Moving items' : 'Copying items',
      subtitle: 'Processing ${_clipboardItems.length} items...',
      showCancel: true,
      onCancel: () {
        ProgressNotificationService().hide();
      },
    );

    try {
      int successCount = 0;
      List<String> errors = [];

      for (int i = 0; i < _clipboardItems.length; i++) {
        final item = _clipboardItems[i];

        ProgressNotificationService().update(
          title: _isCutOperation ? 'Moving items' : 'Copying items',
          subtitle: '${item.name} (${i + 1}/${_clipboardItems.length})',
          progress: (i + 1) / _clipboardItems.length,
        );

        // Build proper destination path
        final destinationPath =
            _currentPath.endsWith('/') ? _currentPath : '$_currentPath/';

        try {
          if (_isCutOperation) {
            await _fileService.moveItem(deviceId, item.path, destinationPath);
          } else {
            await _fileService.copyItem(deviceId, item.path, destinationPath);
          }
          successCount++;
        } catch (e) {
          errors.add(
              'Failed to ${_isCutOperation ? 'move' : 'copy'} ${item.name}: $e');
        }
      }

      // Clear clipboard if it was a cut operation
      if (_isCutOperation) {
        setState(() {
          _clipboardItems.clear();
          _isCutOperation = false;
        });
      }

      // Fast refresh directory
      await _fastRefreshDirectory();

      ProgressNotificationService().hide();
      if (errors.isEmpty) {
        ProgressNotificationService().showSuccess(
          'Pasted $successCount items successfully',
        );
      } else {
        ProgressNotificationService().showError(
          'Pasted $successCount items, ${errors.length} errors',
          duration: const Duration(seconds: 5),
        );
      }
    } catch (e) {
      ProgressNotificationService().hide();
      ProgressNotificationService().showError(
        'Failed to paste items: $e',
      );
    }
  }
}
