import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/app_state.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Settings'),
          ),
          body: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // User Interface Section
              _buildSection(
                context,
                'User Interface',
                [
                  ListTile(
                    leading: const Icon(Icons.view_list),
                    title: const Text('Default View Mode'),
                    subtitle: Text(
                        appState.defaultGridView ? 'Grid View' : 'List View'),
                    trailing: SegmentedButton<bool>(
                      segments: const [
                        ButtonSegment<bool>(
                          value: false,
                          label: Text('List'),
                          icon: Icon(Icons.view_list, size: 16),
                        ),
                        ButtonSegment<bool>(
                          value: true,
                          label: Text('Grid'),
                          icon: Icon(Icons.grid_view, size: 16),
                        ),
                      ],
                      selected: {appState.defaultGridView},
                      onSelectionChanged: (Set<bool> selection) {
                        appState.setDefaultGridView(selection.first);
                      },
                      style: SegmentedButton.styleFrom(
                        selectedBackgroundColor:
                            Theme.of(context).colorScheme.primary,
                        selectedForegroundColor: Colors.white,
                      ),
                    ),
                  ),
                  ListTile(
                    leading: const Icon(Icons.apps),
                    title: const Text('Apps Grid Columns'),
                    subtitle: Text('${appState.appsGridColumns} columns'),
                    trailing: SegmentedButton<int>(
                      segments: const [
                        ButtonSegment<int>(
                          value: 3,
                          label: Text('3'),
                        ),
                        ButtonSegment<int>(
                          value: 4,
                          label: Text('4'),
                        ),
                        ButtonSegment<int>(
                          value: 5,
                          label: Text('5'),
                        ),
                        ButtonSegment<int>(
                          value: 6,
                          label: Text('6'),
                        ),
                      ],
                      selected: {appState.appsGridColumns},
                      onSelectionChanged: (Set<int> selection) {
                        appState.setAppsGridColumns(selection.first);
                      },
                      style: SegmentedButton.styleFrom(
                        selectedBackgroundColor:
                            Theme.of(context).colorScheme.primary,
                        selectedForegroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Network Section
              _buildSection(
                context,
                'Network',
                [
                  ListTile(
                    leading: const Icon(Icons.network_check),
                    title: const Text('Network Scan Timeout'),
                    subtitle: const Text('2 minutes'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showTimeoutDialog(context),
                  ),
                  ListTile(
                    leading: const Icon(Icons.refresh),
                    title: const Text('Auto-refresh Devices'),
                    subtitle: const Text('Automatically check device status'),
                    trailing: Switch(
                      value: appState.autoRefreshEnabled,
                      onChanged: (value) {
                        appState.setAutoRefreshEnabled(value);
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  if (appState.autoRefreshEnabled)
                    ListTile(
                      leading: const Icon(Icons.timer),
                      title: const Text('Refresh Interval'),
                      subtitle: Text('${appState.autoRefreshInterval} seconds'),
                      trailing: DropdownButton<int>(
                        value: appState.autoRefreshInterval,
                        items: const [
                          DropdownMenuItem(value: 15, child: Text('15s')),
                          DropdownMenuItem(value: 30, child: Text('30s')),
                          DropdownMenuItem(value: 60, child: Text('1m')),
                          DropdownMenuItem(value: 120, child: Text('2m')),
                          DropdownMenuItem(value: 300, child: Text('5m')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            appState.setAutoRefreshInterval(value);
                          }
                        },
                        underline: Container(),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 24),

              // SSH Section
              _buildSection(
                context,
                'SSH',
                [
                  ListTile(
                    leading: const Icon(Icons.timer),
                    title: const Text('Connection Timeout'),
                    subtitle: const Text('30 seconds'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showSSHTimeoutDialog(context),
                  ),
                  ListTile(
                    leading: const Icon(Icons.terminal),
                    title: const Text('Terminal Font Size'),
                    subtitle: Text('${appState.terminalFontSize.toInt()}px'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showFontSizeDialog(context, appState),
                  ),
                  ListTile(
                    leading: const Icon(Icons.font_download),
                    title: const Text('Terminal Font Family'),
                    subtitle: Text(appState.terminalFontFamily),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showFontFamilyDialog(context, appState),
                  ),
                  ListTile(
                    leading: const Icon(Icons.palette),
                    title: const Text('Terminal Colors'),
                    subtitle: const Text('Customize terminal appearance'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showTerminalColorsDialog(context, appState),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Data Section
              _buildSection(
                context,
                'Data',
                [
                  ListTile(
                    leading: const Icon(Icons.download),
                    title: const Text('Export Devices'),
                    subtitle: const Text('Export device list to JSON'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _exportDevices(context, appState),
                  ),
                  ListTile(
                    leading: const Icon(Icons.upload),
                    title: const Text('Import Devices'),
                    subtitle: const Text('Import device list from JSON'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _importDevices(context, appState),
                  ),
                  ListTile(
                    leading: const Icon(Icons.delete_forever),
                    title: const Text('Clear All Data'),
                    subtitle: const Text('Remove all devices and settings'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showClearDataDialog(context, appState),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // About Section
              _buildSection(
                context,
                'About',
                [
                  ListTile(
                    leading: const Icon(Icons.info),
                    title: const Text('Version'),
                    subtitle: const Text('1.0.0'),
                  ),
                  ListTile(
                    leading: const Icon(Icons.developer_mode),
                    title: const Text('Developer Mode'),
                    subtitle: const Text('Enable app development features'),
                    trailing: Switch(
                      value: appState.developerModeEnabled,
                      onChanged: (value) {
                        appState.setDeveloperModeEnabled(value);
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  if (appState.developerModeEnabled) ...[
                    const Divider(),
                    ListTile(
                      leading: const Icon(Icons.info_outline),
                      title: const Text('Developer Features'),
                      subtitle: const Text(
                        'Create and edit apps, access app files, and use development tools',
                      ),
                      enabled: false,
                    ),
                  ],
                  const Divider(),
                  ListTile(
                    leading: const Icon(Icons.code),
                    title: const Text('Source Code'),
                    subtitle: const Text('View on GitHub'),
                    trailing: const Icon(Icons.open_in_new),
                    onTap: () => _openGitHub(),
                  ),
                  ListTile(
                    leading: const Icon(Icons.bug_report),
                    title: const Text('Report Issue'),
                    subtitle: const Text('Report bugs or request features'),
                    trailing: const Icon(Icons.open_in_new),
                    onTap: () => _reportIssue(),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSection(
      BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
          ),
        ),
        Card(
          child: Column(children: children),
        ),
      ],
    );
  }

  void _showTimeoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Network Scan Timeout'),
        content: const Text(
            'Configure how long to wait for network scan to complete.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showSSHTimeoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SSH Connection Timeout'),
        content: const Text('Configure how long to wait for SSH connections.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context, AppState appState) {
    double currentFontSize = appState.terminalFontSize;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Terminal Font Size'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Font Size: ${currentFontSize.toInt()}px'),
              const SizedBox(height: 16),
              Slider(
                value: currentFontSize,
                min: 10.0,
                max: 24.0,
                divisions: 14,
                label: '${currentFontSize.toInt()}px',
                onChanged: (value) {
                  setState(() {
                    currentFontSize = value;
                  });
                },
              ),
              const SizedBox(height: 16),
              const Text(
                'Preview: Terminal text will appear at this size',
                style: TextStyle(fontFamily: 'monospace'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: () {
                appState.setTerminalFontSize(currentFontSize);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Terminal font size set to ${currentFontSize.toInt()}px'),
                  ),
                );
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _showFontFamilyDialog(BuildContext context, AppState appState) {
    final fontFamilies = [
      'Cascadia Code',
      'Consolas',
      'Courier New',
      'DejaVu Sans Mono',
      'Fira Code',
      'Hack',
      'Inconsolata',
      'JetBrains Mono',
      'Liberation Mono',
      'Menlo',
      'Monaco',
      'monospace',
      'Roboto Mono',
      'SF Mono',
      'Source Code Pro',
      'Ubuntu Mono',
    ]..sort(); // Sort alphabetically

    String currentFontFamily = appState.terminalFontFamily;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Terminal Font Family'),
          content: SizedBox(
            width: 500, // Fixed width instead of maxFinite
            height: 400, // Fixed height to make it scrollable
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Select a font family:'),
                  const SizedBox(height: 16),
                  ...fontFamilies.map((font) => RadioListTile<String>(
                        title: Text(
                          font,
                          style: TextStyle(
                              fontFamily: font, fontWeight: FontWeight.bold),
                        ),
                        subtitle: Text(
                          'Sample: pi@raspberrypi:~ \$ ls -la',
                          style: TextStyle(fontFamily: font, fontSize: 12),
                        ),
                        value: font,
                        groupValue: currentFontFamily,
                        onChanged: (value) {
                          setState(() {
                            currentFontFamily = value!;
                          });
                        },
                      )),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: () {
                appState.setTerminalFontFamily(currentFontFamily);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Terminal font set to $currentFontFamily'),
                  ),
                );
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _showTerminalColorsDialog(BuildContext context, AppState appState) {
    Color currentBackground = appState.terminalBackground;
    Color currentForeground = appState.terminalForeground;
    Color currentCursor = appState.terminalCursor;
    Color currentSelection = appState.terminalSelection;
    Color currentBold = appState.terminalBold;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Terminal Colors'),
          content: SizedBox(
            width: 600, // Wider for theme selector
            height: 600, // Taller for more content
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Theme Selector
                  const Text(
                    'Color Themes',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildThemeButton('Ocean Dark', () {
                        final theme = _getOceanDarkTheme();
                        setState(() {
                          currentBackground = theme['background']!;
                          currentForeground = theme['foreground']!;
                          currentCursor = theme['cursor']!;
                          currentSelection = theme['selection']!;
                          currentBold = theme['bold']!;
                        });
                      }),
                      _buildThemeButton('Midnight', () {
                        final theme = _getMidnightTheme();
                        setState(() {
                          currentBackground = theme['background']!;
                          currentForeground = theme['foreground']!;
                          currentCursor = theme['cursor']!;
                          currentSelection = theme['selection']!;
                          currentBold = theme['bold']!;
                        });
                      }),
                      _buildThemeButton('Solar Dark', () {
                        final theme = _getSolarDarkTheme();
                        setState(() {
                          currentBackground = theme['background']!;
                          currentForeground = theme['foreground']!;
                          currentCursor = theme['cursor']!;
                          currentSelection = theme['selection']!;
                          currentBold = theme['bold']!;
                        });
                      }),
                      _buildThemeButton('Retro Dark', () {
                        final theme = _getRetroDarkTheme();
                        setState(() {
                          currentBackground = theme['background']!;
                          currentForeground = theme['foreground']!;
                          currentCursor = theme['cursor']!;
                          currentSelection = theme['selection']!;
                          currentBold = theme['bold']!;
                        });
                      }),
                      _buildThemeButton('Classic', () {
                        final theme = _getClassicTheme();
                        setState(() {
                          currentBackground = theme['background']!;
                          currentForeground = theme['foreground']!;
                          currentCursor = theme['cursor']!;
                          currentSelection = theme['selection']!;
                          currentBold = theme['bold']!;
                        });
                      }),
                      _buildThemeButton('Purple Night', () {
                        final theme = _getPurpleNightTheme();
                        setState(() {
                          currentBackground = theme['background']!;
                          currentForeground = theme['foreground']!;
                          currentCursor = theme['cursor']!;
                          currentSelection = theme['selection']!;
                          currentBold = theme['bold']!;
                        });
                      }),
                    ],
                  ),
                  const SizedBox(height: 24),

                  const Text(
                    'Custom Colors',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),

                  // Background Color
                  ListTile(
                    leading: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: currentBackground,
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    title: const Text('Background Color'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showColorPicker(
                      context,
                      'Background Color',
                      currentBackground,
                      (color) => setState(() => currentBackground = color),
                    ),
                  ),

                  // Foreground Color
                  ListTile(
                    leading: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: currentForeground,
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    title: const Text('Text Color'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showColorPicker(
                      context,
                      'Text Color',
                      currentForeground,
                      (color) => setState(() => currentForeground = color),
                    ),
                  ),

                  // Cursor Color
                  ListTile(
                    leading: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: currentCursor,
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    title: const Text('Cursor Color'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showColorPicker(
                      context,
                      'Cursor Color',
                      currentCursor,
                      (color) => setState(() => currentCursor = color),
                    ),
                  ),

                  // Selection Color
                  ListTile(
                    leading: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: currentSelection,
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    title: const Text('Selection Color'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showColorPicker(
                      context,
                      'Selection Color',
                      currentSelection,
                      (color) => setState(() => currentSelection = color),
                    ),
                  ),

                  // Bold Color
                  ListTile(
                    leading: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: currentBold,
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    title: const Text('Bold Text Color'),
                    trailing: const Icon(Icons.chevron_right),
                    onTap: () => _showColorPicker(
                      context,
                      'Bold Text Color',
                      currentBold,
                      (color) => setState(() => currentBold = color),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Preview
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: currentBackground,
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Preview:',
                          style: TextStyle(
                            color: currentForeground,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'pi@raspberrypi:~ \$ ls -la',
                          style: TextStyle(
                            color: currentForeground,
                            fontFamily: 'monospace',
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          'total 24',
                          style: TextStyle(
                            color: currentForeground,
                            fontFamily: 'monospace',
                            fontSize: 14,
                          ),
                        ),
                        Row(
                          children: [
                            Text(
                              'pi@raspberrypi:~ \$ ',
                              style: TextStyle(
                                color: currentForeground,
                                fontFamily: 'monospace',
                                fontSize: 14,
                              ),
                            ),
                            Container(
                              width: 8,
                              height: 16,
                              color: currentCursor,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: () {
                appState.setTerminalBackground(currentBackground);
                appState.setTerminalForeground(currentForeground);
                appState.setTerminalCursor(currentCursor);
                appState.setTerminalSelection(currentSelection);
                appState.setTerminalBold(currentBold);
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Terminal colors updated'),
                  ),
                );
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _showColorPicker(BuildContext context, String title, Color currentColor,
      Function(Color) onColorChanged) {
    final predefinedColors = [
      // Dark backgrounds
      Colors.black,
      const Color(0xFF0D1117), // GitHub dark
      const Color(0xFF1E1E1E), // VS Code dark
      const Color(0xFF2D2D30), // Dark gray
      const Color(0xFF1A1A1A), // Almost black
      const Color(0xFF282828), // Gruvbox dark

      // Light backgrounds
      Colors.white,
      const Color(0xFFF8F8F2), // Monokai light
      const Color(0xFFE8EAED), // Light gray
      const Color(0xFFFDF6E3), // Solarized light
      const Color(0xFFF5F5F5), // Very light gray

      // Reds (errors)
      const Color(0xFFFF6B6B), // Coral red
      const Color(0xFFE74C3C), // Classic red
      const Color(0xFFDC322F), // Solarized red
      const Color(0xFFCC241D), // Gruvbox red
      const Color(0xFFF92672), // Monokai red

      // Greens (success)
      const Color(0xFF4ECDC4), // Teal green
      const Color(0xFF2ECC71), // Emerald
      const Color(0xFF859900), // Solarized green
      const Color(0xFF98971A), // Gruvbox green
      const Color(0xFFA6E22E), // Monokai green

      // Blues (info/directories)
      const Color(0xFF4FC3F7), // Sky blue
      const Color(0xFF3498DB), // Dodger blue
      const Color(0xFF268BD2), // Solarized blue
      const Color(0xFF458588), // Gruvbox blue
      const Color(0xFF66D9EF), // Monokai blue

      // Yellows (warnings)
      const Color(0xFFFFE66D), // Warm yellow
      const Color(0xFFF39C12), // Orange yellow
      const Color(0xFFB58900), // Solarized yellow
      const Color(0xFFD79921), // Gruvbox yellow
      const Color(0xFFE6DB74), // Monokai yellow

      // Purples/Magentas
      const Color(0xFFBA68C8), // Purple
      const Color(0xFF9B59B6), // Amethyst
      const Color(0xFFD33682), // Solarized magenta
      const Color(0xFFB16286), // Gruvbox purple
      const Color(0xFFAE81FF), // Monokai purple

      // Cyans
      const Color(0xFF26C6DA), // Cyan
      const Color(0xFF1ABC9C), // Turquoise
      const Color(0xFF2AA198), // Solarized cyan
      const Color(0xFF689D6A), // Gruvbox aqua
      const Color(0xFF66D9EF), // Monokai cyan
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: SizedBox(
          width: 400,
          height: 300,
          child: SingleChildScrollView(
            child: Wrap(
              spacing: 6,
              runSpacing: 6,
              children: predefinedColors
                  .map((color) => GestureDetector(
                        onTap: () {
                          onColorChanged(color);
                          Navigator.of(context).pop();
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: color,
                            border: Border.all(
                              color: currentColor == color
                                  ? Colors.blue
                                  : Colors.grey.withValues(alpha: 0.5),
                              width: currentColor == color ? 3 : 1,
                            ),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: currentColor == color
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 16,
                                )
                              : null,
                        ),
                      ))
                  .toList(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => _showCustomColorPicker(
                context, title, currentColor, onColorChanged),
            child: const Text('Custom Color'),
          ),
        ],
      ),
    );
  }

  void _exportDevices(BuildContext context, AppState appState) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon')),
    );
  }

  void _importDevices(BuildContext context, AppState appState) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Import functionality coming soon')),
    );
  }

  void _showClearDataDialog(BuildContext context, AppState appState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete all devices and settings. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final appState = Provider.of<AppState>(context, listen: false);
              await appState.clearAllData();
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('All data cleared')),
                );
              }
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  void _openGitHub() {
    // Open GitHub repository in browser
    debugPrint('Opening GitHub repository...');
  }

  void _reportIssue() {
    // Open issue reporting in browser
    debugPrint('Opening issue reporting...');
  }

  void _showCustomColorPicker(BuildContext context, String title,
      Color currentColor, Function(Color) onColorChanged) {
    Color selectedColor = currentColor;
    HSVColor hsvColor = HSVColor.fromColor(currentColor);

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('Custom $title'),
          content: SizedBox(
            width: 320,
            height: 420,
            child: Column(
              children: [
                // Hue/Saturation Picker
                Container(
                  width: 280,
                  height: 200,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border:
                        Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: GestureDetector(
                      onPanUpdate: (details) {
                        final Offset localPosition = details.localPosition;
                        final double saturation =
                            (localPosition.dx / 280).clamp(0.0, 1.0);
                        final double value =
                            1.0 - (localPosition.dy / 200).clamp(0.0, 1.0);

                        setState(() {
                          hsvColor = hsvColor
                              .withSaturation(saturation)
                              .withValue(value);
                          selectedColor = hsvColor.toColor();
                        });
                      },
                      onTapDown: (details) {
                        final Offset localPosition = details.localPosition;
                        final double saturation =
                            (localPosition.dx / 280).clamp(0.0, 1.0);
                        final double value =
                            1.0 - (localPosition.dy / 200).clamp(0.0, 1.0);

                        setState(() {
                          hsvColor = hsvColor
                              .withSaturation(saturation)
                              .withValue(value);
                          selectedColor = hsvColor.toColor();
                        });
                      },
                      child: CustomPaint(
                        size: const Size(280, 200),
                        painter: _HueSaturationPainter(
                            hsvColor.hue, hsvColor.saturation, hsvColor.value),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Hue Slider
                Container(
                  width: 280,
                  height: 20,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border:
                        Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: GestureDetector(
                      onPanUpdate: (details) {
                        final double hue =
                            (details.localPosition.dx / 280 * 360)
                                .clamp(0.0, 360.0);
                        setState(() {
                          hsvColor = hsvColor.withHue(hue);
                          selectedColor = hsvColor.toColor();
                        });
                      },
                      onTapDown: (details) {
                        final double hue =
                            (details.localPosition.dx / 280 * 360)
                                .clamp(0.0, 360.0);
                        setState(() {
                          hsvColor = hsvColor.withHue(hue);
                          selectedColor = hsvColor.toColor();
                        });
                      },
                      child: CustomPaint(
                        size: const Size(280, 20),
                        painter: _HueSliderPainter(hsvColor.hue),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Hex Input and Preview
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: const InputDecoration(
                          labelText: 'Hex',
                          prefixText: '#',
                          border: OutlineInputBorder(),
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        controller: TextEditingController(
                          text:
                              '${selectedColor.r.toInt().toRadixString(16).padLeft(2, '0')}${selectedColor.g.toInt().toRadixString(16).padLeft(2, '0')}${selectedColor.b.toInt().toRadixString(16).padLeft(2, '0')}'
                                  .toUpperCase(),
                        ),
                        onChanged: (value) {
                          try {
                            final color =
                                Color(int.parse('FF$value', radix: 16));
                            setState(() {
                              selectedColor = color;
                              hsvColor = HSVColor.fromColor(color);
                            });
                          } catch (e) {
                            // Invalid hex input, ignore
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Container(
                      width: 60,
                      height: 48,
                      decoration: BoxDecoration(
                        color: selectedColor,
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // RGB Values
                Text(
                  'RGB(${selectedColor.r}, ${selectedColor.g}, ${selectedColor.b})',
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: () {
                onColorChanged(selectedColor);
                Navigator.of(context).pop();
                Navigator.of(context)
                    .pop(); // Close the predefined colors dialog too
              },
              child: const Text('Select'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeButton(String name, VoidCallback onTap) {
    return ElevatedButton(
      onPressed: onTap,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      child: Text(name, style: const TextStyle(fontSize: 12)),
    );
  }

  Map<String, Color> _getOceanDarkTheme() {
    return {
      'background': const Color(0xFF0D1117),
      'foreground': const Color(0xFFE8EAED),
      'cursor':
          const Color(0xFF6EAEA2), // RGB(110, 174, 162) from your screenshot
      'selection': const Color(0x4D3F51B5),
      'bold': const Color(0xFFFFFFFF),
    };
  }

  Map<String, Color> _getMidnightTheme() {
    return {
      'background': const Color(0xFF1E1E1E),
      'foreground': const Color(0xFFCCCCCC),
      'cursor': const Color(0xFFAEAFAD),
      'selection': const Color(0x4D264F78),
      'bold': const Color(0xFFFFFFFF),
    };
  }

  Map<String, Color> _getSolarDarkTheme() {
    return {
      'background': const Color(0xFF002B36),
      'foreground': const Color(0xFF839496),
      'cursor': const Color(0xFF93A1A1),
      'selection': const Color(0x4D073642),
      'bold': const Color(0xFFFDF6E3),
    };
  }

  Map<String, Color> _getRetroDarkTheme() {
    return {
      'background': const Color(0xFF282828),
      'foreground': const Color(0xFFEBDBB2),
      'cursor': const Color(0xFFFBF1C7),
      'selection': const Color(0x4D3C3836),
      'bold': const Color(0xFFFBF1C7),
    };
  }

  Map<String, Color> _getClassicTheme() {
    return {
      'background': const Color(0xFF272822),
      'foreground': const Color(0xFFF8F8F2),
      'cursor': const Color(0xFFF8F8F0),
      'selection': const Color(0x4D49483E),
      'bold': const Color(0xFFFFFFFF),
    };
  }

  Map<String, Color> _getPurpleNightTheme() {
    return {
      'background': const Color(0xFF282A36),
      'foreground': const Color(0xFFF8F8F2),
      'cursor': const Color(0xFFBD93F9),
      'selection': const Color(0x4D44475A),
      'bold': const Color(0xFFFFFFFF),
    };
  }
}

class _HueSaturationPainter extends CustomPainter {
  final double hue;
  final double saturation;
  final double value;

  _HueSaturationPainter(this.hue, this.saturation, this.value);

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // Create gradient from white to the current hue
    final hueColor = HSVColor.fromAHSV(1.0, hue, 1.0, 1.0).toColor();

    // Horizontal gradient (saturation)
    final saturationGradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [Colors.white, hueColor],
    );

    // Vertical gradient (value/brightness)
    final valueGradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [Colors.transparent, Colors.black],
    );

    // Paint saturation gradient
    canvas.drawRect(
        rect, Paint()..shader = saturationGradient.createShader(rect));

    // Paint value gradient on top
    canvas.drawRect(rect, Paint()..shader = valueGradient.createShader(rect));

    // Draw selection indicator
    final indicatorX = saturation * size.width;
    final indicatorY = (1.0 - value) * size.height;

    canvas.drawCircle(
      Offset(indicatorX, indicatorY),
      8,
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2,
    );

    canvas.drawCircle(
      Offset(indicatorX, indicatorY),
      6,
      Paint()
        ..color = Colors.black
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class _HueSliderPainter extends CustomPainter {
  final double hue;

  _HueSliderPainter(this.hue);

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // Create hue gradient
    final hueGradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        const HSVColor.fromAHSV(1.0, 0, 1.0, 1.0).toColor(), // Red
        const HSVColor.fromAHSV(1.0, 60, 1.0, 1.0).toColor(), // Yellow
        const HSVColor.fromAHSV(1.0, 120, 1.0, 1.0).toColor(), // Green
        const HSVColor.fromAHSV(1.0, 180, 1.0, 1.0).toColor(), // Cyan
        const HSVColor.fromAHSV(1.0, 240, 1.0, 1.0).toColor(), // Blue
        const HSVColor.fromAHSV(1.0, 300, 1.0, 1.0).toColor(), // Magenta
        const HSVColor.fromAHSV(1.0, 360, 1.0, 1.0).toColor(), // Red
      ],
    );

    canvas.drawRect(rect, Paint()..shader = hueGradient.createShader(rect));

    // Draw selection indicator
    final indicatorX = (hue / 360) * size.width;

    canvas.drawCircle(
      Offset(indicatorX, size.height / 2),
      size.height / 2 + 2,
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
