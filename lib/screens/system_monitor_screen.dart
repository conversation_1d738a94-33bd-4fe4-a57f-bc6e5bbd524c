import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/app_state.dart';

class SystemMonitorScreen extends StatefulWidget {
  const SystemMonitorScreen({super.key});

  @override
  State<SystemMonitorScreen> createState() => _SystemMonitorScreenState();
}

class _SystemMonitorScreenState extends State<SystemMonitorScreen>
    with TickerProviderStateMixin {
  Timer? _refreshTimer;
  late AnimationController _animationController;
  int _refreshInterval = 5; // seconds
  bool _isAutoRefresh = true;
  String _selectedView = 'overview'; // overview, detailed, performance

  // Historical data for charts
  final List<double> _cpuHistory = [];
  final List<double> _memoryHistory = [];
  final List<double> _tempHistory = [];
  final int _maxHistoryPoints = 60; // 5 minutes at 5-second intervals

  // Process management state
  bool _showAllProcesses = false;
  String? _selectedProcessPid;
  String _processSortColumn = 'cpu_percent';
  bool _processSortAscending = false;
  String _processSearchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // Service management state
  bool _showAllServices = false;
  String _serviceSortColumn = 'name';
  bool _serviceSortAscending = true;
  String _serviceSearchQuery = '';
  String _serviceStatusFilter = 'all'; // all, active, failed, inactive
  final TextEditingController _serviceSearchController =
      TextEditingController();

  // Service selection and action menu state
  String? _selectedServiceName;
  String? _actionMenuServiceName;
  Map<String, dynamic>? _actionMenuServiceData;
  final Map<String, int> _lastServiceActionTime =
      {}; // serviceName -> timestamp

  // Action menu state - tracks which process has an open action menu
  String? _actionMenuProcessPid;
  Map<String, dynamic>? _actionMenuProcessData;

  // Track last action time for each process to prevent premature state clearing
  final Map<int, int> _lastProcessActionTime = {};

  // Column widths for resizable process table (persistent across sessions)
  Map<String, double> _processColumnWidths = {
    'pid': 80.0,
    'ppid': 80.0,
    'user': 120.0,
    'cpu_percent': 80.0,
    'mem_percent': 80.0,
    'read_rate': 80.0,
    'write_rate': 80.0,
    'vsz': 80.0,
    'rss': 80.0,
    'stat': 60.0,
    'cmd': 300.0, // Will be calculated to fill remaining space
    'actions': 100.0,
  };

  // Minimum column widths
  static const Map<String, double> _minColumnWidths = {
    'pid': 60.0,
    'ppid': 60.0,
    'user': 80.0,
    'cpu_percent': 60.0,
    'mem_percent': 60.0,
    'read_rate': 60.0,
    'write_rate': 60.0,
    'vsz': 60.0,
    'rss': 60.0,
    'stat': 50.0,
    'cmd': 150.0,
    'actions': 80.0,
  };

  // Track if we're currently resizing to optimize performance
  bool _isResizing = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Auto-select best device and fetch system info when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = Provider.of<AppState>(context, listen: false);
      appState.autoSelectBestDevice();
      _fetchSystemInfoForSelectedDevice();
      _animationController.forward();
    });

    // Load saved column widths
    _loadProcessColumnWidths();

    // Start auto-refresh timer
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _animationController.dispose();
    _searchController.dispose();
    _serviceSearchController.dispose();

    // Clear action menu state
    _actionMenuProcessPid = null;
    _actionMenuProcessData = null;
    _actionMenuServiceName = null;
    _actionMenuServiceData = null;

    super.dispose();
  }

  void _startAutoRefresh() {
    _refreshTimer?.cancel();
    if (_isAutoRefresh) {
      _refreshTimer =
          Timer.periodic(Duration(seconds: _refreshInterval), (timer) {
        _fetchSystemInfoForSelectedDevice();
      });
    }
  }

  void _fetchSystemInfoForSelectedDevice() {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice != null && selectedDevice.isConnected) {
      appState.refreshSystemInfo(selectedDevice.id);
      _updateHistoricalData(selectedDevice);
    }
  }

  void _updateHistoricalData(Device device) {
    // Update CPU history
    if (device.cpuUsage != null) {
      _cpuHistory.add(device.cpuUsage!);
      if (_cpuHistory.length > _maxHistoryPoints) {
        _cpuHistory.removeAt(0);
      }
    }

    // Update memory history
    if (device.memoryInfo != null &&
        device.memoryInfo!['usage_percent'] != null) {
      _memoryHistory.add(device.memoryInfo!['usage_percent'].toDouble());
      if (_memoryHistory.length > _maxHistoryPoints) {
        _memoryHistory.removeAt(0);
      }
    }

    // Update temperature history
    if (device.temperature != null) {
      _tempHistory.add(device.temperature!);
      if (_tempHistory.length > _maxHistoryPoints) {
        _tempHistory.removeAt(0);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final connectedDevices = appState.connectedDevices;
        final selectedDevice = appState.selectedDevice;

        // Auto-select best device if none selected
        if (selectedDevice == null && connectedDevices.isNotEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            appState.autoSelectBestDevice();
          });
        }

        return Scaffold(
          backgroundColor: Theme.of(context).colorScheme.surface,
          appBar: _buildModernAppBar(context),
          body: connectedDevices.isEmpty
              ? _buildNoDevicesConnected()
              : selectedDevice == null
                  ? _buildNoDeviceSelected()
                  : _buildSystemMonitorDashboard(selectedDevice, appState),
        );
      },
    );
  }

  PreferredSizeWidget _buildModernAppBar(BuildContext context) {
    return AppBar(
      title: const Text('System Monitor'),
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      elevation: 0,
      actions: [
        // View selector
        PopupMenuButton<String>(
          icon: const Icon(Icons.view_module),
          tooltip: 'Change View',
          onSelected: (value) {
            setState(() {
              _selectedView = value;
            });
          },
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'overview',
              child: Row(
                children: [
                  Icon(Icons.dashboard, size: 18),
                  const SizedBox(width: 8),
                  const Text('Overview'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'detailed',
              child: Row(
                children: [
                  Icon(Icons.info_outline, size: 18),
                  const SizedBox(width: 8),
                  const Text('Detailed'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'performance',
              child: Row(
                children: [
                  Icon(Icons.speed, size: 18),
                  const SizedBox(width: 8),
                  const Text('Performance'),
                ],
              ),
            ),
          ],
        ),
        // Refresh interval selector
        PopupMenuButton<int>(
          icon: const Icon(Icons.timer),
          tooltip: 'Refresh Interval',
          onSelected: (value) {
            setState(() {
              _refreshInterval = value;
              _startAutoRefresh();
            });
          },
          itemBuilder: (context) => [
            PopupMenuItem(value: 1, child: Text('1 second')),
            PopupMenuItem(value: 5, child: Text('5 seconds')),
            PopupMenuItem(value: 10, child: Text('10 seconds')),
            PopupMenuItem(value: 30, child: Text('30 seconds')),
          ],
        ),
        // Manual refresh
        IconButton(
          onPressed: _fetchSystemInfoForSelectedDevice,
          icon: Icon(
            Icons.refresh,
            color: _isAutoRefresh
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface,
          ),
          tooltip: 'Refresh System Info',
        ),
        // Auto-refresh toggle
        IconButton(
          onPressed: () {
            setState(() {
              _isAutoRefresh = !_isAutoRefresh;
              _startAutoRefresh();
            });
          },
          icon: Icon(
            _isAutoRefresh ? Icons.pause : Icons.play_arrow,
            color: _isAutoRefresh
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface,
          ),
          tooltip: _isAutoRefresh ? 'Pause Auto-refresh' : 'Start Auto-refresh',
        ),
      ],
    );
  }

  Widget _buildSystemMonitorDashboard(Device device, AppState appState) {
    if (!device.hasSystemInfo) {
      return _buildNoSystemData();
    }

    switch (_selectedView) {
      case 'detailed':
        return _buildDetailedView(device);
      case 'performance':
        return _buildPerformanceView(device);
      default:
        return _buildOverviewDashboard(device);
    }
  }

  Widget _buildNoDevicesConnected() {
    return Center(
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: 0.8 + (_animationController.value * 0.2),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 0.1),
                        Theme.of(context)
                            .colorScheme
                            .secondary
                            .withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.computer_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'No Devices Connected',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    'Connect to a device from the Devices tab to monitor its system resources and performance metrics',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.outline,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildNoDeviceSelected() {
    return Center(
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: 0.8 + (_animationController.value * 0.2),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context)
                            .colorScheme
                            .primary
                            .withValues(alpha: 0.1),
                        Theme.of(context)
                            .colorScheme
                            .secondary
                            .withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.monitor,
                    size: 64,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'No Device Selected',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    'Go to the Devices tab to select an active device for monitoring',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.outline,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildNoSystemData() {
    return Center(
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: 0.8 + (_animationController.value * 0.2),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context)
                            .colorScheme
                            .error
                            .withValues(alpha: 0.1),
                        Theme.of(context)
                            .colorScheme
                            .errorContainer
                            .withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.info_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'No System Data Available',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    'System monitoring data is not available for this device.\nConnect via SSH to enable monitoring.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.outline,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildOverviewDashboard(Device device) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Device Header with Health Status
              _buildDeviceHeader(device),
              const SizedBox(height: 24),

              // Primary Metrics Dashboard
              _buildPrimaryMetricsDashboard(device),
              const SizedBox(height: 24),

              // System Health Overview
              _buildSystemHealthOverview(device),
              const SizedBox(height: 24),

              // Hardware Information Cards
              _buildHardwareInfoCards(device),
            ],
          ),
        );
      },
    );
  }

  // Detailed view with comprehensive hardware information
  Widget _buildDetailedView(Device device) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Device Overview
          _buildDetailedDeviceInfo(device),
          const SizedBox(height: 24),

          // CPU Details
          _buildDetailedCpuInfo(device),
          const SizedBox(height: 24),

          // Memory Details
          _buildDetailedMemoryInfo(device),
          const SizedBox(height: 24),

          // Storage Details
          _buildDetailedStorageInfo(device),
          const SizedBox(height: 24),

          // Network Details
          if (device.networkInterfaces != null)
            _buildDetailedNetworkInfo(device),
          const SizedBox(height: 24),

          // GPU Details
          if (device.gpuInfo != null) _buildDetailedGpuInfo(device),
          const SizedBox(height: 24),

          // Power Details
          if (device.powerInfo != null) _buildDetailedPowerInfo(device),
          const SizedBox(height: 24),

          // Process and Service Management
          _buildProcessAndServiceManagement(device),
        ],
      ),
    );
  }

  // Performance view with real-time charts and trends
  Widget _buildPerformanceView(Device device) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Performance Summary
          _buildPerformanceSummary(device),
          const SizedBox(height: 24),

          // Real-time Charts
          _buildRealTimeCharts(),
          const SizedBox(height: 24),

          // Performance Metrics
          _buildPerformanceMetrics(device),
          const SizedBox(height: 24),

          // System Stress Indicators
          _buildStressIndicators(device),
          const SizedBox(height: 24),

          // Performance History
          _buildPerformanceHistory(),
        ],
      ),
    );
  }

  Widget _buildDeviceHeader(Device device) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.computer,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.name,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  device.ip,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getHealthStatusColor(device.healthStatus),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              device.healthStatus,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrimaryMetricsDashboard(Device device) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Primary Metrics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 20),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio:
                5.0, // Much higher ratio to make cards as short as system info cards
            children: [
              _buildModernMetricCard(
                title: 'CPU Usage',
                value: device.cpuUsage?.toStringAsFixed(1) ?? '--',
                unit: '%',
                progress: (device.cpuUsage ?? 0) / 100,
                icon: Icons.memory,
                color: _getColorForPercentage(device.cpuUsage ?? 0),
                subtitle: device.cpuFrequency != null
                    ? '${device.cpuFrequency!.toInt()} MHz'
                    : null,
              ),
              _buildModernMetricCard(
                title: 'Memory',
                value: device.memoryInfo != null
                    ? (device.memoryInfo!['usage_percent'] ?? 0)
                        .toStringAsFixed(1)
                    : '--',
                unit: '%',
                progress: device.memoryInfo != null
                    ? (device.memoryInfo!['usage_percent'] ?? 0) / 100
                    : 0,
                icon: Icons.storage,
                color: _getColorForPercentage(
                    device.memoryInfo?['usage_percent']?.toDouble() ?? 0),
                subtitle: device.memoryInfo != null
                    ? '${(device.memoryInfo!['used'] / 1024).toStringAsFixed(1)}G / ${(device.memoryInfo!['total'] / 1024).toStringAsFixed(1)}G'
                    : null,
              ),
              _buildModernMetricCard(
                title: 'Temperature',
                value: device.temperature?.toStringAsFixed(1) ?? '--',
                unit: '°C',
                progress: device.temperature != null
                    ? (device.temperature! / 85).clamp(0.0, 1.0)
                    : 0,
                icon: Icons.thermostat,
                color: _getColorForTemperature(device.temperature ?? 0),
                subtitle: device.gpuTemperature != null
                    ? 'GPU: ${device.gpuTemperature!.toStringAsFixed(1)}°C'
                    : null,
              ),
              _buildModernMetricCard(
                title: 'Disk Usage',
                value: device.diskInfo != null
                    ? (device.diskInfo!['usage_percent'] ?? 0).toString()
                    : '--',
                unit: '%',
                progress: device.diskInfo != null
                    ? (device.diskInfo!['usage_percent'] ?? 0) / 100
                    : 0,
                icon: Icons.storage,
                color: _getColorForPercentage(
                    device.diskInfo?['usage_percent']?.toDouble() ?? 0),
                subtitle: device.diskInfo != null
                    ? '${(device.diskInfo!['used_gb'] ?? 0).toStringAsFixed(1)}G / ${(device.diskInfo!['total_gb'] ?? 0).toStringAsFixed(1)}G'
                    : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSystemHealthOverview(Device device) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'System Information',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoCard(
                  'Uptime',
                  device.uptime ?? 'N/A',
                  Icons.schedule,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoCard(
                  'Load Average',
                  device.loadAverage != null
                      ? '${device.loadAverage!['1min']?.toStringAsFixed(2) ?? '--'}'
                      : 'N/A',
                  Icons.trending_up,
                  Colors.purple,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoCard(
                  'Power Status',
                  device.isThrottled == true
                      ? 'Throttled'
                      : device.isThrottled == false
                          ? 'Normal'
                          : 'N/A',
                  Icons.power,
                  device.isThrottled == true ? Colors.red : Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHardwareInfoCards(Device device) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Hardware Information',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        // CPU Cores
        if (device.cpuCores != null) ...[
          _buildCpuCoresGrid(device.cpuCores!),
          const SizedBox(height: 16),
        ],
        // GPU Information
        if (device.gpuInfo != null) ...[
          _buildGpuInfoCard(device.gpuInfo!),
          const SizedBox(height: 16),
        ],
        // Power Information
        if (device.powerInfo != null) ...[
          _buildPowerInfoCard(device.powerInfo!),
          const SizedBox(height: 16),
        ],
        // Network Interfaces
        if (device.networkInterfaces != null) ...[
          _buildNetworkInterfacesCard(device.networkInterfaces!),
        ],
      ],
    );
  }

  // Helper methods for the modern dashboard
  Color _getHealthStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'good':
        return Colors.green;
      case 'warning':
        return Colors.orange;
      case 'critical':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildModernMetricCard({
    required String title,
    required String value,
    required String unit,
    required double progress,
    required IconData icon,
    required Color color,
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(8), // Further reduced for compactness
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        // Changed from Column to Row for horizontal layout
        children: [
          // Icon and title section
          Container(
            padding: const EdgeInsets.all(4), // Further reduced for compactness
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              icon,
              color: color,
              size: 12, // Further reduced for compactness
            ),
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 10, // Even smaller font for compactness
                      ),
                ),
                const SizedBox(height: 1), // Reduced spacing
                Row(
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Text(
                      value,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: color,
                            fontWeight: FontWeight.bold,
                            fontSize: 16, // Further reduced for compactness
                          ),
                    ),
                    const SizedBox(width: 1),
                    Text(
                      unit,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: color.withValues(alpha: 0.8),
                            fontWeight: FontWeight.w500,
                            fontSize: 10, // Smaller unit text
                          ),
                    ),
                  ],
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 1), // Reduced spacing
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontSize: 9, // Very small subtitle
                        ),
                  ),
                ],
                const SizedBox(height: 2), // Reduced spacing
                ClipRRect(
                  borderRadius: BorderRadius.circular(2),
                  child: LinearProgressIndicator(
                    value: progress.clamp(0.0, 1.0),
                    backgroundColor: color.withValues(alpha: 0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                    minHeight: 3, // Even thinner progress bar
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Detailed view methods
  Widget _buildDetailedDeviceInfo(Device device) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.computer,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Device Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailRow('Device Name', device.name),
            _buildDetailRow('IP Address', device.ip),
            _buildDetailRow('MAC Address', device.mac),
            _buildDetailRow('Vendor', device.vendor),
            _buildDetailRow(
                'Status', device.isConnected ? 'Connected' : 'Disconnected'),
            _buildDetailRow('Health Status', device.healthStatus),
            if (device.lastSystemInfoUpdate != null)
              _buildDetailRow(
                  'Last Update', _formatDateTime(device.lastSystemInfoUpdate!)),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedCpuInfo(Device device) {
    final cpuInfo = device.systemInfo['cpu'] as Map<String, dynamic>?;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.memory,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'CPU Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
                'Overall Usage',
                cpuInfo?['overall'] != null
                    ? '${cpuInfo!['overall'].toStringAsFixed(1)}%'
                    : 'N/A'),
            _buildDetailRow(
                'Frequency',
                cpuInfo?['frequency_mhz'] != null
                    ? '${cpuInfo!['frequency_mhz'].toInt()} MHz'
                    : 'N/A'),
            _buildDetailRow(
                'Temperature',
                device.temperature != null
                    ? '${device.temperature!.toStringAsFixed(1)}°C'
                    : 'N/A'),

            // Per-core CPU utilization with visual indicators
            if (cpuInfo?['cores'] != null) ...[
              const Divider(),
              Text(
                'Per-Core Utilization',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const SizedBox(height: 12),
              _buildCpuCoresGrid(cpuInfo!['cores']),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedMemoryInfo(Device device) {
    final memoryInfo = device.systemInfo['memory'] as Map<String, dynamic>?;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.storage,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Memory Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (memoryInfo != null) ...[
              // Basic memory information
              Text(
                'Physical Memory',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const SizedBox(height: 8),
              _buildDetailRow('Total Memory',
                  '${(memoryInfo['total'] / 1024).toStringAsFixed(2)} GB'),
              _buildDetailRow('Used Memory',
                  '${(memoryInfo['used'] / 1024).toStringAsFixed(2)} GB'),
              _buildDetailRow('Free Memory',
                  '${(memoryInfo['free'] / 1024).toStringAsFixed(2)} GB'),
              _buildDetailRow('Available Memory',
                  '${(memoryInfo['available'] / 1024).toStringAsFixed(2)} GB'),
              _buildDetailRow('Shared Memory',
                  '${(memoryInfo['shared'] / 1024).toStringAsFixed(2)} GB'),
              _buildDetailRow('Buffer/Cache',
                  '${(memoryInfo['buff_cache'] / 1024).toStringAsFixed(2)} GB'),
              _buildDetailRow('Usage Percentage',
                  '${memoryInfo['usage_percent']?.toStringAsFixed(1) ?? '--'}%'),

              // Swap information
              if (memoryInfo['swap'] != null) ...[
                const Divider(),
                Text(
                  'Swap Memory',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 8),
                _buildDetailRow('Total Swap',
                    '${(memoryInfo['swap']['total'] / 1024).toStringAsFixed(2)} GB'),
                _buildDetailRow('Used Swap',
                    '${(memoryInfo['swap']['used'] / 1024).toStringAsFixed(2)} GB'),
                _buildDetailRow('Free Swap',
                    '${(memoryInfo['swap']['free'] / 1024).toStringAsFixed(2)} GB'),
                _buildDetailRow('Swap Usage',
                    '${memoryInfo['swap']['usage_percent']?.toStringAsFixed(1) ?? '--'}%'),
              ],

              // Detailed memory breakdown
              if (memoryInfo['detailed'] != null) ...[
                const Divider(),
                Text(
                  'Memory Breakdown',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 8),
                _buildDetailRow('Buffers',
                    '${memoryInfo['detailed']['buffers']?.toStringAsFixed(1) ?? '--'} MB'),
                _buildDetailRow('Cached',
                    '${memoryInfo['detailed']['cached']?.toStringAsFixed(1) ?? '--'} MB'),
                _buildDetailRow('Slab',
                    '${memoryInfo['detailed']['slab']?.toStringAsFixed(1) ?? '--'} MB'),
                _buildDetailRow('Anonymous Pages',
                    '${memoryInfo['detailed']['anon_pages']?.toStringAsFixed(1) ?? '--'} MB'),
                _buildDetailRow('Mapped',
                    '${memoryInfo['detailed']['mapped']?.toStringAsFixed(1) ?? '--'} MB'),
                _buildDetailRow('Shared Memory',
                    '${memoryInfo['detailed']['shmem']?.toStringAsFixed(1) ?? '--'} MB'),
              ],

              // Top memory-consuming processes
              if (memoryInfo['top_processes'] != null) ...[
                const Divider(),
                Text(
                  'Top Memory Consumers',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 8),
                _buildProcessTable(memoryInfo['top_processes']),
              ],
            ] else ...[
              _buildDetailRow('Memory Information', 'Not Available'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedStorageInfo(Device device) {
    final diskInfo = device.systemInfo['disk'] as Map<String, dynamic>?;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.storage,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Storage Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (diskInfo != null) ...[
              // Root filesystem (backward compatibility)
              Text(
                'Root Filesystem',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const SizedBox(height: 8),
              _buildDetailRow('Total Space', diskInfo['total'] ?? 'N/A'),
              _buildDetailRow('Used Space', diskInfo['used'] ?? 'N/A'),
              _buildDetailRow(
                  'Available Space', diskInfo['available'] ?? 'N/A'),
              _buildDetailRow(
                  'Usage Percentage', '${diskInfo['usage_percent'] ?? '--'}%'),

              // All filesystems
              if (diskInfo['filesystems'] != null) ...[
                const Divider(),
                Text(
                  'All Filesystems',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 8),
                _buildFilesystemTable(diskInfo['filesystems']),
              ],

              // Disk performance metrics
              if (diskInfo['performance'] != null) ...[
                const Divider(),
                Text(
                  'Disk Performance',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 8),
                _buildDiskPerformanceTable(diskInfo['performance']),
              ],

              // Block devices
              if (diskInfo['block_devices'] != null) ...[
                const Divider(),
                Text(
                  'Block Devices',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 8),
                _buildBlockDevicesTable(diskInfo['block_devices']),
              ],

              // USB devices
              if (diskInfo['usb_devices'] != null &&
                  diskInfo['usb_devices'].isNotEmpty) ...[
                const Divider(),
                Text(
                  'USB Devices',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 8),
                _buildUsbDevicesTable(diskInfo['usb_devices']),
              ],

              // Recent activity
              if (diskInfo['recent_activity'] != null &&
                  diskInfo['recent_activity'].isNotEmpty) ...[
                const Divider(),
                Text(
                  'Recent Device Activity',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 8),
                ...diskInfo['recent_activity'].take(5).map<Widget>(
                      (activity) => Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Text(
                          activity.toString(),
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontFamily: 'monospace',
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant,
                                  ),
                        ),
                      ),
                    ),
              ],
            ] else ...[
              _buildDetailRow('Storage Information', 'Not Available'),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedNetworkInfo(Device device) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.network_check,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Network Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...device.networkInterfaces!.map((interface) {
              final name = interface['name'] ?? 'Unknown';
              final state = interface['state'] ?? 'Unknown';
              final ips = interface['ips'] as List<dynamic>? ?? [];

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 8),
                  _buildDetailRow('State', state),
                  if (ips.isNotEmpty)
                    _buildDetailRow('IP Addresses', ips.join(', ')),
                  const SizedBox(height: 12),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedGpuInfo(Device device) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.videogame_asset,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'GPU Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
                'GPU Memory',
                device.gpuInfo!['memory_mb'] != null
                    ? '${device.gpuInfo!['memory_mb']} MB'
                    : 'N/A'),
            _buildDetailRow(
                'GPU Frequency',
                device.gpuInfo!['frequency_mhz'] != null
                    ? '${device.gpuInfo!['frequency_mhz']} MHz'
                    : 'N/A'),
            _buildDetailRow(
                'GPU Temperature',
                device.gpuTemperature != null
                    ? '${device.gpuTemperature!.toStringAsFixed(1)}°C'
                    : 'N/A'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedPowerInfo(Device device) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.power, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Power Information',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
                'Throttling Status',
                device.powerInfo!['throttled'] == true
                    ? 'THROTTLED'
                    : 'Normal'),
            if (device.powerInfo!['voltage_core'] != null)
              _buildDetailRow(
                  'Core Voltage', '${device.powerInfo!['voltage_core']}V'),
            if (device.powerInfo!['voltage_sdram_c'] != null)
              _buildDetailRow('SDRAM C Voltage',
                  '${device.powerInfo!['voltage_sdram_c']}V'),
            if (device.powerInfo!['voltage_sdram_i'] != null)
              _buildDetailRow('SDRAM I Voltage',
                  '${device.powerInfo!['voltage_sdram_i']}V'),
            if (device.powerInfo!['voltage_sdram_p'] != null)
              _buildDetailRow('SDRAM P Voltage',
                  '${device.powerInfo!['voltage_sdram_p']}V'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Performance view methods
  Widget _buildPerformanceSummary(Device device) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.speed, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Performance Summary',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildPerformanceMetric(
                    'CPU Score',
                    _calculateCpuScore(device),
                    Icons.memory,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPerformanceMetric(
                    'Memory Score',
                    _calculateMemoryScore(device),
                    Icons.storage,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPerformanceMetric(
                    'Thermal Score',
                    _calculateThermalScore(device),
                    Icons.thermostat,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPerformanceMetric(
                    'Overall Score',
                    _calculateOverallScore(device),
                    Icons.star,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRealTimeCharts() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.show_chart,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Real-time Performance Charts',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 300,
              child: Row(
                children: [
                  Expanded(
                    child:
                        _buildMiniChart('CPU Usage', _cpuHistory, Colors.blue),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildMiniChart(
                        'Memory Usage', _memoryHistory, Colors.green),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildMiniChart(
                        'Temperature', _tempHistory, Colors.orange),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetrics(Device device) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Performance Metrics',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      _buildDetailRow(
                          'CPU Utilization',
                          device.cpuUsage != null
                              ? '${device.cpuUsage!.toStringAsFixed(1)}%'
                              : 'N/A'),
                      _buildDetailRow(
                          'CPU Frequency',
                          device.cpuFrequency != null
                              ? '${device.cpuFrequency!.toInt()} MHz'
                              : 'N/A'),
                      _buildDetailRow(
                          'CPU Temperature',
                          device.temperature != null
                              ? '${device.temperature!.toStringAsFixed(1)}°C'
                              : 'N/A'),
                    ],
                  ),
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: Column(
                    children: [
                      _buildDetailRow(
                          'Memory Usage',
                          device.memoryInfo != null
                              ? '${device.memoryInfo!['usage_percent']?.toStringAsFixed(1) ?? '--'}%'
                              : 'N/A'),
                      _buildDetailRow(
                          'Available Memory',
                          device.memoryInfo != null
                              ? '${(device.memoryInfo!['available'] / 1024).toStringAsFixed(1)} GB'
                              : 'N/A'),
                      _buildDetailRow(
                          'Load Average (1m)',
                          device.loadAverage != null
                              ? '${device.loadAverage!['1min']?.toStringAsFixed(2) ?? '--'}'
                              : 'N/A'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStressIndicators(Device device) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'System Stress Indicators',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStressIndicator(
                    'CPU Stress',
                    device.cpuUsage ?? 0,
                    80, // Warning threshold
                    90, // Critical threshold
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStressIndicator(
                    'Memory Stress',
                    device.memoryInfo?['usage_percent']?.toDouble() ?? 0,
                    85,
                    95,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStressIndicator(
                    'Thermal Stress',
                    device.temperature ?? 0,
                    70,
                    80,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStressIndicator(
                    'Power Stress',
                    device.isThrottled == true ? 100 : 0,
                    50,
                    80,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceHistory() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.history,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Performance History',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildHistoryCard('CPU', _cpuHistory, Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child:
                      _buildHistoryCard('Memory', _memoryHistory, Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildHistoryCard(
                      'Temperature', _tempHistory, Colors.orange),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetric(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMiniChart(String title, List<double> data, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: data.isEmpty
                ? Center(
                    child: Text(
                      'No data yet',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                    ),
                  )
                : CustomPaint(
                    size: Size.infinite,
                    painter: SimpleChartPainter(data, color),
                  ),
          ),
          const SizedBox(height: 8),
          Text(
            data.isNotEmpty
                ? '${data.last.toStringAsFixed(1)}${title.contains('Temperature') ? '°C' : '%'}'
                : '--',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildStressIndicator(String title, double value,
      double warningThreshold, double criticalThreshold) {
    Color color = Colors.green;
    String status = 'Normal';

    if (value >= criticalThreshold) {
      color = Colors.red;
      status = 'Critical';
    } else if (value >= warningThreshold) {
      color = Colors.orange;
      status = 'Warning';
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          CircularProgressIndicator(
            value: (value / 100).clamp(0.0, 1.0),
            backgroundColor: color.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            strokeWidth: 4,
          ),
          const SizedBox(height: 8),
          Text(
            status,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryCard(String title, List<double> data, Color color) {
    final avg =
        data.isNotEmpty ? data.reduce((a, b) => a + b) / data.length : 0.0;
    final max = data.isNotEmpty ? data.reduce((a, b) => a > b ? a : b) : 0.0;
    final min = data.isNotEmpty ? data.reduce((a, b) => a < b ? a : b) : 0.0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),
          _buildDetailRow('Average',
              '${avg.toStringAsFixed(1)}${title == 'Temperature' ? '°C' : '%'}'),
          _buildDetailRow('Maximum',
              '${max.toStringAsFixed(1)}${title == 'Temperature' ? '°C' : '%'}'),
          _buildDetailRow('Minimum',
              '${min.toStringAsFixed(1)}${title == 'Temperature' ? '°C' : '%'}'),
          _buildDetailRow('Data Points', '${data.length}'),
        ],
      ),
    );
  }

  // Performance calculation methods
  String _calculateCpuScore(Device device) {
    if (device.cpuUsage == null) return 'N/A';
    final usage = device.cpuUsage!;
    if (usage < 30) return 'Excellent';
    if (usage < 60) return 'Good';
    if (usage < 80) return 'Fair';
    return 'Poor';
  }

  String _calculateMemoryScore(Device device) {
    if (device.memoryInfo == null) return 'N/A';
    final usage = device.memoryInfo!['usage_percent']?.toDouble() ?? 0;
    if (usage < 50) return 'Excellent';
    if (usage < 70) return 'Good';
    if (usage < 85) return 'Fair';
    return 'Poor';
  }

  String _calculateThermalScore(Device device) {
    if (device.temperature == null) return 'N/A';
    final temp = device.temperature!;
    if (temp < 50) return 'Excellent';
    if (temp < 65) return 'Good';
    if (temp < 80) return 'Fair';
    return 'Poor';
  }

  String _calculateOverallScore(Device device) {
    final scores = [
      _calculateCpuScore(device),
      _calculateMemoryScore(device),
      _calculateThermalScore(device),
    ];

    final validScores = scores.where((s) => s != 'N/A').toList();
    if (validScores.isEmpty) return 'N/A';

    final excellentCount = validScores.where((s) => s == 'Excellent').length;
    final goodCount = validScores.where((s) => s == 'Good').length;
    final fairCount = validScores.where((s) => s == 'Fair').length;

    if (excellentCount >= validScores.length * 0.7) return 'Excellent';
    if (goodCount + excellentCount >= validScores.length * 0.7) return 'Good';
    if (fairCount + goodCount + excellentCount >= validScores.length * 0.7) {
      return 'Fair';
    }
    return 'Poor';
  }

  Widget _buildCpuCoresGrid(Map<String, dynamic> cpuCores) {
    final cores = cpuCores.entries.toList();

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: cores.length > 8
            ? 4
            : cores.length > 4
                ? 2
                : cores.length,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 2.5, // Wider cards for better readability
      ),
      itemCount: cores.length,
      itemBuilder: (context, index) {
        final core = cores[index];
        final usage = (core.value as num).toDouble();
        final color = _getColorForPercentage(usage);

        return Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.15),
                color.withValues(alpha: 0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: color.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Core icon
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  Icons.memory,
                  color: color,
                  size: 12,
                ),
              ),
              const SizedBox(width: 8),
              // Core info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      core.key.toUpperCase(),
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${usage.toStringAsFixed(1)}%',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: color,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                    ),
                    const SizedBox(height: 2),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(2),
                      child: LinearProgressIndicator(
                        value: usage / 100,
                        backgroundColor: color.withValues(alpha: 0.2),
                        valueColor: AlwaysStoppedAnimation<Color>(color),
                        minHeight: 3,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGpuInfoCard(Map<String, dynamic> gpuInfo) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.videogame_asset,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Graphics Processing Unit',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (gpuInfo['memory_mb'] != null)
              _buildInfoRow(
                'GPU Memory',
                '${gpuInfo['memory_mb']} MB',
              ),
            if (gpuInfo['frequency_mhz'] != null) ...[
              const Divider(),
              _buildInfoRow(
                'GPU Frequency',
                '${gpuInfo['frequency_mhz']} MHz',
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPowerInfoCard(Map<String, dynamic> powerInfo) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.power,
                  color: powerInfo['throttled'] == true
                      ? Colors.red
                      : Colors.green,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Power Status',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (powerInfo['throttled'] != null)
              _buildInfoRow(
                'Throttling Status',
                powerInfo['throttled'] == true ? 'THROTTLED' : 'Normal',
              ),
            if (powerInfo['voltage_core'] != null) ...[
              const Divider(),
              _buildInfoRow(
                'Core Voltage',
                '${powerInfo['voltage_core']}V',
              ),
            ],
            if (powerInfo['voltage_sdram_c'] != null) ...[
              const Divider(),
              _buildInfoRow(
                'SDRAM C Voltage',
                '${powerInfo['voltage_sdram_c']}V',
              ),
            ],
            if (powerInfo['voltage_sdram_i'] != null) ...[
              const Divider(),
              _buildInfoRow(
                'SDRAM I Voltage',
                '${powerInfo['voltage_sdram_i']}V',
              ),
            ],
            if (powerInfo['voltage_sdram_p'] != null) ...[
              const Divider(),
              _buildInfoRow(
                'SDRAM P Voltage',
                '${powerInfo['voltage_sdram_p']}V',
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkInterfacesCard(List<dynamic> interfaces) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.network_check,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Network Interfaces',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...interfaces.map((interface) {
              final name = interface['name'] ?? 'Unknown';
              final state = interface['state'] ?? 'Unknown';
              final ips = interface['ips'] as List<dynamic>? ?? [];

              return Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: state == 'UP'
                        ? Colors.green.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          state == 'UP' ? Icons.check_circle : Icons.cancel,
                          color: state == 'UP' ? Colors.green : Colors.grey,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          name,
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: state == 'UP' ? Colors.green : Colors.grey,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            state,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (ips.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      ...ips.map((ip) => Padding(
                            padding: const EdgeInsets.only(left: 24),
                            child: Text(
                              ip.toString(),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant,
                                  ),
                            ),
                          )),
                    ],
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
        ],
      ),
    );
  }

  Color _getColorForPercentage(double percentage) {
    if (percentage < 50) return Colors.green;
    if (percentage < 80) return Colors.orange;
    return Colors.red;
  }

  Color _getColorForTemperature(double temperature) {
    if (temperature < 60) return Colors.green;
    if (temperature < 80) return Colors.orange;
    return Colors.red;
  }

  // Process and Service Management section
  Widget _buildProcessAndServiceManagement(Device device) {
    final processData = device.systemInfo['processes'] as List<dynamic>?;
    final serviceData = device.systemInfo['services'] as List<dynamic>?;
    final systemProcessData =
        device.systemInfo['system_processes'] as Map<String, dynamic>?;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            Icon(Icons.settings, color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 8),
            Text(
              'Process and Service Management',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Process Summary
        if (systemProcessData?['summary'] != null)
          _buildProcessSummaryCard(systemProcessData!['summary']),
        const SizedBox(height: 16),

        // Top CPU Processes
        if (systemProcessData?['top_cpu'] != null) ...[
          _buildTopCpuProcessesCard(systemProcessData!['top_cpu']),
          const SizedBox(height: 16),
        ],

        // Top Memory Processes
        if (systemProcessData?['top_mem'] != null) ...[
          _buildTopMemoryProcessesCard(systemProcessData!['top_mem']),
          const SizedBox(height: 16),
        ],

        // All Processes Table
        if (processData != null && processData.isNotEmpty) ...[
          _buildAllProcessesCard(processData),
          const SizedBox(height: 16),
        ],

        // System Services
        if (serviceData != null && serviceData.isNotEmpty) ...[
          _buildSystemServicesCard(serviceData),
          const SizedBox(height: 16),
        ],

        // Process Tree (if available)
        if (systemProcessData?['tree'] != null)
          _buildProcessTreeCard(systemProcessData!['tree']),
      ],
    );
  }

  Widget _buildProcessSummaryCard(Map<String, dynamic> summary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Process Summary',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryMetric(
                    'Total Processes',
                    '${summary['total'] ?? 0}',
                    Icons.apps,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryMetric(
                    'Running',
                    '${summary['running'] ?? 0}',
                    Icons.play_arrow,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryMetric(
                    'Sleeping',
                    '${summary['sleeping'] ?? 0}',
                    Icons.pause,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryMetric(
                    'Zombie',
                    '${summary['zombie'] ?? 0}',
                    Icons.warning,
                    summary['zombie'] > 0 ? Colors.red : Colors.grey,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Additional process states row
            Row(
              children: [
                Expanded(
                  child: _buildSummaryMetric(
                    'Stopped',
                    '${summary['stopped'] ?? 0}',
                    Icons.pause_circle,
                    summary['stopped'] > 0 ? Colors.blue.shade700 : Colors.grey,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryMetric(
                    'Disk Sleep',
                    '${summary['disk_sleep'] ?? 0}',
                    Icons.storage,
                    summary['disk_sleep'] > 0 ? Colors.purple : Colors.grey,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildSummaryMetric(
                    'Idle',
                    '${summary['idle'] ?? 0}',
                    Icons.schedule,
                    summary['idle'] > 0 ? Colors.grey.shade600 : Colors.grey,
                  ),
                ),
                const SizedBox(width: 12),
                // Empty space to maintain alignment
                Expanded(child: Container()),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryMetric(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            title,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTopCpuProcessesCard(List<dynamic> topCpuProcesses) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.speed, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Top CPU Consuming Processes',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTopProcessesTable(topCpuProcesses),
          ],
        ),
      ),
    );
  }

  Widget _buildTopMemoryProcessesCard(List<dynamic> topMemProcesses) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.memory,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Top Memory Consuming Processes',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTopProcessesTable(topMemProcesses),
          ],
        ),
      ),
    );
  }

  Widget _buildAllProcessesCard(List<dynamic> processes) {
    // Filter processes based on search query
    final filteredProcesses = _filterProcesses(processes);
    // Sort processes based on current sort column and direction
    final sortedProcesses = _sortProcesses(filteredProcesses);
    final displayCount = _showAllProcesses ? sortedProcesses.length : 25;
    final displayProcesses = sortedProcesses.take(displayCount).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              children: [
                Icon(Icons.list, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'All Running Processes',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                Text(
                  'Showing ${displayProcesses.length} of ${filteredProcesses.length} processes',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
                const SizedBox(width: 8),
                if (filteredProcesses.length > 25)
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _showAllProcesses = !_showAllProcesses;
                      });
                    },
                    icon: Icon(
                      _showAllProcesses
                          ? Icons.visibility_off
                          : Icons.visibility,
                      size: 16,
                    ),
                    label: Text(_showAllProcesses ? 'Show Less' : 'Show All'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),

            // Search box
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search processes by command...',
                      prefixIcon: Icon(Icons.search),
                      suffixIcon: _processSearchQuery.isNotEmpty
                          ? IconButton(
                              icon: Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _processSearchQuery = '';
                                  _searchController.clear();
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _processSearchQuery = value;
                      });
                    },
                  ),
                ),
                if (_processSearchQuery.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${filteredProcesses.length} matches',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onPrimaryContainer,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 16),
            _buildSortableProcessTable(displayProcesses),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemServicesCard(List<dynamic> services) {
    // Filter and sort services
    final filteredServices = _filterServices(services);
    final sortedServices = _sortServices(filteredServices);
    final displayCount = _showAllServices ? sortedServices.length : 15;
    final displayServices = sortedServices.take(displayCount).toList();

    // Calculate service counts
    final activeServices =
        services.where((s) => s['active_state'] == 'active').toList();
    final failedServices =
        services.where((s) => s['active_state'] == 'failed').toList();
    final inactiveServices =
        services.where((s) => s['active_state'] == 'inactive').toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and status counts
            Row(
              children: [
                Icon(Icons.miscellaneous_services,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'System Services',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                _buildServiceStatusBadge(
                    '${activeServices.length} active', Colors.green),
                if (failedServices.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  _buildServiceStatusBadge(
                      '${failedServices.length} failed', Colors.red),
                ],
                if (inactiveServices.isNotEmpty) ...[
                  const SizedBox(width: 8),
                  _buildServiceStatusBadge(
                      '${inactiveServices.length} inactive', Colors.grey),
                ],
              ],
            ),
            const SizedBox(height: 16),

            // Search and filter controls
            Row(
              children: [
                // Search field
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _serviceSearchController,
                    decoration: InputDecoration(
                      hintText: 'Search services...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _serviceSearchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _serviceSearchController.clear();
                                  _serviceSearchQuery = '';
                                });
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _serviceSearchQuery = value;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),

                // Status filter dropdown
                Expanded(
                  flex: 1,
                  child: DropdownButtonFormField<String>(
                    value: _serviceStatusFilter,
                    decoration: InputDecoration(
                      labelText: 'Filter',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                    ),
                    items: const [
                      DropdownMenuItem(
                          value: 'all', child: Text('All Services')),
                      DropdownMenuItem(
                          value: 'active', child: Text('Active Only')),
                      DropdownMenuItem(
                          value: 'failed', child: Text('Failed Only')),
                      DropdownMenuItem(
                          value: 'inactive', child: Text('Inactive Only')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _serviceStatusFilter = value ?? 'all';
                      });
                    },
                  ),
                ),
                const SizedBox(width: 12),

                // Show all toggle
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _showAllServices = !_showAllServices;
                    });
                  },
                  icon: Icon(_showAllServices
                      ? Icons.visibility_off
                      : Icons.visibility),
                  label: Text(_showAllServices ? 'Show Less' : 'Show All'),
                  style: ElevatedButton.styleFrom(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Services table
            _buildEnhancedServicesTable(displayServices),

            // Show count information
            if (filteredServices.length != services.length ||
                !_showAllServices) ...[
              const SizedBox(height: 12),
              Text(
                'Showing ${displayServices.length} of ${filteredServices.length} services (${services.length} total)',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProcessTreeCard(String processTree) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_tree,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Process Tree',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity, // Take full width
              height: 400, // Increased height
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context)
                      .colorScheme
                      .outline
                      .withValues(alpha: 0.3),
                ),
              ),
              child: SingleChildScrollView(
                child: SelectableText(
                  processTree,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontFamily:
                            null, // Use default app font instead of monospace
                        fontSize: 12, // Increased from 10
                        color:
                            Colors.black, // Set to black for better readability
                        height: 1.4, // Better line spacing
                      ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Service filtering and sorting methods
  List<dynamic> _filterServices(List<dynamic> services) {
    List<dynamic> filtered = services;

    // Apply status filter
    if (_serviceStatusFilter != 'all') {
      filtered = filtered.where((service) {
        return service['active_state'] == _serviceStatusFilter;
      }).toList();
    }

    // Apply search filter
    if (_serviceSearchQuery.isNotEmpty) {
      final query = _serviceSearchQuery.toLowerCase();
      filtered = filtered.where((service) {
        final name = service['name']?.toString().toLowerCase() ?? '';
        final description =
            service['description']?.toString().toLowerCase() ?? '';
        final subState = service['sub_state']?.toString().toLowerCase() ?? '';

        return name.contains(query) ||
            description.contains(query) ||
            subState.contains(query);
      }).toList();
    }

    return filtered;
  }

  List<dynamic> _sortServices(List<dynamic> services) {
    final sortedList = List<dynamic>.from(services);

    sortedList.sort((a, b) {
      dynamic aValue = a[_serviceSortColumn];
      dynamic bValue = b[_serviceSortColumn];

      // Handle null values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return _serviceSortAscending ? -1 : 1;
      if (bValue == null) return _serviceSortAscending ? 1 : -1;

      // Handle different data types
      int comparison;
      if (aValue is num && bValue is num) {
        comparison = aValue.compareTo(bValue);
      } else {
        comparison = aValue.toString().compareTo(bValue.toString());
      }

      return _serviceSortAscending ? comparison : -comparison;
    });

    return sortedList;
  }

  void _onServiceColumnSort(String column) {
    setState(() {
      if (_serviceSortColumn == column) {
        _serviceSortAscending = !_serviceSortAscending;
      } else {
        _serviceSortColumn = column;
        _serviceSortAscending = true; // Default to ascending for service names
      }
    });
  }

  Widget _buildServiceStatusBadge(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
      ),
    );
  }

  Widget _buildEnhancedServicesTable(List<dynamic> services) {
    // Schedule cleanup operations for after the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Clean up service states for services that no longer exist
        _cleanupServiceActionMenuState(services);
        _cleanupServiceSelectionState(services);
      }
    });

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
            color:
                Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header with sortable columns
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                _buildSortableServiceHeader('Service Name', 'name', flex: 3),
                _buildSortableServiceHeader('Load', 'load_state', flex: 1),
                _buildSortableServiceHeader('Active', 'active_state', flex: 1),
                _buildSortableServiceHeader('Sub', 'sub_state', flex: 1),
                _buildSortableServiceHeader('PID', 'main_pid', flex: 1),
                _buildSortableServiceHeader('Memory', 'memory_current',
                    flex: 1),
                Expanded(
                  flex: 3,
                  child: Text(
                    'Description',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Actions',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          // Service rows
          ...services.map((service) => _buildServiceRow(service)),
        ],
      ),
    );
  }

  Widget _buildSortableServiceHeader(String title, String column,
      {int flex = 1}) {
    final isCurrentSort = _serviceSortColumn == column;

    return Expanded(
      flex: flex,
      child: InkWell(
        onTap: () => _onServiceColumnSort(column),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isCurrentSort
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
            ),
            if (isCurrentSort) ...[
              const SizedBox(width: 4),
              Icon(
                _serviceSortAscending
                    ? Icons.arrow_upward
                    : Icons.arrow_downward,
                size: 12,
                color: Theme.of(context).colorScheme.primary,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildServiceRow(Map<String, dynamic> service) {
    final activeState = service['active_state'] ?? 'unknown';
    final statusColor = _getServiceStatusColor(activeState);

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
              color:
                  Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)),
        ),
        color: _getServiceRowColor(service),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            final serviceName = service['name']?.toString();
            _selectedServiceName =
                _selectedServiceName == serviceName ? null : serviceName;
          });
        },
        child: Row(
          children: [
            // Service Name
            Expanded(
              flex: 3,
              child: Text(
                '${service['name']}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Load State
            Expanded(
              flex: 1,
              child: Text(
                '${service['load_state']}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
            // Active State with colored badge
            Expanded(
              flex: 1,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    activeState,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: statusColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 10,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            // Sub State
            Expanded(
              flex: 1,
              child: Text(
                '${service['sub_state']}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
            // PID
            Expanded(
              flex: 1,
              child: Text(
                '${service['main_pid'] ?? '--'}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
            // Memory
            Expanded(
              flex: 1,
              child: Text(
                service['memory_current'] != null &&
                        service['memory_current'] != '0'
                    ? '${(int.tryParse(service['memory_current']) ?? 0) ~/ 1024}K'
                    : '--',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
            // Description
            Expanded(
              flex: 3,
              child: Text(
                '${service['description']}',
                style: Theme.of(context).textTheme.bodySmall,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            // Actions
            Expanded(
              flex: 1,
              child: _buildServiceActions(service),
            ),
          ],
        ),
      ),
    );
  }

  Color _getServiceStatusColor(String activeState) {
    switch (activeState) {
      case 'active':
        return Colors.green;
      case 'failed':
        return Colors.red;
      case 'inactive':
        return Colors.grey;
      case 'activating':
        return Colors.orange;
      case 'deactivating':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  // Get the background color for a service row based on its state and selection
  Color? _getServiceRowColor(Map<String, dynamic> service) {
    final serviceName = service['name']?.toString();
    if (serviceName == null) return null;

    // Check if this service has an open action menu (highest priority - green)
    final hasOpenActionMenu = _actionMenuServiceName == serviceName;
    if (hasOpenActionMenu) {
      return Colors.green.withValues(alpha: 0.3);
    }

    // Check if this service is selected (second priority - primary color)
    final isSelected = _selectedServiceName == serviceName;
    if (isSelected) {
      return Theme.of(context)
          .colorScheme
          .primaryContainer
          .withValues(alpha: 0.3);
    }

    // Check if service is disabled (third priority - red tint)
    final unitFileState = service['unit_file_state']?.toString();
    if (unitFileState == 'disabled') {
      return Colors.red.withValues(alpha: 0.1);
    }

    return null;
  }

  // Record when a service action was performed for timing tracking
  void _recordServiceAction(String serviceName) {
    _lastServiceActionTime[serviceName] = DateTime.now().millisecondsSinceEpoch;
  }

  // Clean up action menu state if the service no longer exists
  void _cleanupServiceActionMenuState(List<dynamic> services) {
    if (_actionMenuServiceName != null) {
      final serviceExists = services.any(
          (service) => service['name']?.toString() == _actionMenuServiceName);

      if (!serviceExists) {
        setState(() {
          _actionMenuServiceName = null;
          _actionMenuServiceData = null;
        });
      }
    }
  }

  // Clean up selected service state if the service no longer exists
  void _cleanupServiceSelectionState(List<dynamic> services) {
    if (_selectedServiceName != null) {
      final serviceExists = services.any(
          (service) => service['name']?.toString() == _selectedServiceName);

      if (!serviceExists) {
        setState(() {
          _selectedServiceName = null;
        });
      }
    }
  }

  Widget _buildServiceActions(Map<String, dynamic> service) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        size: 16,
        color: Theme.of(context).colorScheme.onSurface,
      ),
      onOpened: () {
        // Capture service data when menu opens and highlight the row
        setState(() {
          _actionMenuServiceName = service['name']?.toString();
          _actionMenuServiceData = Map<String, dynamic>.from(service);
        });
      },
      onCanceled: () {
        // Clear highlighting when menu is canceled
        setState(() {
          _actionMenuServiceName = null;
          _actionMenuServiceData = null;
        });
      },
      onSelected: (action) {
        // Use the captured service data to ensure action applies to correct service
        final serviceData = _actionMenuServiceData;

        // Clear the action menu state
        setState(() {
          _actionMenuServiceName = null;
          _actionMenuServiceData = null;
        });

        if (serviceData != null) {
          _handleServiceAction(action, serviceData);
        }
      },
      itemBuilder: (context) {
        // Use the captured service data for menu options
        final menuService = _actionMenuServiceData ?? service;
        final menuActiveState = menuService['active_state'] ?? 'unknown';
        final menuUnitFileState = menuService['unit_file_state']?.toString();

        return [
          const PopupMenuItem(
            value: 'properties',
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16),
                SizedBox(width: 8),
                Text('Properties'),
              ],
            ),
          ),
          if (menuActiveState == 'active') ...[
            const PopupMenuItem(
              value: 'stop',
              child: Row(
                children: [
                  Icon(Icons.stop, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Stop'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'restart',
              child: Row(
                children: [
                  Icon(Icons.refresh, size: 16, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('Restart'),
                ],
              ),
            ),
          ],
          if (menuActiveState == 'inactive' || menuActiveState == 'failed') ...[
            const PopupMenuItem(
              value: 'start',
              child: Row(
                children: [
                  Icon(Icons.play_arrow, size: 16, color: Colors.green),
                  SizedBox(width: 8),
                  Text('Start'),
                ],
              ),
            ),
          ],
          const PopupMenuDivider(),
          // Only show enable if service is disabled
          if (menuUnitFileState == 'disabled') ...[
            const PopupMenuItem(
              value: 'enable',
              child: Row(
                children: [
                  Icon(Icons.check_circle_outline,
                      size: 16, color: Colors.green),
                  SizedBox(width: 8),
                  Text('Enable'),
                ],
              ),
            ),
          ],
          // Only show disable if service is enabled
          if (menuUnitFileState == 'enabled') ...[
            const PopupMenuItem(
              value: 'disable',
              child: Row(
                children: [
                  Icon(Icons.cancel_outlined, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Disable'),
                ],
              ),
            ),
          ],
        ];
      },
    );
  }

  void _handleServiceAction(String action, Map<String, dynamic> service) async {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null || !selectedDevice.isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device connected')),
      );
      return;
    }

    final serviceName = service['name'];

    switch (action) {
      case 'properties':
        _showServiceProperties(service);
        break;
      case 'start':
        _recordServiceAction(serviceName);
        _executeServiceCommand(selectedDevice, 'start', serviceName);
        // Trigger immediate UI update to show status change
        setState(() {
          // Force rebuild to show updated highlighting
        });
        break;
      case 'stop':
        _confirmServiceAction(
          'Stop Service',
          'Are you sure you want to stop the service "$serviceName"?',
          'This will stop the service and may affect system functionality.',
          () {
            _recordServiceAction(serviceName);
            _executeServiceCommand(selectedDevice, 'stop', serviceName);
            // Trigger immediate UI update to show status change
            setState(() {
              // Force rebuild to show updated highlighting
            });
          },
        );
        break;
      case 'restart':
        _recordServiceAction(serviceName);
        _executeServiceCommand(selectedDevice, 'restart', serviceName);
        // Trigger immediate UI update to show status change
        setState(() {
          // Force rebuild to show updated highlighting
        });
        break;
      case 'enable':
        _recordServiceAction(serviceName);
        _executeServiceCommand(selectedDevice, 'enable', serviceName);
        // Trigger immediate UI update to show status change
        setState(() {
          // Force rebuild to show updated highlighting
        });
        break;
      case 'disable':
        _confirmServiceAction(
          'Disable Service',
          'Are you sure you want to disable the service "$serviceName"?',
          'This will prevent the service from starting automatically.',
          () {
            _recordServiceAction(serviceName);
            _executeServiceCommand(selectedDevice, 'disable', serviceName);
            // Trigger immediate UI update to show status change
            setState(() {
              // Force rebuild to show updated highlighting
            });
          },
        );
        break;
    }
  }

  void _showServiceProperties(Map<String, dynamic> service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Service Properties: ${service['name']}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildPropertyRow('Name', service['name']),
              _buildPropertyRow('Description', service['description']),
              _buildPropertyRow('Load State', service['load_state']),
              _buildPropertyRow('Active State', service['active_state']),
              _buildPropertyRow('Sub State', service['sub_state']),
              _buildPropertyRow(
                  'Main PID', service['main_pid']?.toString() ?? 'N/A'),
              _buildPropertyRow(
                  'Memory Usage',
                  service['memory_current'] != null &&
                          service['memory_current'] != '0'
                      ? '${(int.tryParse(service['memory_current']) ?? 0) ~/ 1024}K'
                      : 'N/A'),
              if (service['unit_file_state'] != null)
                _buildPropertyRow(
                    'Unit File State', service['unit_file_state']),
              if (service['fragment_path'] != null)
                _buildPropertyRow('Unit File Path', service['fragment_path']),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value ?? 'N/A',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  void _confirmServiceAction(
      String title, String message, String warning, VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 8),
            Text(
              warning,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.orange,
                    fontStyle: FontStyle.italic,
                  ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  void _executeServiceCommand(
      Device device, String command, String serviceName) async {
    if (!mounted) return;

    final appState = Provider.of<AppState>(context, listen: false);

    // Add small delay to prevent SSH channel conflicts
    await Future.delayed(Duration(milliseconds: 50));

    if (!mounted) return;

    try {
      final connection = appState.sshConnections[device.id];

      if (connection == null ||
          !connection.isConnected ||
          connection.client == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Device not connected via SSH'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Build the systemctl command
      final systemctlCommand = 'sudo systemctl $command $serviceName';

      // Execute the command via SSH
      final session = await connection.client!.execute(systemctlCommand);
      final error = await utf8.decoder.bind(session.stderr).join();
      final output = await utf8.decoder.bind(session.stdout).join();
      final exitCode = session.exitCode;

      if (mounted) {
        // Check if command was successful (exit code 0 and no significant errors)
        final isSuccess = (exitCode == null || exitCode == 0) &&
            (error.isEmpty ||
                error.trim().isEmpty ||
                (!error.toLowerCase().contains('error') &&
                    !error.toLowerCase().contains('failed') &&
                    !error.toLowerCase().contains('permission denied') &&
                    !error.toLowerCase().contains('not found')));

        // Debug information
        debugPrint(
            'Service command debug: command="$systemctlCommand", exitCode=$exitCode, error="$error", output="$output"');

        if (isSuccess) {
          String actionText = '';
          switch (command) {
            case 'start':
              actionText = 'Service $serviceName started successfully';
              break;
            case 'stop':
              actionText = 'Service $serviceName stopped successfully';
              break;
            case 'restart':
              actionText = 'Service $serviceName restarted successfully';
              break;
            case 'enable':
              actionText = 'Service $serviceName enabled successfully';
              break;
            case 'disable':
              actionText = 'Service $serviceName disabled successfully';
              break;
            default:
              actionText =
                  'Command $command executed on $serviceName successfully';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(actionText),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
              action: SnackBarAction(
                label: 'Refresh',
                onPressed: () => _fetchSystemInfoForSelectedDevice(),
              ),
            ),
          );

          // Wait a moment then refresh to see changes
          Timer(Duration(milliseconds: 1500), () {
            if (mounted) {
              _fetchSystemInfoForSelectedDevice();
            }
          });
        } else {
          // Provide more detailed error information
          String errorMessage = 'Failed to $command service $serviceName';
          if (error.isNotEmpty) {
            errorMessage += ': $error';
          }
          if (exitCode != null && exitCode != 0) {
            errorMessage += ' (exit code: $exitCode)';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
              action: SnackBarAction(
                label: 'Refresh',
                onPressed: () => _fetchSystemInfoForSelectedDevice(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error executing service command: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Refresh',
              onPressed: () => _fetchSystemInfoForSelectedDevice(),
            ),
          ),
        );
      }
    }
  }

  // Process filtering and sorting methods
  List<dynamic> _filterProcesses(List<dynamic> processes) {
    if (_processSearchQuery.isEmpty) {
      return processes;
    }

    final query = _processSearchQuery.toLowerCase();
    return processes.where((process) {
      final command = process['cmd']?.toString().toLowerCase() ?? '';
      final user = process['user']?.toString().toLowerCase() ?? '';
      final pid = process['pid']?.toString() ?? '';

      return command.contains(query) ||
          user.contains(query) ||
          pid.contains(query);
    }).toList();
  }

  List<dynamic> _sortProcesses(List<dynamic> processes) {
    final sortedList = List<dynamic>.from(processes);

    sortedList.sort((a, b) {
      dynamic aValue = a[_processSortColumn];
      dynamic bValue = b[_processSortColumn];

      // Handle null values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return _processSortAscending ? -1 : 1;
      if (bValue == null) return _processSortAscending ? 1 : -1;

      // Handle different data types
      int comparison;
      if (aValue is num && bValue is num) {
        comparison = aValue.compareTo(bValue);
      } else {
        comparison = aValue.toString().compareTo(bValue.toString());
      }

      return _processSortAscending ? comparison : -comparison;
    });

    return sortedList;
  }

  void _onProcessColumnSort(String column) {
    setState(() {
      if (_processSortColumn == column) {
        _processSortAscending = !_processSortAscending;
      } else {
        _processSortColumn = column;
        _processSortAscending = false; // Default to descending for most columns
      }
    });
  }

  // Get the background color for a process row based on its state
  Color? _getProcessRowColor(Map<String, dynamic> process, AppState appState) {
    final deviceId = appState.selectedDeviceId;
    if (deviceId == null) return null;

    final processPid = process['pid'].toString();

    // Check if this process has an open action menu (highest priority - green)
    final hasOpenActionMenu = _actionMenuProcessPid == processPid;
    if (hasOpenActionMenu) {
      return Colors.green.withValues(alpha: 0.3);
    }

    // Check if this process is selected (second priority - primary color)
    final isSelected = _selectedProcessPid == processPid;
    if (isSelected) {
      return Theme.of(context)
          .colorScheme
          .primaryContainer
          .withValues(alpha: 0.3);
    }

    // Determine actual process state from host machine first
    final actualState = _getActualProcessState(process, appState);

    // Apply state-based colors based on actual state (lowest priority)
    switch (actualState) {
      case ProcessState.terminated:
        return Colors.orange.withValues(alpha: 0.2);
      case ProcessState.killed:
        return Colors.red.withValues(alpha: 0.2);
      case ProcessState.suspended:
        return Colors.blue.withValues(alpha: 0.2);
      case ProcessState.normal:
        return null;
    }
  }

  // Get the actual process state by checking host machine status first, then tracked state
  ProcessState _getActualProcessState(
      Map<String, dynamic> process, AppState appState) {
    final pid = process['pid'] as int;
    final deviceId = appState.selectedDeviceId;
    final processStatus = process['stat']?.toString() ?? '';

    // Check actual process status from host machine first
    if (processStatus.contains('T')) {
      // Process is actually suspended on host machine
      return ProcessState.suspended;
    } else if (processStatus.contains('Z')) {
      // Process is zombie (terminated/killed)
      final trackedState =
          deviceId != null ? appState.getProcessState(deviceId, pid) : null;
      if (trackedState == ProcessState.killed) {
        return ProcessState.killed;
      } else {
        return ProcessState.terminated;
      }
    } else {
      // Process is running normally, check tracked state
      final trackedState =
          deviceId != null ? appState.getProcessState(deviceId, pid) : null;

      // For terminated/killed states, keep them briefly for visual feedback
      if (trackedState == ProcessState.terminated) {
        return ProcessState.terminated;
      } else if (trackedState == ProcessState.killed) {
        return ProcessState.killed;
      }

      // Note: Don't clear suspended state here to avoid setState during build
      // The synchronization method will handle this with post-frame callback
      if (trackedState == ProcessState.suspended) {
        return ProcessState.suspended;
      }

      return ProcessState.normal;
    }
  }

  // Update process state - keep until process changes or disappears
  void _setProcessState(int pid, ProcessState state, AppState appState) {
    final deviceId = appState.selectedDeviceId;
    if (deviceId != null) {
      appState.setProcessState(deviceId, pid, state);
    }
  }

  // Record when an action was performed on a process
  void _recordProcessAction(int pid) {
    _lastProcessActionTime[pid] = DateTime.now().millisecondsSinceEpoch;
  }

  // Load process table column widths from SharedPreferences
  Future<void> _loadProcessColumnWidths() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedWidths = prefs.getString('process_column_widths');
      if (savedWidths != null) {
        final Map<String, dynamic> widthsMap = jsonDecode(savedWidths);
        setState(() {
          _processColumnWidths =
              widthsMap.map((key, value) => MapEntry(key, value.toDouble()));
        });
      }
    } catch (e) {
      debugPrint('Error loading process column widths: $e');
    }
  }

  // Save process table column widths to SharedPreferences
  Future<void> _saveProcessColumnWidths() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          'process_column_widths', jsonEncode(_processColumnWidths));
    } catch (e) {
      debugPrint('Error saving process column widths: $e');
    }
  }

  // Calculate the command column width to fill remaining space
  double _calculateCommandColumnWidth(double availableWidth) {
    double usedWidth = 0;
    for (final entry in _processColumnWidths.entries) {
      if (entry.key != 'cmd') {
        usedWidth += entry.value;
      }
    }

    final remainingWidth = availableWidth - usedWidth;
    final minWidth = _minColumnWidths['cmd']!;
    return remainingWidth > minWidth ? remainingWidth : minWidth;
  }

  // Update column width with performance optimization
  void _updateColumnWidth(String column, double width) {
    final minWidth = _minColumnWidths[column]!;
    final clampedWidth = width.clamp(minWidth, 500.0);

    if (_processColumnWidths[column] != clampedWidth) {
      setState(() {
        _processColumnWidths[column] = clampedWidth;
      });

      // Debounced save to avoid excessive writes during drag
      if (!_isResizing) {
        _saveProcessColumnWidths();
      }
    }
  }

  // Start resize operation
  void _startResize() {
    _isResizing = true;
  }

  // End resize operation and save
  void _endResize() {
    _isResizing = false;
    _saveProcessColumnWidths();
  }

  // Clean up states for processes that no longer exist
  void _cleanupProcessStates(
      List<dynamic> currentProcesses, AppState appState) {
    final deviceId = appState.selectedDeviceId;
    if (deviceId == null) return;

    final currentPids = currentProcesses.map((p) => p['pid'] as int).toList();

    // Use the global cleanup method
    appState.cleanupProcessStates(deviceId, currentPids);

    // Handle zombie processes - clear terminated/killed states after delay
    final deviceStates = appState.getProcessStates(deviceId);
    for (final pid in currentPids) {
      final currentState = deviceStates[pid];
      if (currentState == ProcessState.terminated ||
          currentState == ProcessState.killed) {
        // Check if terminated/killed processes became zombies
        final process = currentProcesses.firstWhere((p) => p['pid'] == pid);
        final processStatus = process['stat']?.toString() ?? '';

        if (processStatus.contains('Z')) {
          // Process became zombie, clear state after a delay
          Timer(Duration(seconds: 2), () {
            if (mounted) {
              appState.clearProcessState(deviceId, pid);
            }
          });
        }
      }
      // Don't auto-clear suspended state - let user explicitly resume
    }
  }

  // Synchronize tracked process states with actual process states from host machine
  void _synchronizeProcessStates(List<dynamic> processes, AppState appState) {
    final deviceId = appState.selectedDeviceId;
    if (deviceId == null) return;

    // Use post-frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      for (final process in processes) {
        final pid = process['pid'] as int;
        final processStatus = process['stat']?.toString() ?? '';
        final trackedState = appState.getProcessState(deviceId, pid);

        // If process is actually suspended but we don't have it tracked, track it
        if (processStatus.contains('T') &&
            trackedState != ProcessState.suspended) {
          appState.setProcessState(deviceId, pid, ProcessState.suspended);
        }
        // Only clear suspended state if enough time has passed since last action
        // This prevents immediate clearing of user-initiated suspend actions
        else if (!processStatus.contains('T') &&
            trackedState == ProcessState.suspended) {
          // Add a delay before clearing to allow SSH signal to take effect
          final now = DateTime.now().millisecondsSinceEpoch;
          final lastActionTime = _lastProcessActionTime[pid] ?? 0;

          // Only clear if more than 2 seconds have passed since last action
          if (now - lastActionTime > 2000) {
            appState.clearProcessState(deviceId, pid);
          }
        }
      }
    });
  }

  // Clean up action menu state if the process no longer exists
  void _cleanupActionMenuState(List<dynamic> processes) {
    final currentPids = processes.map((p) => p['pid'] as int).toSet();

    // Clean up action menu state
    if (_actionMenuProcessPid != null) {
      final actionMenuPid = int.tryParse(_actionMenuProcessPid!);
      if (actionMenuPid != null && !currentPids.contains(actionMenuPid)) {
        // Use post-frame callback to avoid setState during build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _actionMenuProcessPid = null;
              _actionMenuProcessData = null;
            });
          }
        });
      }
    }

    // Clean up action time tracking for processes that no longer exist
    final staleActionPids = _lastProcessActionTime.keys
        .where((pid) => !currentPids.contains(pid))
        .toList();
    for (final pid in staleActionPids) {
      _lastProcessActionTime.remove(pid);
    }
  }

  Widget _buildSortableProcessTable(List<dynamic> processes) {
    final appState = Provider.of<AppState>(context, listen: false);

    // Schedule cleanup operations for after the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Clean up process states for processes that no longer exist
        _cleanupProcessStates(processes, appState);

        // Synchronize tracked states with actual process states
        _synchronizeProcessStates(processes, appState);

        // Clear action menu state if the process no longer exists
        _cleanupActionMenuState(processes);
      }
    });

    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth =
            constraints.maxWidth - 32; // Account for padding and borders

        return Container(
          decoration: BoxDecoration(
            border: Border.all(
                color: Theme.of(context)
                    .colorScheme
                    .outline
                    .withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              // Header with resizable columns
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    _buildResizableHeader('PID', 'pid'),
                    _buildResizableHeader('PPID', 'ppid'),
                    _buildResizableHeader('USER', 'user'),
                    _buildResizableHeader('CPU%', 'cpu_percent'),
                    _buildResizableHeader('MEM%', 'mem_percent'),
                    _buildResizableHeader('READ', 'read_rate'),
                    _buildResizableHeader('WRITE', 'write_rate'),
                    _buildResizableHeader('VSZ', 'vsz'),
                    _buildResizableHeader('RSS', 'rss'),
                    _buildResizableHeader('STAT', 'stat'),
                    _buildResizableHeader('COMMAND', 'cmd',
                        availableWidth: availableWidth),
                    SizedBox(
                      width: _processColumnWidths['actions']!,
                      child: Text('Actions',
                          style: Theme.of(context)
                              .textTheme
                              .labelSmall
                              ?.copyWith(fontWeight: FontWeight.bold)),
                    ),
                  ],
                ),
              ),
              // Rows
              ...processes.map((process) => Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(
                            color: Theme.of(context)
                                .colorScheme
                                .outline
                                .withValues(alpha: 0.2)),
                      ),
                      color: _getProcessRowColor(process, appState),
                    ),
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _selectedProcessPid =
                              _selectedProcessPid == process['pid'].toString()
                                  ? null
                                  : process['pid'].toString();
                        });
                      },
                      child: Row(
                        children: [
                          SizedBox(
                            width: _processColumnWidths['pid']!,
                            child: Text('${process['pid']}',
                                style: Theme.of(context).textTheme.bodySmall),
                          ),
                          SizedBox(
                            width: _processColumnWidths['ppid']!,
                            child: Text('${process['ppid']}',
                                style: Theme.of(context).textTheme.bodySmall),
                          ),
                          SizedBox(
                            width: _processColumnWidths['user']!,
                            child: Text('${process['user']}',
                                style: Theme.of(context).textTheme.bodySmall,
                                overflow: TextOverflow.ellipsis),
                          ),
                          SizedBox(
                            width: _processColumnWidths['cpu_percent']!,
                            child: Text(
                                '${process['cpu_percent'].toStringAsFixed(1)}',
                                style: Theme.of(context).textTheme.bodySmall),
                          ),
                          SizedBox(
                            width: _processColumnWidths['mem_percent']!,
                            child: Text(
                                '${process['mem_percent'].toStringAsFixed(1)}',
                                style: Theme.of(context).textTheme.bodySmall),
                          ),
                          SizedBox(
                            width: _processColumnWidths['read_rate']!,
                            child: Text(
                                '${(process['read_rate'] ?? 0.0).toStringAsFixed(1)}M',
                                style: Theme.of(context).textTheme.bodySmall),
                          ),
                          SizedBox(
                            width: _processColumnWidths['write_rate']!,
                            child: Text(
                                '${(process['write_rate'] ?? 0.0).toStringAsFixed(1)}M',
                                style: Theme.of(context).textTheme.bodySmall),
                          ),
                          SizedBox(
                            width: _processColumnWidths['vsz']!,
                            child: Text(
                                '${(process['vsz'] / 1024).toStringAsFixed(0)}M',
                                style: Theme.of(context).textTheme.bodySmall),
                          ),
                          SizedBox(
                            width: _processColumnWidths['rss']!,
                            child: Text(
                                '${(process['rss'] / 1024).toStringAsFixed(0)}M',
                                style: Theme.of(context).textTheme.bodySmall),
                          ),
                          SizedBox(
                            width: _processColumnWidths['stat']!,
                            child: Text('${process['stat']}',
                                style: Theme.of(context).textTheme.bodySmall),
                          ),
                          Expanded(
                            child: Text('${process['cmd']}',
                                style: Theme.of(context).textTheme.bodySmall,
                                overflow: TextOverflow.ellipsis),
                          ),
                          SizedBox(
                            width: _processColumnWidths['actions']!,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Process actions menu
                                PopupMenuButton<String>(
                                  icon: Icon(Icons.more_vert, size: 16),
                                  tooltip: 'Process Actions',
                                  onOpened: () {
                                    // Capture process data when menu opens and highlight the row
                                    setState(() {
                                      _actionMenuProcessPid =
                                          process['pid'].toString();
                                      _actionMenuProcessData =
                                          Map<String, dynamic>.from(process);
                                    });
                                  },
                                  onCanceled: () {
                                    // Clear highlighting when menu is canceled
                                    setState(() {
                                      _actionMenuProcessPid = null;
                                      _actionMenuProcessData = null;
                                    });
                                  },
                                  onSelected: (action) {
                                    // Use the captured process data to ensure action applies to correct process
                                    final processData = _actionMenuProcessData;

                                    // Clear the action menu state
                                    setState(() {
                                      _actionMenuProcessPid = null;
                                      _actionMenuProcessData = null;
                                    });

                                    if (processData != null) {
                                      _handleProcessAction(action, processData);
                                    }
                                  },
                                  itemBuilder: (context) {
                                    // Use the captured process data for menu options
                                    final menuProcess =
                                        _actionMenuProcessData ?? process;
                                    final actualState = _getActualProcessState(
                                        menuProcess, appState);
                                    final isSuspended =
                                        actualState == ProcessState.suspended;

                                    return [
                                      // Header showing which process this menu is for
                                      PopupMenuItem(
                                        enabled: false,
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 4),
                                          decoration: BoxDecoration(
                                            color: Colors.green
                                                .withValues(alpha: 0.1),
                                            borderRadius:
                                                BorderRadius.circular(4),
                                          ),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Process ${menuProcess['pid']}',
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .labelSmall
                                                    ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color:
                                                          Colors.green.shade700,
                                                    ),
                                              ),
                                              Text(
                                                () {
                                                  final cmd =
                                                      '${menuProcess['cmd']}';
                                                  return cmd.length > 30
                                                      ? '${cmd.substring(0, 30)}...'
                                                      : cmd;
                                                }(),
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodySmall
                                                    ?.copyWith(
                                                      color:
                                                          Colors.green.shade600,
                                                      fontSize: 10,
                                                    ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      const PopupMenuDivider(),
                                      PopupMenuItem(
                                        value: 'properties',
                                        child: Row(
                                          children: [
                                            Icon(Icons.info_outline, size: 16),
                                            const SizedBox(width: 8),
                                            const Text('Properties'),
                                          ],
                                        ),
                                      ),
                                      PopupMenuItem(
                                        value: 'terminate',
                                        child: Row(
                                          children: [
                                            Icon(Icons.stop,
                                                size: 16, color: Colors.orange),
                                            const SizedBox(width: 8),
                                            const Text('Terminate (SIGTERM)'),
                                          ],
                                        ),
                                      ),
                                      PopupMenuItem(
                                        value: 'kill',
                                        child: Row(
                                          children: [
                                            Icon(Icons.close,
                                                size: 16, color: Colors.red),
                                            const SizedBox(width: 8),
                                            const Text('Kill (SIGKILL)'),
                                          ],
                                        ),
                                      ),
                                      // Show either Suspend or Resume based on process state
                                      if (isSuspended)
                                        PopupMenuItem(
                                          value: 'resume',
                                          child: Row(
                                            children: [
                                              Icon(Icons.play_arrow,
                                                  size: 16,
                                                  color: Colors.green),
                                              const SizedBox(width: 8),
                                              const Text('Resume (SIGCONT)'),
                                            ],
                                          ),
                                        )
                                      else
                                        PopupMenuItem(
                                          value: 'suspend',
                                          child: Row(
                                            children: [
                                              Icon(Icons.pause,
                                                  size: 16, color: Colors.blue),
                                              const SizedBox(width: 8),
                                              const Text('Suspend (SIGSTOP)'),
                                            ],
                                          ),
                                        ),
                                    ];
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  )),
            ],
          ),
        );
      },
    );
  }

  // Build a resizable header column
  Widget _buildResizableHeader(String title, String column,
      {double? availableWidth}) {
    final isCurrentSort = _processSortColumn == column;
    double width = _processColumnWidths[column]!;

    // For command column, calculate width to fill remaining space
    if (column == 'cmd' && availableWidth != null) {
      width = _calculateCommandColumnWidth(availableWidth);
    }

    return SizedBox(
      width: width,
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => _onProcessColumnSort(column),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isCurrentSort
                                ? Theme.of(context).colorScheme.primary
                                : null,
                          ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 4),
                  if (isCurrentSort)
                    Icon(
                      _processSortAscending
                          ? Icons.arrow_upward
                          : Icons.arrow_downward,
                      size: 12,
                      color: Theme.of(context).colorScheme.primary,
                    )
                  else
                    Icon(
                      Icons.unfold_more,
                      size: 12,
                      color: Theme.of(context)
                          .colorScheme
                          .onSurfaceVariant
                          .withValues(alpha: 0.5),
                    ),
                ],
              ),
            ),
          ),
          // Resize handle (not for command column as it auto-fills)
          if (column != 'cmd')
            MouseRegion(
              cursor: SystemMouseCursors.resizeColumn,
              child: GestureDetector(
                onPanStart: (_) => _startResize(),
                onPanEnd: (_) => _endResize(),
                onPanUpdate: (details) {
                  final newWidth = width + details.delta.dx;
                  _updateColumnWidth(column, newWidth);
                },
                child: Container(
                  width: 8,
                  height: 20,
                  color: Colors.transparent,
                  child: Center(
                    child: Container(
                      width: 2,
                      height: 16,
                      color: Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Table builder methods for detailed information display
  Widget _buildProcessTable(List<dynamic> processes) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
            color:
                Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                    flex: 1,
                    child: Text('PID',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 2,
                    child: Text('User',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('CPU%',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('MEM%',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 3,
                    child: Text('Command',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
              ],
            ),
          ),
          // Rows
          ...processes.take(5).map((process) => Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                        color: Theme.of(context)
                            .colorScheme
                            .outline
                            .withValues(alpha: 0.2)),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                        flex: 1,
                        child: Text('${process['pid']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 2,
                        child: Text('${process['user']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${process['cpu_percent'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${process['mem_percent'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 3,
                        child: Text('${process['command']}',
                            style: Theme.of(context).textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis)),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildTopProcessesTable(List<dynamic> processes) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
            color:
                Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                    flex: 1,
                    child: Text('PID',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 2,
                    child: Text('User',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('CPU%',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('MEM%',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('State',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 3,
                    child: Text('Command',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
              ],
            ),
          ),
          // Rows
          ...processes.take(10).map((process) => Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                        color: Theme.of(context)
                            .colorScheme
                            .outline
                            .withValues(alpha: 0.2)),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                        flex: 1,
                        child: Text('${process['pid']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 2,
                        child: Text('${process['user']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${process['cpu_percent'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${process['mem_percent'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text('${process['stat']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 3,
                        child: Text('${process['command']}',
                            style: Theme.of(context).textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis)),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  void _handleProcessAction(String action, Map<String, dynamic> process) async {
    final appState = Provider.of<AppState>(context, listen: false);
    final selectedDevice = appState.selectedDevice;

    if (selectedDevice == null || !selectedDevice.isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No device connected')),
      );
      return;
    }

    switch (action) {
      case 'properties':
        _showProcessProperties(process);
        break;
      case 'terminate':
        _confirmProcessAction(
          'Terminate Process',
          'Are you sure you want to terminate process ${process['pid']} (${process['cmd']})?',
          'This will send a SIGTERM signal to the process.',
          () {
            final pid = process['pid'] as int;
            _recordProcessAction(pid);
            _setProcessState(pid, ProcessState.terminated, appState);
            _executeProcessTerminate(selectedDevice, process, appState);
            // Trigger immediate UI update to show status change
            setState(() {
              // Force rebuild to show updated highlighting
            });
          },
        );
        break;
      case 'kill':
        _confirmProcessAction(
          'Kill Process',
          'Are you sure you want to kill process ${process['pid']} (${process['cmd']})?',
          'This will forcefully terminate the process with SIGKILL. This action cannot be undone.',
          () {
            final pid = process['pid'] as int;
            _recordProcessAction(pid);
            _setProcessState(pid, ProcessState.killed, appState);
            _executeProcessSignal(selectedDevice, pid, 'KILL');
            // Trigger immediate UI update to show status change
            setState(() {
              // Force rebuild to show updated highlighting
            });
          },
        );
        break;
      case 'suspend':
        final pid = process['pid'] as int;
        _recordProcessAction(pid);
        _setProcessState(pid, ProcessState.suspended, appState);
        _executeProcessSignal(selectedDevice, pid, 'STOP');
        // Trigger immediate UI update to show status change
        setState(() {
          // Force rebuild to show updated highlighting
        });
        break;
      case 'resume':
        final pid = process['pid'] as int;
        _recordProcessAction(pid);
        // Clear the suspended state when resuming
        appState.clearProcessState(appState.selectedDeviceId!, pid);
        _executeProcessSignal(selectedDevice, pid, 'CONT');
        // Trigger immediate UI update to show status change
        setState(() {
          // Force rebuild to show updated highlighting
        });
        break;
    }
  }

  void _showProcessProperties(Map<String, dynamic> process) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.info_outline,
                color: Theme.of(context).colorScheme.primary),
            const SizedBox(width: 8),
            Text('Process Properties'),
          ],
        ),
        content: SizedBox(
          width: 600,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildPropertyRow('Process ID (PID)', '${process['pid']}'),
                _buildPropertyRow('Parent PID (PPID)', '${process['ppid']}'),
                _buildPropertyRow('User', '${process['user']}'),
                _buildPropertyRow('CPU Usage',
                    '${process['cpu_percent'].toStringAsFixed(1)}%'),
                _buildPropertyRow('Memory Usage',
                    '${process['mem_percent'].toStringAsFixed(1)}%'),
                _buildPropertyRow('Virtual Memory (VSZ)',
                    '${(process['vsz'] / 1024).toStringAsFixed(0)} MB'),
                _buildPropertyRow('Resident Memory (RSS)',
                    '${(process['rss'] / 1024).toStringAsFixed(0)} MB'),
                _buildPropertyRow('Disk Usage',
                    '${(process['disk_usage'] ?? 0.0).toStringAsFixed(1)} MB'),
                _buildPropertyRow(
                    'Read Bytes', '${(process['read_bytes'] ?? 0)} bytes'),
                _buildPropertyRow(
                    'Write Bytes', '${(process['write_bytes'] ?? 0)} bytes'),
                _buildPropertyRow('Terminal (TTY)', '${process['tty']}'),
                _buildPropertyRow('Status', '${process['stat']}'),
                _buildPropertyRow('Start Time', '${process['start']}'),
                _buildPropertyRow('CPU Time', '${process['time']}'),
                const SizedBox(height: 16),
                Text(
                  'Command Line',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  child: SelectableText(
                    '${process['cmd']}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontFamily: 'monospace',
                        ),
                  ),
                ),
                const SizedBox(height: 20),

                // Process Management Actions Explanation
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context)
                          .colorScheme
                          .outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Process Management Actions',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 8),
                      _buildActionExplanation(
                        'Terminate (SIGTERM)',
                        'Politely asks the process to shut down. The process can catch this signal and perform cleanup operations before exiting. This is the preferred method for stopping processes.',
                        Colors.orange,
                        Icons.stop,
                      ),
                      const SizedBox(height: 8),
                      _buildActionExplanation(
                        'Kill (SIGKILL)',
                        'Immediately terminates the process without allowing it to perform cleanup. Use this only when Terminate doesn\'t work. Cannot be caught or ignored by the process.',
                        Colors.red,
                        Icons.close,
                      ),
                      const SizedBox(height: 8),
                      _buildActionExplanation(
                        'Suspend (SIGSTOP)',
                        'Pauses the process execution. The process stops running but remains in memory. Can be resumed later.',
                        Colors.blue,
                        Icons.pause,
                      ),
                      const SizedBox(height: 8),
                      _buildActionExplanation(
                        'Resume (SIGCONT)',
                        'Resumes a suspended process. The process continues execution from where it was paused.',
                        Colors.green,
                        Icons.play_arrow,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildActionExplanation(
      String title, String description, Color color, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _confirmProcessAction(
      String title, String message, String warning, VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.orange, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      warning,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  void _executeProcessSignal(Device device, int pid, String signal) async {
    if (!mounted) return;

    final appState = Provider.of<AppState>(context, listen: false);

    // Add small delay to prevent SSH channel conflicts
    await Future.delayed(Duration(milliseconds: 50));

    if (!mounted) return;

    try {
      final connection = appState.sshConnections[device.id];

      if (connection == null ||
          !connection.isConnected ||
          connection.client == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Device not connected via SSH'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Get OS information to determine the correct command
      final osInfo = device.systemInfo['os'] as Map<String, dynamic>?;
      final osSystem = osInfo?['system'] ?? 'Linux';

      String command;
      if (osSystem == 'Windows') {
        // Windows process management
        switch (signal) {
          case 'TERM':
          case 'KILL':
            command = 'taskkill /PID $pid /F'; // Force terminate
            break;
          case 'STOP':
            command = 'powershell "Suspend-Process -Id $pid"';
            break;
          case 'CONT':
            command = 'powershell "Resume-Process -Id $pid"';
            break;
          default:
            command = 'taskkill /PID $pid /F';
        }
      } else {
        // Unix/Linux/macOS process management
        command = 'kill -$signal $pid';
      }

      // Execute the command via SSH
      final session = await connection.client!.execute(command);
      final error = await utf8.decoder.bind(session.stderr).join();
      final output = await utf8.decoder.bind(session.stdout).join();
      final exitCode = session.exitCode;

      if (mounted) {
        // Check if command was successful (exit code 0 and no significant errors)
        final isSuccess = (exitCode == null || exitCode == 0) &&
            (error.isEmpty ||
                error.trim().isEmpty ||
                (!error.toLowerCase().contains('error') &&
                    !error.toLowerCase().contains('failed') &&
                    !error.toLowerCase().contains('permission denied') &&
                    !error.toLowerCase().contains('no such process')));

        // Debug information
        debugPrint(
            'Process signal debug: command="$command", exitCode=$exitCode, error="$error", output="$output"');

        if (isSuccess) {
          String actionText = '';
          switch (signal) {
            case 'STOP':
              actionText = 'Process $pid suspended successfully';
              break;
            case 'CONT':
              actionText = 'Process $pid resumed successfully';
              break;
            case 'TERM':
              actionText = 'Signal SIGTERM sent to process $pid successfully';
              break;
            case 'KILL':
              actionText = 'Signal SIGKILL sent to process $pid successfully';
              break;
            default:
              actionText = 'Signal $signal sent to process $pid successfully';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(actionText),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 3),
            ),
          );

          // For SIGTERM and SIGKILL, wait a moment then refresh to see if process is gone
          if (signal == 'TERM' || signal == 'KILL') {
            Timer(Duration(milliseconds: 1500), () {
              if (mounted) {
                _fetchSystemInfoForSelectedDevice();
              }
            });
          } else {
            // For SIGSTOP and SIGCONT, refresh immediately to see state change
            _fetchSystemInfoForSelectedDevice();
          }
        } else {
          // Provide more detailed error information
          String errorMessage = 'Failed to send signal $signal to process $pid';
          if (error.isNotEmpty) {
            errorMessage += ': $error';
          }
          if (exitCode != null && exitCode != 0) {
            errorMessage += ' (exit code: $exitCode)';
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 5),
            ),
          );

          // If suspend/resume failed, revert our state tracking
          if (signal == 'STOP' || signal == 'CONT') {
            final deviceId = appState.selectedDeviceId;
            if (deviceId != null) {
              if (signal == 'STOP') {
                // Suspend failed, clear the suspended state we set
                appState.clearProcessState(deviceId, pid);
              } else if (signal == 'CONT') {
                // Resume failed, restore the suspended state
                appState.setProcessState(deviceId, pid, ProcessState.suspended);
              }
            }
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error executing command: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Special method to handle terminating processes that might be suspended
  void _executeProcessTerminate(
      Device device, Map<String, dynamic> process, AppState appState) async {
    final pid = process['pid'] as int;
    final processStatus = process['stat']?.toString() ?? '';

    // If process is suspended, we need to resume it first before terminating
    if (processStatus.contains('T')) {
      debugPrint('Process $pid is suspended, resuming before terminating');

      // First resume the process
      _executeProcessSignal(device, pid, 'CONT');

      // Wait a moment for the process to resume
      await Future.delayed(Duration(milliseconds: 500));

      // Then terminate it
      _executeProcessSignal(device, pid, 'TERM');
    } else {
      // Process is not suspended, terminate normally
      _executeProcessSignal(device, pid, 'TERM');
    }
  }

  Widget _buildFilesystemTable(List<dynamic> filesystems) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
            color:
                Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                    flex: 2,
                    child: Text('Filesystem',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('Size',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('Used',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('Avail',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('Use%',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 2,
                    child: Text('Mount',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
              ],
            ),
          ),
          // Rows
          ...filesystems.map((fs) => Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                        color: Theme.of(context)
                            .colorScheme
                            .outline
                            .withValues(alpha: 0.2)),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                        flex: 2,
                        child: Text('${fs['filesystem']}',
                            style: Theme.of(context).textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis)),
                    Expanded(
                        flex: 1,
                        child: Text('${fs['size']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text('${fs['used']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text('${fs['available']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text('${fs['usage_percent']}%',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 2,
                        child: Text('${fs['mount_point']}',
                            style: Theme.of(context).textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis)),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildDiskPerformanceTable(List<dynamic> performance) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
            color:
                Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                    flex: 2,
                    child: Text('Device',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('R/s',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('W/s',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('rKB/s',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('wKB/s',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('Queue',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('Util%',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
              ],
            ),
          ),
          // Rows
          ...performance.map((perf) => Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                        color: Theme.of(context)
                            .colorScheme
                            .outline
                            .withValues(alpha: 0.2)),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                        flex: 2,
                        child: Text('${perf['device']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${perf['reads_per_sec'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${perf['writes_per_sec'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${perf['kb_read_per_sec'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${perf['kb_written_per_sec'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${perf['avg_queue_size'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text(
                            '${perf['utilization_percent'].toStringAsFixed(1)}',
                            style: Theme.of(context).textTheme.bodySmall)),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildBlockDevicesTable(List<dynamic> blockDevices) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
            color:
                Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                    flex: 2,
                    child: Text('Name',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('Size',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('Type',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 2,
                    child: Text('Mount',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('FS',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
              ],
            ),
          ),
          // Rows
          ...blockDevices.map((device) => Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                        color: Theme.of(context)
                            .colorScheme
                            .outline
                            .withValues(alpha: 0.2)),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                        flex: 2,
                        child: Text('${device['name'] ?? 'N/A'}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text('${device['size'] ?? 'N/A'}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text('${device['type'] ?? 'N/A'}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 2,
                        child: Text('${device['mountpoint'] ?? 'N/A'}',
                            style: Theme.of(context).textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis)),
                    Expanded(
                        flex: 1,
                        child: Text('${device['fstype'] ?? 'N/A'}',
                            style: Theme.of(context).textTheme.bodySmall)),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildUsbDevicesTable(List<dynamic> usbDevices) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
            color:
                Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                    flex: 1,
                    child: Text('Bus',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 1,
                    child: Text('Device',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 2,
                    child: Text('ID',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
                Expanded(
                    flex: 4,
                    child: Text('Description',
                        style: Theme.of(context)
                            .textTheme
                            .labelSmall
                            ?.copyWith(fontWeight: FontWeight.bold))),
              ],
            ),
          ),
          // Rows
          ...usbDevices.map((usb) => Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                        color: Theme.of(context)
                            .colorScheme
                            .outline
                            .withValues(alpha: 0.2)),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                        flex: 1,
                        child: Text('${usb['bus']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 1,
                        child: Text('${usb['device']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 2,
                        child: Text('${usb['id']}',
                            style: Theme.of(context).textTheme.bodySmall)),
                    Expanded(
                        flex: 4,
                        child: Text('${usb['description']}',
                            style: Theme.of(context).textTheme.bodySmall,
                            overflow: TextOverflow.ellipsis)),
                  ],
                ),
              )),
        ],
      ),
    );
  }
}

// Simple chart painter for performance graphs
class SimpleChartPainter extends CustomPainter {
  final List<double> data;
  final Color color;

  SimpleChartPainter(this.data, this.color);

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty || data.length < 2) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final fillPaint = Paint()
      ..color = color.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    final path = Path();
    final fillPath = Path();

    // Find min and max values for scaling
    final minValue = data.reduce((a, b) => a < b ? a : b);
    final maxValue = data.reduce((a, b) => a > b ? a : b);
    final range = maxValue - minValue;

    if (range == 0) return; // Avoid division by zero

    // Calculate points
    final points = <Offset>[];
    for (int i = 0; i < data.length; i++) {
      final x = (i / (data.length - 1)) * size.width;
      final normalizedValue = (data[i] - minValue) / range;
      final y = size.height - (normalizedValue * size.height);
      points.add(Offset(x, y));
    }

    // Create line path
    if (points.isNotEmpty) {
      path.moveTo(points.first.dx, points.first.dy);
      for (int i = 1; i < points.length; i++) {
        path.lineTo(points[i].dx, points[i].dy);
      }
    }

    // Create fill path
    if (points.isNotEmpty) {
      fillPath.moveTo(points.first.dx, size.height);
      fillPath.lineTo(points.first.dx, points.first.dy);
      for (int i = 1; i < points.length; i++) {
        fillPath.lineTo(points[i].dx, points[i].dy);
      }
      fillPath.lineTo(points.last.dx, size.height);
      fillPath.close();
    }

    // Draw fill first, then line
    canvas.drawPath(fillPath, fillPaint);
    canvas.drawPath(path, paint);

    // Draw points
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    for (final point in points) {
      canvas.drawCircle(point, 2.0, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! SimpleChartPainter ||
        oldDelegate.data != data ||
        oldDelegate.color != color;
  }
}
