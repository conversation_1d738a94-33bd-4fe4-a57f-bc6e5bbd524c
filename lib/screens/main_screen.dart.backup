import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import '../services/app_state.dart';
import '../services/mac_vendor_service.dart';
import 'ssh_screen.dart';
import 'apps_screen.dart';
import 'settings_screen.dart';
import '../widgets/add_device_dialog.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;
  final Set<String> _shownNotifications =
      {}; // Track which notifications we've already shown

  void _showPendingNotifications(BuildContext context, AppState appState) {
    for (int i = 0; i < appState.pendingNotifications.length; i++) {
      final notification = appState.pendingNotifications[i];
      final notificationId =
          '${notification['type']}_${notification['timestamp']}';

      // Only show each notification once
      if (!_shownNotifications.contains(notificationId)) {
        _shownNotifications.add(notificationId);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('${notification['title']}: ${notification['message']}'),
            backgroundColor: notification['type'] == 'connection_lost'
                ? Colors.red
                : Colors.blue,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Dismiss',
              onPressed: () {
                appState.clearNotification(i);
              },
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        // Show pending notifications
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showPendingNotifications(context, appState);
        });

        return Scaffold(
          body: Row(
            children: [
              // Dark Sidebar
              Container(
                width: 240,
                color: const Color(0xFF374151),
                child: Column(
                  children: [
                    // Header
                    Container(
                      height: 64,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: const Row(
                        children: [
                          Icon(Icons.computer, color: Colors.white, size: 24),
                          SizedBox(width: 12),
                          Text(
                            'Jelly Pi',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Navigation Items
                    Expanded(
                      child: ListView(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        children: [
                          _buildNavItem(
                            icon: Icons.computer,
                            label: 'Manage Devices',
                            isSelected: _selectedIndex == 0,
                            onTap: () => setState(() => _selectedIndex = 0),
                          ),
                          _buildNavItem(
                            icon: Icons.terminal,
                            label: 'Terminal',
                            isSelected: _selectedIndex == 1,
                            onTap: () => setState(() => _selectedIndex = 1),
                          ),
                          _buildNavItem(
                            icon: Icons.apps,
                            label: 'Apps',
                            isSelected: _selectedIndex == 2,
                            onTap: () => setState(() => _selectedIndex = 2),
                          ),
                        ],
                      ),
                    ),
                    // Settings at bottom
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: _buildNavItem(
                        icon: Icons.settings,
                        label: 'Settings',
                        isSelected: _selectedIndex == 3,
                        onTap: () => setState(() => _selectedIndex = 3),
                      ),
                    ),
                  ],
                ),
              ),
              // Main Content Area
              Expanded(
                child: _getSelectedScreen(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Colors.white : Colors.grey[400],
          size: 20,
        ),
        title: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[400],
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        selectedTileColor: Colors.white.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6),
        ),
        onTap: onTap,
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  Widget _getSelectedScreen() {
    switch (_selectedIndex) {
      case 0:
        return const DeviceManagementScreen();
      case 1:
        return const SSHScreen();
      case 2:
        return const AppsScreen();
      case 3:
        return const SettingsScreen();
      default:
        return const DeviceManagementScreen();
    }
  }
}

// New Device Management Screen that matches your design
class DeviceManagementScreen extends StatefulWidget {
  const DeviceManagementScreen({super.key});

  @override
  State<DeviceManagementScreen> createState() => _DeviceManagementScreenState();
}

class _DeviceManagementScreenState extends State<DeviceManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isGridView = false; // false = list view, true = grid view

  // Network discovery state
  List<Map<String, String>> _discoveredDevices = [];
  bool _isScanning = false;
  bool _showAllDevices = false;
  static const List<String> _rpiMacPrefixes = [
    '28:CD:C1',
    '3A:35:41',
    'D8:3A:DD',
    'E4:5F:01',
    '2C:CF:67',
    'B8:27:EB',
    'DC:A6:32'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Set initial view based on user preference
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = context.read<AppState>();
      setState(() {
        _isGridView = appState.defaultGridView;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF8FAFC),
      child: Column(
        children: [
          // Header
          Container(
            height: 64,
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              children: [
                const Icon(Icons.computer, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Device Management',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2937),
                  ),
                ),
                const Spacer(),
                // Beta badge like in your design
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'BETA',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Tab Bar
          Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: const Color(0xFF6366F1),
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: const Color(0xFF6366F1),
              tabs: const [
                Tab(
                  icon: Icon(Icons.list),
                  text: 'Managed Devices',
                ),
                Tab(
                  icon: Icon(Icons.add),
                  text: 'Add Devices',
                ),
              ],
            ),
          ),
          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildManagedDevicesTab(),
                _buildAddDevicesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagedDevicesTab() {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Container(
          padding: const EdgeInsets.fromLTRB(
              24, 24, 16, 24), // Reduced right padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with device selection and controls
              LayoutBuilder(
                builder: (context, constraints) {
                  final isNarrow = constraints.maxWidth < 600;

                  if (isNarrow) {
                    // Narrow layout: stack vertically
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Expanded(
                              child: Text(
                                'Your Devices',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF1F2937),
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                context.read<AppState>().refreshAllDevices();
                              },
                              icon: const Icon(Icons.refresh),
                              tooltip: 'Refresh',
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            // Device Selection Dropdown
                            if (appState.devices.isNotEmpty) ...[
                              Expanded(
                                child: Container(
                                  height: 40,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                  decoration: BoxDecoration(
                                    border:
                                        Border.all(color: Colors.grey[300]!),
                                    borderRadius: BorderRadius.circular(6),
                                    color: Colors.white,
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton<String>(
                                      value: appState.selectedDeviceId,
                                      hint: const Text('Select Device'),
                                      isDense: true,
                                      isExpanded: true,
                                      items: [
                                        const DropdownMenuItem<String>(
                                          value: null,
                                          child: Text('No device selected'),
                                        ),
                                        ...appState.devices.map((device) =>
                                            DropdownMenuItem<String>(
                                              value: device.id,
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(
                                                    device.isConnected
                                                        ? Icons.check_circle
                                                        : Icons.circle_outlined,
                                                    size: 16,
                                                    color: device.isConnected
                                                        ? Colors.green
                                                        : Colors.grey,
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Flexible(
                                                      child: Text(device.name)),
                                                  if (device
                                                      .isDefaultDevice) ...[
                                                    const SizedBox(width: 8),
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 6,
                                                          vertical: 2),
                                                      decoration: BoxDecoration(
                                                        color: Colors.blue[100],
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10),
                                                      ),
                                                      child: Text(
                                                        'DEFAULT',
                                                        style: TextStyle(
                                                          fontSize: 10,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          color:
                                                              Colors.blue[800],
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ],
                                              ),
                                            )),
                                      ],
                                      onChanged: (String? deviceId) {
                                        appState.selectDevice(deviceId);
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ],
                            const Spacer(), // Push icons to the right
                            // View toggle buttons - aligned to the right
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                    border:
                                        Border.all(color: Colors.grey[300]!),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        onPressed: () {
                                          setState(() {
                                            _isGridView = false;
                                          });
                                        },
                                        icon: const Icon(Icons.view_list),
                                        tooltip: 'List View',
                                        color: !_isGridView
                                            ? const Color(0xFF374151)
                                            : Colors.grey,
                                        style: IconButton.styleFrom(
                                          backgroundColor: !_isGridView
                                              ? const Color(0xFF374151)
                                                  .withValues(alpha: 0.1)
                                              : null,
                                        ),
                                      ),
                                      Container(
                                        width: 1,
                                        height: 24,
                                        color: Colors.grey[300],
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          setState(() {
                                            _isGridView = true;
                                          });
                                        },
                                        icon: const Icon(Icons.grid_view),
                                        tooltip: 'Grid View',
                                        color: _isGridView
                                            ? const Color(0xFF374151)
                                            : Colors.grey,
                                        style: IconButton.styleFrom(
                                          backgroundColor: _isGridView
                                              ? const Color(0xFF374151)
                                                  .withValues(alpha: 0.1)
                                              : null,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 8),
                                IconButton(
                                  onPressed: () {
                                    context
                                        .read<AppState>()
                                        .refreshAllDevices();
                                  },
                                  icon: const Icon(Icons.refresh),
                                  tooltip: 'Refresh',
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    );
                  } else {
                    // Wide layout: single row with proper alignment
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Left side: Title and dropdown
                        Expanded(
                          child: Row(
                            children: [
                              const Text(
                                'Your Devices',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF1F2937),
                                ),
                              ),
                              const SizedBox(width: 16),
                              // Device Selection Dropdown
                              if (appState.devices.isNotEmpty) ...[
                                Flexible(
                                  child: Container(
                                    height: 40,
                                    constraints: const BoxConstraints(
                                      minWidth: 150,
                                      maxWidth: 300, // Increased max width
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12),
                                    decoration: BoxDecoration(
                                      border:
                                          Border.all(color: Colors.grey[300]!),
                                      borderRadius: BorderRadius.circular(6),
                                      color: Colors.white,
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton<String>(
                                        value: appState.connectedDevices.any(
                                                (d) =>
                                                    d.id ==
                                                    appState.selectedDeviceId)
                                            ? appState.selectedDeviceId
                                            : null,
                                        hint: Text(
                                            appState.connectedDevices.isEmpty
                                                ? 'Connecting devices...'
                                                : 'Select Device'),
                                        isDense: true,
                                        isExpanded:
                                            true, // Allow dropdown to expand
                                        // Disable dropdown if no devices are connected
                                        onChanged: appState
                                                .connectedDevices.isEmpty
                                            ? null
                                            : (String? deviceId) {
                                                appState.selectDevice(deviceId);
                                              },
                                        items: [
                                          // Only show "No device selected" if there are no connected devices
                                          if (appState.connectedDevices.isEmpty)
                                            const DropdownMenuItem<String>(
                                              value: null,
                                              child: Text('No device selected'),
                                            ),
                                          // Only show connected devices in the dropdown
                                          ...appState.connectedDevices.map(
                                              (device) =>
                                                  DropdownMenuItem<String>(
                                                    value: device.id,
                                                    child: Row(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Icon(
                                                          Icons.check_circle,
                                                          size: 16,
                                                          color: Colors.green,
                                                        ),
                                                        const SizedBox(
                                                            width: 8),
                                                        Flexible(
                                                            child: Text(
                                                                device.name)),
                                                        if (device
                                                            .isDefaultDevice) ...[
                                                          const SizedBox(
                                                              width: 8),
                                                          Container(
                                                            padding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    horizontal:
                                                                        6,
                                                                    vertical:
                                                                        2),
                                                            decoration:
                                                                BoxDecoration(
                                                              color: Colors
                                                                  .blue[100],
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          10),
                                                            ),
                                                            child: Text(
                                                              'DEFAULT',
                                                              style: TextStyle(
                                                                fontSize: 10,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .blue[800],
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ],
                                                    ),
                                                  )),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        // Right side: View toggle buttons - aligned to the right
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  IconButton(
                                    onPressed: () {
                                      setState(() {
                                        _isGridView = false;
                                      });
                                    },
                                    icon: const Icon(Icons.view_list),
                                    tooltip: 'List View',
                                    color: !_isGridView
                                        ? const Color(0xFF374151)
                                        : Colors.grey,
                                    style: IconButton.styleFrom(
                                      backgroundColor: !_isGridView
                                          ? const Color(0xFF374151)
                                              .withValues(alpha: 0.1)
                                          : null,
                                    ),
                                  ),
                                  Container(
                                    width: 1,
                                    height: 24,
                                    color: Colors.grey[300],
                                  ),
                                  IconButton(
                                    onPressed: () {
                                      setState(() {
                                        _isGridView = true;
                                      });
                                    },
                                    icon: const Icon(Icons.grid_view),
                                    tooltip: 'Grid View',
                                    color: _isGridView
                                        ? const Color(0xFF374151)
                                        : Colors.grey,
                                    style: IconButton.styleFrom(
                                      backgroundColor: _isGridView
                                          ? const Color(0xFF374151)
                                              .withValues(alpha: 0.1)
                                          : null,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: () {
                                context.read<AppState>().refreshAllDevices();
                              },
                              icon: const Icon(Icons.refresh),
                              tooltip: 'Refresh',
                            ),
                          ],
                        ),
                      ],
                    );
                  }
                },
              ),
              const SizedBox(height: 16),
              // Device List
              Expanded(
                child: appState.devices.isEmpty
                    ? _buildEmptyState()
                    : _isGridView
                        ? LayoutBuilder(
                            builder: (context, constraints) {
                              // Responsive grid based on available width
                              int crossAxisCount = 3;
                              if (constraints.maxWidth < 800) {
                                crossAxisCount = 2;
                              }
                              if (constraints.maxWidth < 500) {
                                crossAxisCount = 1;
                              }

                              return MasonryGridView.count(
                                crossAxisCount: crossAxisCount,
                                crossAxisSpacing: 16,
                                mainAxisSpacing: 16,
                                itemCount: appState.devices.length,
                                itemBuilder: (context, index) {
                                  final device = appState.devices[index];
                                  return _buildDeviceGridCard(device);
                                },
                              );
                            },
                          )
                        : ListView.builder(
                            itemCount: appState.devices.length,
                            itemBuilder: (context, index) {
                              final device = appState.devices[index];
                              return _buildDeviceCard(device);
                            },
                          ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.devices_other,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No devices added yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Add devices using the "Add Devices" tab',
            style: TextStyle(
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceCard(device) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Use horizontal layout for wider screens, vertical for narrow
          final isWideLayout = constraints.maxWidth > 800;

          if (isWideLayout) {
            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left side: Device Info
                Expanded(
                  flex: 2,
                  child: Row(
                    children: [
                      // Device Icon
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          device.vendor.toLowerCase().contains('raspberry')
                              ? Icons.memory
                              : Icons.computer,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Device Details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              device.name,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF1F2937),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              device.ip,
                              style: TextStyle(
                                fontSize: 14,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withValues(alpha: 0.7),
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              'MAC: ${device.mac}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withValues(alpha: 0.6),
                              ),
                            ),
                            Text(
                              'Vendor: ${device.vendor}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withValues(alpha: 0.6),
                              ),
                            ),
                            const SizedBox(height: 8),
                            // Status and Health indicators
                            Row(
                              children: [
                                // Status indicator
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: _getStatusColor(device)
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: _getStatusColor(device)
                                          .withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        _getStatusIcon(device),
                                        size: 12,
                                        color: _getStatusColor(device),
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        _getStatusText(device),
                                        style: TextStyle(
                                          fontSize: 11,
                                          fontWeight: FontWeight.w500,
                                          color: _getStatusColor(device),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // OS Badge
                                if (device.operatingSystem != 'Unknown')
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: _getOSColor(device.operatingSystem)
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color:
                                            _getOSColor(device.operatingSystem)
                                                .withValues(alpha: 0.3),
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          _getOSIcon(device.operatingSystem),
                                          size: 12,
                                          color: _getOSColor(
                                              device.operatingSystem),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          _getOSDisplayText(
                                              device.operatingSystem),
                                          style: TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.w500,
                                            color: _getOSColor(
                                                device.operatingSystem),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                // Raspberry Pi Model Badge
                                if (_isRaspberryPiDevice(device) &&
                                    _getRaspberryPiModel(device) != null) ...[
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFE91E63)
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: const Color(0xFFE91E63)
                                            .withValues(alpha: 0.3),
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.memory,
                                          size: 12,
                                          color: const Color(0xFFE91E63),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          _getRaspberryPiModel(device)!,
                                          style: const TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.w500,
                                            color: Color(0xFFE91E63),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                                // Health indicator (only when connected)
                                if (device.isConnected &&
                                    device.hasSystemInfo) ...[
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 4),
                                    decoration: BoxDecoration(
                                      color:
                                          _getHealthColor(device.healthStatus)
                                              .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color:
                                            _getHealthColor(device.healthStatus)
                                                .withValues(alpha: 0.3),
                                        width: 1,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.health_and_safety,
                                          size: 12,
                                          color: _getHealthColor(
                                              device.healthStatus),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          device.healthStatus,
                                          style: TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.w500,
                                            color: _getHealthColor(
                                                device.healthStatus),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Right side: System Health Metrics (only when connected)
                if (device.isConnected && device.hasSystemInfo) ...[
                  const SizedBox(width: 24),
                  Expanded(
                    flex: 3,
                    child: _buildHorizontalHealthMetrics(device),
                  ),
                ],
                // Action Menu
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.grey),
                  enabled: device.status != DeviceStatus.rebooting &&
                      device.status != DeviceStatus.shuttingDown,
                  onSelected: (value) {
                    switch (value) {
                      case 'connect':
                        _connectToDevice(device);
                        break;
                      case 'disconnect':
                        _disconnectFromDevice(device);
                        break;
                      case 'edit':
                        _editDevice(device);
                        break;
                      case 'delete':
                        _deleteDevice(device);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: device.isConnected ? 'disconnect' : 'connect',
                      child: Row(
                        children: [
                          Icon(device.isConnected ? Icons.link_off : Icons.link,
                              size: 16),
                          const SizedBox(width: 8),
                          Text(device.isConnected ? 'Disconnect' : 'Connect'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 16),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 16, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            );
          } else {
            // Narrow layout: vertical stacking (original layout)
            return Row(
              children: [
                // Device Icon
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    device.vendor.toLowerCase().contains('raspberry')
                        ? Icons.memory
                        : Icons.computer,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 16),
                // Device Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        device.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        device.ip,
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.7),
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        'MAC: ${device.mac}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.6),
                        ),
                      ),
                      Text(
                        'Vendor: ${device.vendor}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withValues(alpha: 0.6),
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Status and Health indicators
                      Row(
                        children: [
                          // Status indicator
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(device)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _getStatusColor(device)
                                    .withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getStatusIcon(device),
                                  size: 12,
                                  color: _getStatusColor(device),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _getStatusText(device),
                                  style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                    color: _getStatusColor(device),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // OS Badge
                          if (device.operatingSystem != 'Unknown') ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getOSColor(device.operatingSystem)
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: _getOSColor(device.operatingSystem)
                                      .withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    _getOSIcon(device.operatingSystem),
                                    size: 12,
                                    color: _getOSColor(device.operatingSystem),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _getOSDisplayText(device.operatingSystem),
                                    style: TextStyle(
                                      fontSize: 11,
                                      fontWeight: FontWeight.w500,
                                      color:
                                          _getOSColor(device.operatingSystem),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                          // Raspberry Pi Model Badge
                          if (_isRaspberryPiDevice(device) &&
                              _getRaspberryPiModel(device) != null) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFE91E63)
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: const Color(0xFFE91E63)
                                      .withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.memory,
                                    size: 12,
                                    color: const Color(0xFFE91E63),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    _getRaspberryPiModel(device)!,
                                    style: const TextStyle(
                                      fontSize: 11,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFFE91E63),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                          // Health indicator (only when connected)
                          if (device.isConnected && device.hasSystemInfo) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getHealthColor(device.healthStatus)
                                    .withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: _getHealthColor(device.healthStatus)
                                      .withValues(alpha: 0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.health_and_safety,
                                    size: 12,
                                    color: _getHealthColor(device.healthStatus),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    device.healthStatus,
                                    style: TextStyle(
                                      fontSize: 11,
                                      fontWeight: FontWeight.w500,
                                      color:
                                          _getHealthColor(device.healthStatus),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                      // Comprehensive health metrics (only when connected and has data)
                      if (device.isConnected && device.hasSystemInfo) ...[
                        const SizedBox(height: 12),
                        // Primary metrics row
                        Row(
                          children: [
                            Expanded(
                              child: _buildMiniMetric(
                                'CPU Usage',
                                '${device.cpuUsage?.toStringAsFixed(1) ?? '--'}%',
                                device.cpuUsage != null
                                    ? device.cpuUsage! / 100
                                    : 0,
                                _getCpuColor(device.cpuUsage),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: _buildMiniMetric(
                                'Memory',
                                device.memoryInfo != null
                                    ? '${(device.memoryInfo!['used'] / 1024).toStringAsFixed(1)}G / ${(device.memoryInfo!['total'] / 1024).toStringAsFixed(1)}G'
                                    : 'N/A',
                                device.memoryInfo != null
                                    ? device.memoryInfo!['usage_percent'] / 100
                                    : 0,
                                _getMemoryColor(
                                    device.memoryInfo?['usage_percent']),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: _buildMiniMetric(
                                'CPU Temp',
                                '${device.temperature?.toStringAsFixed(1) ?? '--'}°C',
                                device.temperature != null
                                    ? (device.temperature! / 80).clamp(0.0, 1.0)
                                    : 0,
                                _getTemperatureColor(device.temperature),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // Secondary metrics row
                        Row(
                          children: [
                            Expanded(
                              child: _buildMiniMetric(
                                'CPU Freq',
                                device.cpuFrequency != null
                                    ? '${device.cpuFrequency!.toStringAsFixed(0)} MHz'
                                    : 'N/A',
                                device.cpuFrequency != null
                                    ? (device.cpuFrequency! / 2000)
                                        .clamp(0.0, 1.0)
                                    : 0,
                                Colors.blue,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: _buildMiniMetric(
                                'GPU Temp',
                                device.gpuTemperature != null
                                    ? '${device.gpuTemperature!.toStringAsFixed(1)}°C'
                                    : 'N/A',
                                device.gpuTemperature != null
                                    ? (device.gpuTemperature! / 80)
                                        .clamp(0.0, 1.0)
                                    : 0,
                                _getTemperatureColor(device.gpuTemperature),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: _buildMiniMetric(
                                'Power',
                                device.isThrottled == true
                                    ? 'Throttled'
                                    : device.isThrottled == false
                                        ? 'Normal'
                                        : 'N/A',
                                device.isThrottled == true ? 1.0 : 0.0,
                                device.isThrottled == true
                                    ? Colors.red
                                    : device.isThrottled == false
                                        ? Colors.green
                                        : Colors.grey,
                              ),
                            ),
                          ],
                        ),
                        // CPU cores information
                        if (device.cpuCores != null) ...[
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Colors.grey[50],
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(color: Colors.grey[200]!),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'CPU Cores',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey[700],
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Wrap(
                                  spacing: 6,
                                  runSpacing: 4,
                                  children: device.cpuCores!.entries
                                      .map<Widget>((entry) {
                                    final coreUsage =
                                        entry.value?.toDouble() ?? 0.0;
                                    return Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: _getCpuColor(coreUsage)
                                            .withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(4),
                                        border: Border.all(
                                          color: _getCpuColor(coreUsage)
                                              .withValues(alpha: 0.3),
                                        ),
                                      ),
                                      child: Text(
                                        '${entry.key}: ${coreUsage.toStringAsFixed(0)}%',
                                        style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w500,
                                          color: _getCpuColor(coreUsage),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ],
                  ),
                ),
                // Action Menu
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.grey),
                  enabled: device.status != DeviceStatus.rebooting &&
                      device.status != DeviceStatus.shuttingDown,
                  onSelected: (value) {
                    switch (value) {
                      case 'connect':
                        _connectToDevice(device);
                        break;
                      case 'disconnect':
                        _disconnectFromDevice(device);
                        break;
                      case 'edit':
                        _editDevice(device);
                        break;
                      case 'delete':
                        _deleteDevice(device);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: device.isConnected ? 'disconnect' : 'connect',
                      child: Row(
                        children: [
                          Icon(device.isConnected ? Icons.link_off : Icons.link,
                              size: 16),
                          const SizedBox(width: 8),
                          Text(device.isConnected ? 'Disconnect' : 'Connect'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 16),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 16, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            );
          }
        },
      ),
    );
  }

  Widget _buildHorizontalHealthMetrics(device) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Primary metrics row
        Row(
          children: [
            Expanded(
              child: _buildMiniMetric(
                'CPU Usage',
                '${device.cpuUsage?.toStringAsFixed(1) ?? '--'}%',
                device.cpuUsage != null ? device.cpuUsage! / 100 : 0,
                _getCpuColor(device.cpuUsage),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildMiniMetric(
                'Memory',
                device.memoryInfo != null
                    ? '${(device.memoryInfo!['used'] / 1024).toStringAsFixed(1)}G / ${(device.memoryInfo!['total'] / 1024).toStringAsFixed(1)}G'
                    : 'N/A',
                device.memoryInfo != null
                    ? device.memoryInfo!['usage_percent'] / 100
                    : 0,
                _getMemoryColor(device.memoryInfo?['usage_percent']),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildMiniMetric(
                'CPU Temp',
                '${device.temperature?.toStringAsFixed(1) ?? '--'}°C',
                device.temperature != null
                    ? (device.temperature! / 80).clamp(0.0, 1.0)
                    : 0,
                _getTemperatureColor(device.temperature),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        // Secondary metrics row
        Row(
          children: [
            Expanded(
              child: _buildMiniMetric(
                'CPU Freq',
                device.cpuFrequency != null
                    ? '${device.cpuFrequency!.toStringAsFixed(0)} MHz'
                    : 'N/A',
                device.cpuFrequency != null
                    ? (device.cpuFrequency! / 2000).clamp(0.0, 1.0)
                    : 0,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildMiniMetric(
                'GPU Temp',
                device.gpuTemperature != null
                    ? '${device.gpuTemperature!.toStringAsFixed(1)}°C'
                    : 'N/A',
                device.gpuTemperature != null
                    ? (device.gpuTemperature! / 80).clamp(0.0, 1.0)
                    : 0,
                _getTemperatureColor(device.gpuTemperature),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildMiniMetric(
                'Power',
                device.isThrottled == true
                    ? 'Throttled'
                    : device.isThrottled == false
                        ? 'Normal'
                        : 'N/A',
                device.isThrottled == true ? 1.0 : 0.0,
                device.isThrottled == true
                    ? Colors.red
                    : device.isThrottled == false
                        ? Colors.green
                        : Colors.grey,
              ),
            ),
          ],
        ),
        // CPU cores information
        if (device.cpuCores != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'CPU Cores',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 4),
                Wrap(
                  spacing: 6,
                  runSpacing: 4,
                  children: device.cpuCores!.entries.map<Widget>((entry) {
                    final coreUsage = entry.value?.toDouble() ?? 0.0;
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getCpuColor(coreUsage).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: _getCpuColor(coreUsage).withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        '${entry.key}: ${coreUsage.toStringAsFixed(0)}%',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: _getCpuColor(coreUsage),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDeviceGridCard(device) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with icon and menu
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  device.vendor.toLowerCase().contains('raspberry')
                      ? Icons.memory
                      : Icons.computer,
                  color: Colors.grey[600],
                  size: 18,
                ),
              ),
              const Spacer(),
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, color: Colors.grey, size: 18),
                enabled: device.status != DeviceStatus.rebooting &&
                    device.status != DeviceStatus.shuttingDown,
                onSelected: (value) {
                  switch (value) {
                    case 'connect':
                      _connectToDevice(device);
                      break;
                    case 'disconnect':
                      _disconnectFromDevice(device);
                      break;
                    case 'edit':
                      _editDevice(device);
                      break;
                    case 'delete':
                      _deleteDevice(device);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: device.isConnected ? 'disconnect' : 'connect',
                    child: Row(
                      children: [
                        Icon(device.isConnected ? Icons.link_off : Icons.link,
                            size: 16),
                        const SizedBox(width: 8),
                        Text(device.isConnected ? 'Disconnect' : 'Connect'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 6),

          // Device name
          Text(
            device.name,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1F2937),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 3),

          // IP address
          Text(
            device.ip,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context)
                  .colorScheme
                  .onSurface
                  .withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 6),

          // Status badges
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getStatusColor(device).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: _getStatusColor(device).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getStatusIcon(device),
                      size: 10,
                      color: _getStatusColor(device),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getStatusText(device),
                      style: TextStyle(
                        fontSize: 9,
                        fontWeight: FontWeight.w500,
                        color: _getStatusColor(device),
                      ),
                    ),
                  ],
                ),
              ),
              // OS Badge
              if (device.operatingSystem != 'Unknown')
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getOSColor(device.operatingSystem)
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getOSColor(device.operatingSystem)
                          .withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getOSIcon(device.operatingSystem),
                        size: 10,
                        color: _getOSColor(device.operatingSystem),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getOSDisplayText(device.operatingSystem),
                        style: TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.w500,
                          color: _getOSColor(device.operatingSystem),
                        ),
                      ),
                    ],
                  ),
                ),
              // Raspberry Pi Model Badge
              if (_isRaspberryPiDevice(device) &&
                  _getRaspberryPiModel(device) != null)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: const Color(0xFFE91E63).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.memory,
                        size: 10,
                        color: const Color(0xFFE91E63),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getRaspberryPiModel(device)!,
                        style: const TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFFE91E63),
                        ),
                      ),
                    ],
                  ),
                ),
              if (device.isConnected && device.hasSystemInfo)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getHealthColor(device.healthStatus)
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getHealthColor(device.healthStatus)
                          .withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.health_and_safety,
                        size: 10,
                        color: _getHealthColor(device.healthStatus),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        device.healthStatus,
                        style: TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.w500,
                          color: _getHealthColor(device.healthStatus),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),

          // Health metrics (detailed layout matching list view)
          if (device.isConnected && device.hasSystemInfo) ...[
            const SizedBox(height: 6),
            // Primary metrics row
            Row(
              children: [
                Expanded(
                  child: _buildMiniMetric(
                    'CPU Usage',
                    '${device.cpuUsage?.toStringAsFixed(1) ?? '--'}%',
                    device.cpuUsage != null ? device.cpuUsage! / 100 : 0,
                    _getCpuColor(device.cpuUsage),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: _buildMiniMetric(
                    'Memory',
                    device.memoryInfo != null
                        ? '${(device.memoryInfo!['used'] / 1024).toStringAsFixed(1)}G / ${(device.memoryInfo!['total'] / 1024).toStringAsFixed(1)}G'
                        : 'N/A',
                    device.memoryInfo != null
                        ? device.memoryInfo!['usage_percent'] / 100
                        : 0,
                    _getMemoryColor(device.memoryInfo?['usage_percent']),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: _buildMiniMetric(
                    'CPU Temp',
                    '${device.temperature?.toStringAsFixed(1) ?? '--'}°C',
                    device.temperature != null
                        ? (device.temperature! / 80).clamp(0.0, 1.0)
                        : 0,
                    _getTemperatureColor(device.temperature),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            // Secondary metrics row
            Row(
              children: [
                Expanded(
                  child: _buildMiniMetric(
                    'CPU Freq',
                    device.cpuFrequency != null
                        ? '${device.cpuFrequency!.toStringAsFixed(0)} MHz'
                        : 'N/A',
                    device.cpuFrequency != null
                        ? (device.cpuFrequency! / 2000).clamp(0.0, 1.0)
                        : 0,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: _buildMiniMetric(
                    'GPU Temp',
                    device.gpuTemperature != null
                        ? '${device.gpuTemperature!.toStringAsFixed(1)}°C'
                        : 'N/A',
                    device.gpuTemperature != null
                        ? (device.gpuTemperature! / 80).clamp(0.0, 1.0)
                        : 0,
                    _getTemperatureColor(device.gpuTemperature),
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: _buildMiniMetric(
                    'Power',
                    device.isThrottled == true
                        ? 'Throttled'
                        : device.isThrottled == false
                            ? 'Normal'
                            : 'N/A',
                    device.isThrottled == true ? 1.0 : 0.0,
                    device.isThrottled == true
                        ? Colors.red
                        : device.isThrottled == false
                            ? Colors.green
                            : Colors.grey,
                  ),
                ),
              ],
            ),
            // CPU cores information (compact version for grid)
            if (device.cpuCores != null) ...[
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'CPU Cores',
                      style: TextStyle(
                        fontSize: 9,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 3),
                    Wrap(
                      spacing: 3,
                      runSpacing: 2,
                      children: device.cpuCores!.entries.map<Widget>((entry) {
                        final coreUsage = entry.value?.toDouble() ?? 0.0;
                        return Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4, vertical: 1),
                          decoration: BoxDecoration(
                            color:
                                _getCpuColor(coreUsage).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(3),
                            border: Border.all(
                              color: _getCpuColor(coreUsage)
                                  .withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            '${entry.key}: ${coreUsage.toStringAsFixed(0)}%',
                            style: TextStyle(
                              fontSize: 8,
                              fontWeight: FontWeight.w500,
                              color: _getCpuColor(coreUsage),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }

  void _connectToDevice(device) async {
    final appState = context.read<AppState>();

    // Try to load saved credentials
    final savedCredentials = await appState.getSavedSSHCredentials(device.id);

    final usernameController =
        TextEditingController(text: savedCredentials?['username'] ?? '');
    final passwordController =
        TextEditingController(text: savedCredentials?['password'] ?? '');

    bool rememberCredentials = savedCredentials != null;

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('Connect to ${device.name}'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: usernameController,
                decoration: const InputDecoration(
                  labelText: 'Username',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: passwordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Password',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.lock),
                ),
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('Remember credentials'),
                subtitle: const Text(
                    'Save username and password for future connections'),
                value: rememberCredentials,
                onChanged: (value) {
                  setState(() {
                    rememberCredentials = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                navigator.pop();
                try {
                  await appState.connectSSH(
                    device.id,
                    usernameController.text,
                    passwordController.text,
                    rememberCredentials: rememberCredentials,
                  );

                  // Check if connection was actually successful
                  final connection = appState.getSSHConnection(device.id);
                  if (mounted) {
                    if (connection?.isConnected == true) {
                      messenger.showSnackBar(
                        SnackBar(
                          content:
                              Text('Successfully connected to ${device.name}'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    } else {
                      // Connection failed but no exception was thrown
                      final errorMsg =
                          connection?.error ?? 'Unknown connection error';
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text(
                              'Failed to connect to ${device.name}: $errorMsg'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  if (mounted) {
                    messenger.showSnackBar(
                      SnackBar(
                        content:
                            Text('Failed to connect to ${device.name}: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: const Text('Connect'),
            ),
          ],
        ),
      ),
    );
  }

  void _editDevice(device) {
    final nameController = TextEditingController(text: device.name);
    final ipController = TextEditingController(text: device.ip);
    bool connectOnLaunch = device.connectOnLaunch;
    bool isDefaultDevice = device.isDefaultDevice;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Edit Device'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Device Name',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.label),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: ipController,
                decoration: const InputDecoration(
                  labelText: 'IP Address',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.network_check),
                ),
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: const Text('Connect on launch'),
                subtitle: const Text('Automatically connect when app starts'),
                value: connectOnLaunch,
                onChanged: (value) {
                  setState(() {
                    connectOnLaunch = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
              CheckboxListTile(
                title: const Text('Set as default device'),
                subtitle: const Text(
                    'Use this device as the default for terminal and apps'),
                value: isDefaultDevice,
                onChanged: (value) {
                  setState(() {
                    isDefaultDevice = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            FilledButton(
              onPressed: () async {
                Navigator.of(context).pop();

                final appState = context.read<AppState>();
                final updatedDevice = device.copyWith(
                  name: nameController.text,
                  ip: ipController.text,
                  connectOnLaunch: connectOnLaunch,
                  isDefaultDevice: isDefaultDevice,
                );

                await appState.updateDevice(updatedDevice);

                // If setting as default, also call setDefaultDevice to clear other defaults
                if (isDefaultDevice && !device.isDefaultDevice) {
                  await appState.setDefaultDevice(device.id);
                }

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Device updated')),
                  );
                }
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _deleteDevice(device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Device'),
        content: Text('Are you sure you want to delete "${device.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await context.read<AppState>().removeDevice(device.id);
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Deleted ${device.name}')),
                );
              }
            },
            style: FilledButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(device) {
    if (device.isConnected) {
      return const Color(0xFF10B981); // Green for connected
    } else if (device.status == DeviceStatus.online) {
      return const Color(0xFF3B82F6); // Blue for online
    } else if (device.status == DeviceStatus.offline) {
      return const Color(0xFF6B7280); // Gray for offline
    } else if (device.status == DeviceStatus.rebooting ||
        device.status == DeviceStatus.shuttingDown) {
      return const Color(0xFFEA580C); // Orange for rebooting/shutting down
    } else if (device.status == DeviceStatus.error) {
      return const Color(0xFFEF4444); // Red for error
    } else {
      return const Color(0xFFF59E0B); // Yellow for connecting/unknown
    }
  }

  String _getStatusText(device) {
    if (device.isConnected) {
      return 'CONNECTED';
    } else if (device.status == DeviceStatus.online) {
      return 'ONLINE';
    } else if (device.status == DeviceStatus.offline) {
      return 'OFFLINE';
    } else if (device.status == DeviceStatus.rebooting) {
      return 'REBOOTING';
    } else if (device.status == DeviceStatus.shuttingDown) {
      return 'SHUTTING DOWN';
    } else if (device.status == DeviceStatus.error) {
      return 'ERROR';
    } else if (device.status == DeviceStatus.connecting) {
      return 'CONNECTING...';
    } else {
      return 'UNKNOWN';
    }
  }

  IconData _getStatusIcon(device) {
    if (device.isConnected) {
      return Icons.link;
    } else if (device.status == DeviceStatus.online) {
      return Icons.circle;
    } else if (device.status == DeviceStatus.offline) {
      return Icons.circle_outlined;
    } else if (device.status == DeviceStatus.rebooting) {
      return Icons.restart_alt;
    } else if (device.status == DeviceStatus.shuttingDown) {
      return Icons.power_settings_new;
    } else if (device.status == DeviceStatus.error) {
      return Icons.error_outline;
    } else if (device.status == DeviceStatus.connecting) {
      return Icons.sync;
    } else {
      return Icons.help_outline;
    }
  }

  void _disconnectFromDevice(device) async {
    try {
      await context.read<AppState>().disconnectSSH(device.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Disconnected from ${device.name}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to disconnect: $e')),
        );
      }
    }
  }

  Widget _buildMiniMetric(
      String label, String value, double progress, Color color) {
    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 9,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 1),
          Text(
            value,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 3),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 2,
          ),
        ],
      ),
    );
  }

  Color _getHealthColor(String status) {
    switch (status) {
      case 'Good':
        return Colors.green;
      case 'Warning':
        return Colors.orange;
      case 'Critical':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getCpuColor(double? usage) {
    if (usage == null) return Colors.grey;
    if (usage < 50) return Colors.green;
    if (usage < 80) return Colors.orange;
    return Colors.red;
  }

  Color _getMemoryColor(double? usage) {
    if (usage == null) return Colors.grey;
    if (usage < 70) return Colors.green;
    if (usage < 85) return Colors.orange;
    return Colors.red;
  }

  Color _getTemperatureColor(double? temp) {
    if (temp == null) return Colors.grey;
    if (temp < 60) return Colors.green;
    if (temp < 70) return Colors.orange;
    return Colors.red;
  }

  Widget _buildAddDevicesTab() {
    return Container(
      color: const Color(0xFFF8FAFC),
      padding: const EdgeInsets.all(24),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Responsive breakpoints
          final isNarrow = constraints.maxWidth < 800;
          final cardPadding = isNarrow ? 16.0 : 24.0;
          final spacing = isNarrow ? 16.0 : 24.0;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top Row: Manual Add Device and Network Discovery side by side
              if (isNarrow)
                // Stack vertically on narrow screens
                Column(
                  children: [
                    _buildManualAddCard(cardPadding),
                    SizedBox(height: spacing),
                    _buildNetworkDiscoveryCard(cardPadding),
                  ],
                )
              else
                // Side by side on wide screens with equal height
                IntrinsicHeight(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Expanded(
                        flex: 1,
                        child: _buildManualAddCard(cardPadding),
                      ),
                      SizedBox(width: spacing),
                      Expanded(
                        flex: 1,
                        child: _buildNetworkDiscoveryCard(cardPadding),
                      ),
                    ],
                  ),
                ),

              SizedBox(height: spacing),

              // Middle Row: Filter controls (only show if devices found)
              if (_discoveredDevices.isNotEmpty) ...[
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(cardPadding * 0.67),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Left side: Filter icon and text
                        Expanded(
                          child: Row(
                            children: [
                              Icon(
                                Icons.filter_list,
                                color: Theme.of(context).colorScheme.primary,
                                size: 20,
                              ),
                              const SizedBox(width: 12),
                              Text(
                                'Filter Results',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2937),
                                    ),
                              ),
                            ],
                          ),
                        ),
                        // Right side: Show All Devices button - aligned to the right
                        OutlinedButton.icon(
                          onPressed: () {
                            setState(() {
                              _showAllDevices = !_showAllDevices;
                            });
                          },
                          icon: Icon(_showAllDevices
                              ? Icons.filter_list_off
                              : Icons.filter_list),
                          label: Text(
                            _showAllDevices
                                ? 'Show Pi Only'
                                : 'Show All Devices',
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: spacing),
              ],

              // Bottom Row: Scan results - Use Expanded to fill remaining space
              Expanded(
                child: Card(
                  child: Padding(
                    padding: EdgeInsets.all(cardPadding),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.devices,
                              color: Theme.of(context).colorScheme.primary,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Discovered Devices',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: const Color(0xFF1F2937),
                                  ),
                            ),
                            const Spacer(),
                            if (_discoveredDevices.isNotEmpty)
                              Text(
                                '${_discoveredDevices.length} device${_discoveredDevices.length == 1 ? '' : 's'} found',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 24),

                        // Results section - Use Expanded to fill remaining space
                        Expanded(
                          child: _buildDiscoveryResults(),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildManualAddCard(double padding) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  Icons.add_circle_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Add Device Manually',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF1F2937),
                        ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Flexible(
              child: Text(
                'Add a device by manually entering it\'s IP address.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) => const AddDeviceDialog(),
                ),
                icon: const Icon(Icons.add),
                label: const Text('Add Device'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkDiscoveryCard(double padding) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(padding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(
                  Icons.search,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Network Discovery',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF1F2937),
                        ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Flexible(
              child: Text(
                'Scan your local network to automatically discover devices.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 24),

            // Scan button
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: _isScanning ? null : _scanNetwork,
                icon: _isScanning
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.search),
                label: Text(_isScanning ? 'Scanning...' : 'Start Network Scan'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Network scanning functionality
  Future<void> _scanNetwork() async {
    setState(() {
      _isScanning = true;
      _discoveredDevices = [];
    });

    try {
      // Get the application directory and script path
      final directory = await getApplicationDocumentsDirectory();
      final outputFile = File('${directory.path}/discovered_devices.json');

      // Get the script path - try multiple locations
      String? scriptPath;
      final possiblePaths = [
        'lib/scripts/device_discovery_new.py',
        'scripts/device_discovery_new.py',
        '../lib/scripts/device_discovery_new.py',
        '../../lib/scripts/device_discovery_new.py',
      ];

      for (final path in possiblePaths) {
        final file = File(path);
        if (await file.exists()) {
          scriptPath = file.absolute.path;
          break;
        }
      }

      if (scriptPath == null) {
        throw Exception('Could not find device discovery script');
      }

      // Find python3
      final pythonPath = await Process.run('which', ['python3']);
      if (pythonPath.exitCode != 0) {
        throw Exception('Could not find python3: ${pythonPath.stderr}');
      }

      debugPrint('Running network scan with script: $scriptPath');
      debugPrint('Output file: ${outputFile.path}');

      // Run the Python script
      final process = await Process.start(
        pythonPath.stdout.toString().trim(),
        [scriptPath, outputFile.path],
      );

      final stdout = await process.stdout.transform(utf8.decoder).join();
      final stderr = await process.stderr.transform(utf8.decoder).join();

      if (stdout.isNotEmpty) {
        debugPrint('Python script stdout: $stdout');
      }
      if (stderr.isNotEmpty) {
        debugPrint('Python script stderr: $stderr');
      }

      final exitCode = await process.exitCode;
      debugPrint('Python script exit code: $exitCode');

      if (exitCode == 0 && await outputFile.exists()) {
        final contents = await outputFile.readAsString();
        debugPrint('Raw output file contents: $contents');

        try {
          final List<dynamic> devices = jsonDecode(contents);
          final List<Map<String, String>> discoveredDevices = [];

          // Process each device and enhance with vendor lookup
          for (final device in devices) {
            final ip = device['ip'] as String? ?? 'Unknown IP';
            final mac = device['mac'] as String? ?? 'Unknown';
            final hostname = device['hostname'] as String?;
            final os = device['os'] as String?;

            // Use MAC vendor lookup service for better vendor detection
            String vendor = device['vendor'] as String? ?? 'Unknown';
            if (mac != 'Unknown' && mac.isNotEmpty) {
              try {
                final lookupVendor =
                    await MacVendorService.getVendorFromMac(mac);
                if (lookupVendor != 'Unknown') {
                  vendor = lookupVendor;
                }
              } catch (e) {
                debugPrint('Failed to lookup vendor for MAC $mac: $e');
              }
            }

            discoveredDevices.add({
              'ip': ip,
              'mac': mac,
              'vendor': vendor,
              if (hostname != null) 'hostname': hostname,
              if (os != null) 'os': os,
            });
          }

          debugPrint('Parsed ${discoveredDevices.length} devices');
          setState(() {
            _discoveredDevices = discoveredDevices;
            _isScanning = false;
          });
        } catch (e) {
          setState(() {
            _isScanning = false;
          });
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to parse scan results: $e')),
            );
          }
        }
      } else {
        throw Exception(
            'Script failed with exit code $exitCode. stderr: $stderr');
      }
    } catch (e) {
      debugPrint('Network scan error: $e');
      setState(() {
        _isScanning = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error during discovery: $e')),
        );
      }
    }
  }

  Widget _buildDiscoveryResults() {
    if (_isScanning) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Scanning network for devices...'),
          ],
        ),
      );
    }

    if (_discoveredDevices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No devices discovered',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start a network scan to discover devices',
              style: TextStyle(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    // Filter devices based on toggle
    final filteredDevices = _showAllDevices
        ? _discoveredDevices
        : _discoveredDevices.where((device) {
            final vendor = device['vendor']?.toLowerCase() ?? '';
            final mac = device['mac']?.toUpperCase() ?? '';
            return vendor.contains('raspberry') ||
                _rpiMacPrefixes.any((prefix) => mac.startsWith(prefix));
          }).toList();

    if (filteredDevices.isEmpty && !_showAllDevices) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.filter_list_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Raspberry Pi devices found',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try "Show All" to see other devices',
              style: TextStyle(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredDevices.length,
      itemBuilder: (context, index) {
        final device = filteredDevices[index];
        return _buildDiscoveredDeviceCard(device);
      },
    );
  }

  Widget _buildDiscoveredDeviceCard(Map<String, String> device) {
    final appState = context.read<AppState>();
    final isRaspberryPi =
        device['vendor']?.toLowerCase().contains('raspberry') ??
            false ||
                _rpiMacPrefixes.any((prefix) =>
                    device['mac']?.toUpperCase().startsWith(prefix) ?? false);

    // Check if device already exists
    final existingDevice =
        appState.devices.where((d) => d.ip == device['ip']).firstOrNull;
    final isAlreadyAdded = existingDevice != null;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Device Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isRaspberryPi ? Colors.green[50] : Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isRaspberryPi ? Icons.memory : Icons.computer,
                color: isRaspberryPi ? Colors.green[600] : Colors.grey[600],
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            // Device Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    device['hostname'] ?? device['ip'] ?? 'Unknown IP',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF1F2937),
                    ),
                  ),
                  if (device['hostname'] != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      device['ip'] ?? 'Unknown IP',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                  const SizedBox(height: 4),
                  Text(
                    'MAC: ${device['mac'] ?? 'Unknown'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    'Vendor: ${device['vendor'] ?? 'Unknown'}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  // OS Badge
                  if (device['os'] != null && device['os'] != 'Unknown')
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color:
                            _getOSColor(device['os']!).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color:
                              _getOSColor(device['os']!).withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getOSIcon(device['os']!),
                            size: 12,
                            color: _getOSColor(device['os']!),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getOSDisplayText(device['os']!),
                            style: TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                              color: _getOSColor(device['os']!),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            // Add Button
            FilledButton.icon(
              onPressed:
                  isAlreadyAdded ? null : () => _addDiscoveredDevice(device),
              icon: Icon(isAlreadyAdded ? Icons.check : Icons.add),
              label: Text(isAlreadyAdded ? 'Added' : 'Add'),
              style: FilledButton.styleFrom(
                backgroundColor: isAlreadyAdded ? Colors.grey : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addDiscoveredDevice(Map<String, String> device) async {
    final appState = context.read<AppState>();
    final name = await _showNameDeviceDialog(device['ip'] ?? 'Unknown IP');
    if (name != null) {
      final newDevice = Device(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        ip: device['ip']!,
        mac: device['mac'] ?? 'Unknown',
        vendor: device['vendor'] ?? 'Unknown',
        name: name,
        operatingSystem: device['os'] ?? 'Unknown',
      );
      await appState.addDevice(newDevice);

      if (mounted) {
        // Trigger a rebuild to update the button state
        setState(() {});

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Added $name')),
        );
      }
    }
  }

  Future<String?> _showNameDeviceDialog(String ip) async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Name Device'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Enter a name for device at $ip:22'),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                decoration: const InputDecoration(
                  labelText: 'Device Name',
                  hintText: 'e.g., Raspberry Pi 4',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              final name = controller.text.trim();
              if (name.isNotEmpty) {
                Navigator.of(context).pop(name);
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  // OS-related helper methods
  Color _getOSColor(String operatingSystem) {
    final os = operatingSystem.toLowerCase();
    if (os.contains('raspberry pi')) {
      return Colors.green.shade600; // Raspberry Pi specific color
    } else if (os.contains('linux') ||
        os.contains('ubuntu') ||
        os.contains('debian') ||
        os.contains('raspbian')) {
      return Colors.orange;
    } else if (os.contains('windows')) {
      return Colors.blue;
    } else if (os.contains('macos') || os.contains('darwin')) {
      return Colors.grey.shade600;
    } else if (os.contains('android')) {
      return Colors.lightGreen;
    } else if (os.contains('detecting')) {
      return Colors.purple;
    } else {
      return Colors.grey;
    }
  }

  IconData _getOSIcon(String operatingSystem) {
    final os = operatingSystem.toLowerCase();
    if (os.contains('raspberry pi')) {
      return Icons.memory; // Raspberry Pi specific icon
    } else if (os.contains('linux') ||
        os.contains('ubuntu') ||
        os.contains('debian') ||
        os.contains('raspbian')) {
      return Icons.terminal;
    } else if (os.contains('windows')) {
      return Icons.desktop_windows;
    } else if (os.contains('macos') || os.contains('darwin')) {
      return Icons.laptop_mac;
    } else if (os.contains('android')) {
      return Icons.android;
    } else if (os.contains('detecting')) {
      return Icons.search;
    } else {
      return Icons.computer;
    }
  }

  String _getOSDisplayText(String operatingSystem) {
    if (operatingSystem.toLowerCase().contains('detecting')) {
      return 'Detecting...';
    }

    // For comprehensive OS names, try to shorten intelligently
    final os = operatingSystem.toLowerCase();

    // Raspberry Pi specific
    if (os.contains('raspberry pi os')) {
      return 'Pi OS';
    } else if (os.contains('raspberry pi ubuntu')) {
      return 'Pi Ubuntu';
    } else if (os.contains('raspberry pi debian')) {
      return 'Pi Debian';
    } else if (os.contains('raspberry pi android')) {
      return 'Pi Android';
    } else if (os.contains('raspberry pi')) {
      // Extract just the distro name after "Raspberry Pi"
      final parts = operatingSystem.split(' ');
      if (parts.length > 2) {
        return 'Pi ${parts[2]}';
      }
      return 'Raspberry Pi';
    }

    // Regular distributions - keep version if short enough
    if (operatingSystem.length <= 15) {
      return operatingSystem;
    }

    // For longer names, try to extract the main distro name
    if (os.contains('ubuntu')) {
      final match = RegExp(r'ubuntu\s*(\d+\.?\d*)?', caseSensitive: false)
          .firstMatch(operatingSystem);
      return match?.group(0) ?? 'Ubuntu';
    } else if (os.contains('debian')) {
      final match = RegExp(r'debian\s*(\d+\.?\d*)?', caseSensitive: false)
          .firstMatch(operatingSystem);
      return match?.group(0) ?? 'Debian';
    } else if (os.contains('fedora')) {
      final match = RegExp(r'fedora\s*(\d+\.?\d*)?', caseSensitive: false)
          .firstMatch(operatingSystem);
      return match?.group(0) ?? 'Fedora';
    } else if (os.contains('centos')) {
      return 'CentOS';
    } else if (os.contains('red hat')) {
      return 'RHEL';
    } else if (os.contains('opensuse')) {
      return 'openSUSE';
    } else if (os.contains('arch linux')) {
      return 'Arch';
    } else if (os.contains('manjaro')) {
      return 'Manjaro';
    } else if (os.contains('linux mint')) {
      return 'Mint';
    } else if (os.contains('elementary')) {
      return 'elementary';
    } else if (os.contains('pop!_os')) {
      return 'Pop!_OS';
    } else if (os.contains('kali')) {
      return 'Kali';
    } else if (os.contains('alpine')) {
      return 'Alpine';
    } else if (os.contains('gentoo')) {
      return 'Gentoo';
    } else if (os.contains('nixos')) {
      return 'NixOS';
    } else if (os.contains('based')) {
      // For "X-based" distributions, just show the base
      return operatingSystem.replaceAll('-based', '');
    } else if (os.contains('linux')) {
      return 'Linux';
    } else if (os.contains('windows')) {
      return 'Windows';
    } else if (os.contains('macos') || os.contains('darwin')) {
      return 'macOS';
    } else if (os.contains('android')) {
      return 'Android';
    } else {
      // Truncate long OS names
      return operatingSystem.length > 12
          ? '${operatingSystem.substring(0, 12)}...'
          : operatingSystem;
    }
  }

  /// Check if a device is a Raspberry Pi
  bool _isRaspberryPiDevice(Device device) {
    final os = device.operatingSystem.toLowerCase();
    final vendor = device.vendor.toLowerCase();
    return os.contains('raspberry pi') ||
        os.contains('raspbian') ||
        os.contains('pi os') ||
        vendor.contains('raspberry');
  }

  /// Extract Raspberry Pi model from system info or OS string
  String? _getRaspberryPiModel(Device device) {
    if (!_isRaspberryPiDevice(device)) return null;

    // Try to get model from system info first using the utility method
    if (device.systemInfo.isNotEmpty) {
      // Import the utility class method
      final badgeModel = _extractPiModelForBadge(device.systemInfo);
      if (badgeModel != null) {
        return badgeModel;
      }
    }

    // Fallback to OS string parsing
    final os = device.operatingSystem.toLowerCase();
    if (os.contains('raspberry pi')) {
      final match =
          RegExp(r'raspberry pi (\w+(?:\s+\w+)?)', caseSensitive: false)
              .firstMatch(os);
      if (match != null) {
        final modelPart = match.group(1)!;
        // Convert to badge format
        if (modelPart.startsWith('4')) return 'RPi 4';
        if (modelPart.startsWith('5')) return 'RPi 5';
        if (modelPart.startsWith('3')) return 'RPi 3';
        if (modelPart.startsWith('2')) return 'RPi 2';
        if (modelPart.startsWith('1')) return 'RPi 1';
        if (modelPart.toLowerCase().contains('zero')) return 'RPi Zero';
        return 'RPi $modelPart';
      }
    }

    // Generic fallback
    return 'RPi';
  }

  /// Extract short Pi model for badge display (e.g., "RPi 4")
  String? _extractPiModelForBadge(Map<String, dynamic> systemInfo) {
    // First try to get the exact model from OS info
    if (systemInfo['os'] != null) {
      final osInfo = systemInfo['os'] as Map<String, dynamic>;
      final piModel = osInfo['pi_model'] as String?;
      if (piModel != null && piModel.isNotEmpty) {
        // Extract model number from full model string
        final match =
            RegExp(r'Raspberry Pi (\w+(?:\s+\w+)?)', caseSensitive: false)
                .firstMatch(piModel);
        if (match != null) {
          final modelPart = match.group(1)!;
          // Convert "4 Model B" to "4", "Zero W" to "Zero", etc.
          if (modelPart.startsWith('4')) return 'RPi 4';
          if (modelPart.startsWith('5')) return 'RPi 5';
          if (modelPart.startsWith('3')) return 'RPi 3';
          if (modelPart.startsWith('2')) return 'RPi 2';
          if (modelPart.startsWith('1')) return 'RPi 1';
          if (modelPart.toLowerCase().contains('zero')) return 'RPi Zero';
          return 'RPi $modelPart';
        }
      }
    }

    // Fallback to architecture-based detection
    final uname = systemInfo['uname'] ?? '';
    if (uname.contains('aarch64')) return 'RPi 4/5';
    if (uname.contains('armv7l')) return 'RPi 3/4';
    if (uname.contains('armv6l')) return 'RPi Zero/1';

    return 'RPi';
  }
}
