import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:xterm/xterm.dart';
import '../services/app_state.dart';

class SSHScreen extends StatefulWidget {
  const SSHScreen({super.key});

  @override
  State<SSHScreen> createState() => _SSHScreenState();
}

class _SSHScreenState extends State<SSHScreen> {
  final _scrollController = ScrollController();
  String? _lastConnectedDeviceId;
  Timer? _terminalActivityTimer;

  @override
  void initState() {
    super.initState();

    // Auto-select best device (prioritizing default) when terminal page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AppState>().autoSelectBestDevice();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _terminalActivityTimer?.cancel();
    super.dispose();
  }

  void _handleTerminalOutput(String data) {
    // Mark terminal as active when user types
    final appState = context.read<AppState>();
    appState.setTerminalActive(true);

    // Reset the timer to mark terminal as inactive after a delay
    _terminalActivityTimer?.cancel();
    _terminalActivityTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        appState.setTerminalActive(false);
      }
    });

    // This will be handled by the connectTerminalToSSH method
    // No need to manually execute commands here
  }

  void _handleTerminalResize(
      int width, int height, int pixelWidth, int pixelHeight) {
    final appState = context.read<AppState>();
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice != null) {
      appState.resizeSSHTerminal(selectedDevice.id, width, height);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final selectedDevice = appState.selectedDevice;
        final sshConnection = appState.currentSSHConnection;

        return Scaffold(
          appBar: AppBar(
            title: const Text('SSH Terminal'),
            actions: [
              if (selectedDevice != null) ...[
                // Connection status
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color: _getConnectionStatusColor(
                        selectedDevice, sshConnection),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _getConnectionStatusIcon(selectedDevice, sshConnection),
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _getConnectionStatusText(selectedDevice, sshConnection),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),

                // Connect/Disconnect button
                if (sshConnection?.isConnected == true)
                  IconButton(
                    onPressed: () => _disconnect(appState),
                    icon: const Icon(Icons.link_off),
                    tooltip: 'Disconnect',
                  )
                else
                  IconButton(
                    onPressed: () => _goToDevicesTab(context),
                    icon: const Icon(Icons.link),
                    tooltip: 'Go to Devices to Connect',
                  ),
              ],
            ],
          ),
          body: selectedDevice == null
              ? _buildNoDeviceSelected()
              : Column(
                  children: [
                    // Device Info Bar with Device Selector
                    Container(
                      padding: const EdgeInsets.all(16),
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: Row(
                        children: [
                          Icon(
                            Icons.computer,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Device name and info (no dropdown)
                                Text(
                                  selectedDevice.name,
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        '${selectedDevice.ip} • ${selectedDevice.vendor}',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall,
                                      ),
                                    ),
                                    Text(
                                      'Change device in Devices tab',
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurfaceVariant,
                                            fontStyle: FontStyle.italic,
                                          ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Terminal
                    Expanded(
                      child: sshConnection?.isConnected == true
                          ? _buildTerminal(appState)
                          : _buildNotConnected(),
                    ),
                  ],
                ),
        );
      },
    );
  }

  Widget _buildNoDeviceSelected() {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        final connectedDevices = appState.connectedDevices;

        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.terminal,
                size: 64,
                color: Theme.of(context).colorScheme.outline,
              ),
              const SizedBox(height: 16),
              Text(
                'No Device Selected',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              if (connectedDevices.isNotEmpty) ...[
                Text(
                  'Go to the Devices tab to select an active device for the terminal',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                  textAlign: TextAlign.center,
                ),
              ] else
                Text(
                  'Connect to a device from the Devices tab to start an SSH session',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                  textAlign: TextAlign.center,
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNotConnected() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.link_off,
            size: 64,
            color: Colors.grey.shade600,
          ),
          const SizedBox(height: 16),
          Text(
            'Not Connected',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Connect to the device to start an SSH session',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade400,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTerminal(AppState appState) {
    final selectedDevice = appState.selectedDevice;
    final sshConnection = appState.currentSSHConnection;

    if (selectedDevice == null) {
      return _buildNoDeviceSelected();
    }

    // Get or create terminal for this device
    final terminal = appState.getOrCreateTerminalForDevice(selectedDevice.id);

    if (sshConnection?.isConnected == true) {
      // Check if we need to switch to a different device
      if (_lastConnectedDeviceId != selectedDevice.id) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Connect to the new device terminal (initial prompt handled automatically)
          appState.connectTerminalToSSH(selectedDevice.id, terminal);
          _lastConnectedDeviceId = selectedDevice.id;
        });
      } else if (sshConnection?.terminal != terminal) {
        // Connect the terminal to the SSH session if not already connected
        WidgetsBinding.instance.addPostFrameCallback((_) {
          appState.connectTerminalToSSH(selectedDevice.id, terminal);
          _lastConnectedDeviceId = selectedDevice.id;
        });
      }
    }

    // Set up terminal callbacks
    terminal.onOutput = _handleTerminalOutput;
    terminal.onResize = _handleTerminalResize;

    // Create a controller for this terminal
    final controller = TerminalController();

    return Container(
      color: appState.terminalBackground, // Use terminal background color
      child: Padding(
        padding: const EdgeInsets.all(8.0), // Consistent padding
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Calculate terminal size based on available space and font metrics
            final fontSize = appState.terminalFontSize;

            // More accurate character width calculation based on font family
            double charWidth;
            double lineHeight;

            switch (appState.terminalFontFamily.toLowerCase()) {
              case 'menlo':
              case 'monaco':
              case 'consolas':
                charWidth =
                    fontSize * 0.6; // Monospace fonts are typically 0.6 ratio
                lineHeight = fontSize * 1.3; // Better line spacing
                break;
              case 'courier':
              case 'courier new':
                charWidth = fontSize * 0.65; // Courier is slightly wider
                lineHeight = fontSize * 1.25;
                break;
              default:
                charWidth = fontSize * 0.6; // Default monospace ratio
                lineHeight = fontSize * 1.3;
            }

            final cols = (constraints.maxWidth / charWidth).floor();
            final rows = (constraints.maxHeight / lineHeight).floor();

            // Ensure minimum and maximum reasonable sizes
            final terminalCols = cols.clamp(20, 300);
            final terminalRows = rows.clamp(10, 100);

            // Update terminal size if it has changed
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (terminal.viewWidth != terminalCols ||
                  terminal.viewHeight != terminalRows) {
                terminal.resize(terminalCols, terminalRows);
                _handleTerminalResize(
                    terminalCols,
                    terminalRows,
                    constraints.maxWidth.toInt(),
                    constraints.maxHeight.toInt());
              }
            });

            return GestureDetector(
              onTap: () {
                // Mark terminal as active when clicked
                appState.setTerminalActive(true);

                // Reset the timer to mark terminal as inactive after a delay
                _terminalActivityTimer?.cancel();
                _terminalActivityTimer = Timer(const Duration(seconds: 3), () {
                  if (mounted) {
                    appState.setTerminalActive(false);
                  }
                });
              },
              onSecondaryTapDown: (details) => _showContextMenu(
                  context, details.globalPosition, terminal, controller),
              child: TerminalView(
                terminal,
                controller: controller,
                theme: TerminalTheme(
                  // Use customizable colors from app state
                  cursor: appState.terminalCursor,
                  selection: appState.terminalSelection,
                  foreground: appState.terminalForeground,
                  background: appState.terminalBackground,

                  // Use all customizable colors from app state
                  black: appState.terminalBlack,
                  red: appState.terminalRed,
                  green: appState.terminalGreen,
                  yellow: appState.terminalYellow,
                  blue: appState.terminalBlue,
                  magenta: appState.terminalMagenta,
                  cyan: appState.terminalCyan,
                  white: appState.terminalWhite,

                  // Bright colors - all customizable
                  brightBlack: appState.terminalBrightBlack,
                  brightRed: appState.terminalBrightRed,
                  brightGreen: appState.terminalBrightGreen,
                  brightYellow: appState.terminalBrightYellow,
                  brightBlue: appState.terminalBrightBlue,
                  brightMagenta: appState.terminalBrightMagenta,
                  brightCyan: appState.terminalBrightCyan,
                  brightWhite: appState.terminalBrightWhite,

                  // Search highlighting
                  searchHitBackground:
                      appState.terminalSelection, // Use selection color
                  searchHitBackgroundCurrent:
                      appState.terminalCursor, // Use cursor color
                  searchHitForeground:
                      appState.terminalBackground, // Use background color
                ),
                autofocus: true,
                backgroundOpacity: 1.0,
                // Use customizable font settings from app state
                textStyle: TerminalStyle(
                  fontSize: appState.terminalFontSize,
                  fontFamily: appState.terminalFontFamily,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Color _getConnectionStatusColor(Device device, SSHConnection? connection) {
    if (connection?.isConnected == true) {
      return Colors.green;
    } else if (device.status == DeviceStatus.online) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  IconData _getConnectionStatusIcon(Device device, SSHConnection? connection) {
    if (connection?.isConnected == true) {
      return Icons.link;
    } else if (device.status == DeviceStatus.online) {
      return Icons.link_off;
    } else {
      return Icons.wifi_off;
    }
  }

  String _getConnectionStatusText(Device device, SSHConnection? connection) {
    if (connection?.isConnected == true) {
      return 'Connected';
    } else if (device.status == DeviceStatus.online) {
      return 'Ready to Connect';
    } else {
      return 'Device Offline';
    }
  }

  void _goToDevicesTab(BuildContext context) {
    // Show a helpful message to guide the user to the Devices tab
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            const Text('Go to "Manage Devices" tab to connect to a device'),
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Got it',
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  void _disconnect(AppState appState) async {
    final selectedDevice = appState.selectedDevice;
    if (selectedDevice == null) return;

    try {
      await appState.disconnectSSH(selectedDevice.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Disconnected from ${selectedDevice.name}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to disconnect: $e')),
        );
      }
    }
  }

  void _showContextMenu(BuildContext context, Offset position,
      Terminal terminal, TerminalController controller) {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy,
        position.dx + 1,
        position.dy + 1,
      ),
      items: [
        PopupMenuItem(
          value: 'copy',
          child: const Row(
            children: [
              Icon(Icons.copy, size: 16),
              SizedBox(width: 8),
              Text('Copy'),
            ],
          ),
          onTap: () => _copySelection(terminal, controller),
        ),
        PopupMenuItem(
          value: 'paste',
          child: const Row(
            children: [
              Icon(Icons.paste, size: 16),
              SizedBox(width: 8),
              Text('Paste'),
            ],
          ),
          onTap: () => _pasteFromClipboard(terminal),
        ),
        PopupMenuItem(
          value: 'select_all',
          child: const Row(
            children: [
              Icon(Icons.select_all, size: 16),
              SizedBox(width: 8),
              Text('Select All'),
            ],
          ),
          onTap: () => _selectAll(terminal, controller),
        ),
      ],
    );
  }

  void _copySelection(Terminal terminal, TerminalController controller) {
    try {
      final selection = controller.selection;
      if (selection != null) {
        final text = terminal.buffer.getText(selection);
        Clipboard.setData(ClipboardData(text: text));
        // No notification - silent operation
      }
      // No notification for no selection either
    } catch (e) {
      // Silent failure - no notification
    }
  }

  void _pasteFromClipboard(Terminal terminal) async {
    try {
      final clipboardData = await Clipboard.getData('text/plain');
      if (clipboardData?.text != null && mounted) {
        // Use the terminal's paste method for proper handling
        terminal.paste(clipboardData!.text!);
        // No notification - silent operation
      }
    } catch (e) {
      // Silent failure - no notification
    }
  }

  void _selectAll(Terminal terminal, TerminalController controller) {
    try {
      // Create anchors for the entire buffer
      final buffer = terminal.buffer;
      final startAnchor = buffer.createAnchor(0, 0);
      final endAnchor =
          buffer.createAnchor(buffer.viewWidth - 1, buffer.height - 1);

      // Set selection from start to end
      controller.setSelection(startAnchor, endAnchor);
      // No notification - silent operation
    } catch (e) {
      // Silent failure - no notification
    }
  }
}
