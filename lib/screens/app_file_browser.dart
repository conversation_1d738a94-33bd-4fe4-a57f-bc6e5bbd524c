import 'package:flutter/material.dart';
import '../models/app_module.dart';
import '../services/app_editor_service.dart';
import 'app_file_editor.dart';

class AppFileBrowser extends StatefulWidget {
  final AppModule module;

  const AppFileBrowser({super.key, required this.module});

  @override
  State<AppFileBrowser> createState() => _AppFileBrowserState();
}

class _AppFileBrowserState extends State<AppFileBrowser> {
  final _editorService = AppEditorService();
  List<AppFileInfo> _files = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFiles();
  }

  Future<void> _loadFiles() async {
    try {
      final files = await _editorService.getAppFiles(widget.module);
      setState(() {
        _files = files;
        _isLoading = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading files: $e')),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.module.title} Files'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          IconButton(
            onPressed: _loadFiles,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  child: Row(
                    children: [
                      Icon(
                        Icons.folder_open,
                        size: 20,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.module.appFolderPath ?? 'App Files',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${_files.length} files',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // File list
                Expanded(
                  child: _files.isEmpty
                      ? const Center(
                          child: Text('No files found'),
                        )
                      : ListView.builder(
                          itemCount: _files.length,
                          itemBuilder: (context, index) {
                            final file = _files[index];
                            return _buildFileItem(file);
                          },
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildFileItem(AppFileInfo file) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Icon(
          _getFileIcon(file.type),
          color: _getFileColor(file.type),
        ),
        title: Text(
          file.name,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getFileTypeLabel(file.type),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 2),
            Row(
              children: [
                Text(
                  _formatFileSize(file.size),
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[500],
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _formatDate(file.lastModified),
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: () => _editFile(file),
              icon: const Icon(Icons.edit, size: 20),
              tooltip: 'Edit',
            ),
            IconButton(
              onPressed: () => _showFileInfo(file),
              icon: const Icon(Icons.info_outline, size: 20),
              tooltip: 'Info',
            ),
          ],
        ),
        onTap: () => _editFile(file),
      ),
    );
  }

  IconData _getFileIcon(AppFileType type) {
    switch (type) {
      case AppFileType.source:
        return Icons.code;
      case AppFileType.configuration:
        return Icons.settings;
      case AppFileType.documentation:
        return Icons.description;
      case AppFileType.image:
        return Icons.image;
      case AppFileType.asset:
        return Icons.file_present;
      case AppFileType.other:
        return Icons.insert_drive_file;
    }
  }

  Color _getFileColor(AppFileType type) {
    switch (type) {
      case AppFileType.source:
        return Colors.blue;
      case AppFileType.configuration:
        return Colors.orange;
      case AppFileType.documentation:
        return Colors.green;
      case AppFileType.image:
        return Colors.purple;
      case AppFileType.asset:
        return Colors.teal;
      case AppFileType.other:
        return Colors.grey;
    }
  }

  String _getFileTypeLabel(AppFileType type) {
    switch (type) {
      case AppFileType.source:
        return 'Source Code';
      case AppFileType.configuration:
        return 'Configuration';
      case AppFileType.documentation:
        return 'Documentation';
      case AppFileType.image:
        return 'Image';
      case AppFileType.asset:
        return 'Asset';
      case AppFileType.other:
        return 'File';
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _editFile(AppFileInfo file) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AppFileEditor(
          module: widget.module,
          fileName: file.name,
          fileType: file.type,
        ),
      ),
    );
  }

  void _showFileInfo(AppFileInfo file) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(file.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Type', _getFileTypeLabel(file.type)),
            _buildInfoRow('Size', _formatFileSize(file.size)),
            _buildInfoRow('Modified', file.lastModified.toString()),
            _buildInfoRow('Path', '${widget.module.appFolderPath}/${file.name}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.of(context).pop();
              _editFile(file);
            },
            child: const Text('Edit'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
