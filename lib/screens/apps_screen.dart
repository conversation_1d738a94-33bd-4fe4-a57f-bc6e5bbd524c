import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_reorderable_grid_view/widgets/widgets.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import '../services/app_state.dart';
import '../services/app_module_manager.dart';
import '../models/app_module.dart';
import 'system_monitor_screen.dart';
import 'file_manager_screen.dart';
import '../widgets/add_app_dialog.dart';
import '../widgets/create_app_dialog.dart';
import '../widgets/edit_app_dialog.dart';
import '../widgets/template_app_screen.dart';
import 'rpi_management_app.dart';
import 'rpi_diagnostics_app.dart';

class AppsScreen extends StatefulWidget {
  const AppsScreen({super.key});

  @override
  State<AppsScreen> createState() => _AppsScreenState();
}

class _AppsScreenState extends State<AppsScreen> {
  final _scrollController = ScrollController();
  final _gridViewKey = GlobalKey();
  String? _lastSelectedDeviceId;

  @override
  void initState() {
    super.initState();
    // Initialize the singleton module manager if not already done
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AppModuleManager().initialize();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        // Check if the selected device has changed
        final currentSelectedDeviceId = appState.selectedDevice?.id;
        if (_lastSelectedDeviceId != currentSelectedDeviceId) {
          _lastSelectedDeviceId = currentSelectedDeviceId;
          // Trigger a rebuild after the current build cycle to refresh app filtering
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {});
            }
          });
        }

        return Container(
          color: const Color(0xFFF8FAFC),
          child: Column(
            children: [
              // Header with controls
              Container(
                height: 64,
                color: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Row(
                  children: [
                    const Icon(Icons.apps, size: 24),
                    const SizedBox(width: 12),
                    const Text(
                      'Apps',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    const Spacer(),
                  ],
                ),
              ),
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Available Apps',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF1F2937),
                            ),
                          ),
                          const Spacer(),
                          // Add App button
                          IconButton(
                            onPressed: () => _showAddAppDialog(context),
                            icon: const Icon(Icons.add),
                            tooltip: 'Add App',
                          ),
                          // Create App button (only in developer mode)
                          if (appState.developerModeEnabled)
                            IconButton(
                              onPressed: () => _showCreateAppDialog(context),
                              icon: const Icon(Icons.create),
                              tooltip: 'Create App',
                            ),
                          // Clear User Apps button (only in developer mode)
                          if (appState.developerModeEnabled)
                            IconButton(
                              onPressed: _clearUserApps,
                              icon: const Icon(Icons.clear_all),
                              tooltip: 'Clear User Apps',
                            ),
                          // Refresh button
                          IconButton(
                            onPressed: _refreshApps,
                            icon: const Icon(Icons.refresh),
                            tooltip: 'Refresh Apps',
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.drag_indicator,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Long press and drag to reorder apps',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // Apps grid
                      Expanded(
                        child: ChangeNotifierProvider.value(
                          value: AppModuleManager(),
                          child: Consumer<AppModuleManager>(
                            builder: (context, moduleManager, child) {
                              if (moduleManager.isLoading) {
                                return const Center(
                                  child: CircularProgressIndicator(),
                                );
                              }

                              if (moduleManager.error != null) {
                                return Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.error_outline,
                                        size: 64,
                                        color: Colors.red[300],
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'Error loading apps',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleLarge,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        moduleManager.error!,
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium,
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 16),
                                      ElevatedButton(
                                        onPressed: () =>
                                            moduleManager.refresh(),
                                        child: const Text('Retry'),
                                      ),
                                    ],
                                  ),
                                );
                              }

                              final modules = moduleManager.orderedModules;
                              if (modules.isEmpty) {
                                return const Center(
                                  child: Text('No apps available'),
                                );
                              }

                              return _buildAppsGrid(modules, appState);
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAppsGrid(List<AppModule> modules, AppState appState) {
    // Filter modules based on Raspberry Pi only setting
    final filteredModules = modules.where((module) {
      // If the app is not Raspberry Pi only, always show it
      if (!module.raspberryPiOnly) {
        return true;
      }

      // If the app is Raspberry Pi only, check if we're connected to a Raspberry Pi
      final selectedDevice = appState.selectedDevice;

      if (selectedDevice == null) {
        return false; // No device selected, don't show RPi-only apps
      }

      // Device must be connected to show RPi-only apps
      if (!selectedDevice.isConnected) {
        return false;
      }

      // Additional check: ensure we have an active SSH connection
      final hasActiveConnection = appState.connectedDevices.any(
          (device) => device.id == selectedDevice.id && device.isConnected);

      if (!hasActiveConnection) {
        return false;
      }

      // Check if the selected device is a Raspberry Pi
      return _isRaspberryPiDevice(selectedDevice);
    }).toList();

    final generatedChildren = filteredModules.map((module) {
      return _buildDraggableModuleCard(module, appState);
    }).toList();

    return ReorderableBuilder(
      scrollController: _scrollController,
      onReorder: (ReorderedListFunction reorderedListFunction) {
        final reorderedModules =
            reorderedListFunction(modules) as List<AppModule>;
        // Update the module order in the manager
        AppModuleManager().updateModuleOrder(reorderedModules);
      },
      dragChildBoxDecoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      builder: (children) {
        return GridView(
          key: _gridViewKey,
          controller: _scrollController,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: appState.appsGridColumns,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.1,
          ),
          children: children,
        );
      },
      children: generatedChildren,
    );
  }

  Widget _buildDraggableModuleCard(AppModule module, AppState appState) {
    return Container(
      key: ValueKey(module.id),
      child: _buildModuleCard(module, appState),
    );
  }

  Widget _buildModuleCard(AppModule module, AppState appState) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive sizes based on card width and column count
        final cardWidth = constraints.maxWidth;
        final columnCount = appState.appsGridColumns;

        // More aggressive scaling for higher column counts
        final scaleFactor = columnCount <= 4 ? 1.0 : (4.0 / columnCount);

        final iconSize = (cardWidth * 0.25 * scaleFactor).clamp(24.0, 56.0);
        final titleFontSize =
            (cardWidth * 0.08 * scaleFactor).clamp(10.0, 16.0);
        final descriptionFontSize =
            (cardWidth * 0.06 * scaleFactor).clamp(8.0, 12.0);
        final versionFontSize =
            (cardWidth * 0.05 * scaleFactor).clamp(7.0, 10.0);
        final padding = (cardWidth * 0.08 * scaleFactor).clamp(6.0, 16.0);

        return Card(
          key: ValueKey(module.id),
          elevation: 2,
          child: GestureDetector(
            onTap: () => _handleModuleTap(module),
            onSecondaryTap: appState.developerModeEnabled
                ? () => _showEditAppDialog(context, module)
                : null,
            child: InkWell(
              onTap: () => _handleModuleTap(module),
              borderRadius: BorderRadius.circular(8),
              child: Stack(
                children: [
                  Padding(
                    padding: EdgeInsets.all(padding),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Icon - centered
                        Center(
                          child: _buildAppIcon(module, iconSize),
                        ),
                        SizedBox(height: padding * 0.5),
                        // Title - centered
                        Flexible(
                          child: Center(
                            child: Text(
                              module.title,
                              style: TextStyle(
                                fontSize: titleFontSize,
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2937),
                              ),
                              textAlign: TextAlign.center,
                              maxLines: columnCount >= 6 ? 1 : 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        SizedBox(height: padding * 0.25),
                        // Description - centered
                        Flexible(
                          child: Center(
                            child: Text(
                              module.description,
                              style: TextStyle(
                                fontSize: descriptionFontSize,
                                color: Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                              maxLines: columnCount >= 5
                                  ? 1
                                  : (columnCount >= 4 ? 1 : 2),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        // Version - centered
                        if (module.version != '1.0.0') ...[
                          SizedBox(height: padding * 0.25),
                          Center(
                            child: Text(
                              'v${module.version}',
                              style: TextStyle(
                                fontSize: versionFontSize,
                                color: Colors.grey[500],
                                fontStyle: FontStyle.italic,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  // Raspberry Pi badge
                  if (module.raspberryPiOnly)
                    Positioned(
                      top: 4,
                      left: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 3),
                        decoration: BoxDecoration(
                          color: const Color(0xFFC51A4A), // Raspberry Pi red
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _buildRaspberryPiLogo(12),
                            const SizedBox(width: 3),
                            const Text(
                              'Pi',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 9,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  // User app badge
                  if (_isUserImportedApp(module))
                    Positioned(
                      top: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'USER',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build app icon widget (supports both material icons and custom images)
  Widget _buildAppIcon(AppModule module, double size) {
    if (module.hasCustomIcon) {
      // Show custom image
      final iconFile = File(path.join(module.appFolderPath!, module.iconPath!));
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(size * 0.15),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(size * 0.15),
          child: Image.file(
            iconFile,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // Fallback to material icon if custom image fails
              return Icon(
                module.icon,
                size: size * 0.7,
                color: const Color(0xFF6366F1),
              );
            },
          ),
        ),
      );
    } else {
      // Show material icon
      return Icon(
        module.icon,
        size: size,
        color: const Color(0xFF6366F1),
      );
    }
  }

  /// Check if this is a user-imported app (not built-in)
  bool _isUserImportedApp(AppModule module) {
    return module.appFolderPath?.contains('imported_apps') == true;
  }

  void _handleModuleTap(AppModule module) {
    // Use the dynamic app loader for new modular apps
    if (module.appFolderPath != null) {
      _launchModularApp(module);
    } else {
      // Fallback to legacy handling
      _launchLegacyApp(module);
    }
  }

  void _launchModularApp(AppModule module) {
    switch (module.id) {
      case 'system_monitor':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const SystemMonitorScreen(),
          ),
        );
        break;
      case 'file_manager':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => _buildFileManagerScreen(),
          ),
        );
        break;
      case 'network_tools':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => _buildNetworkToolsScreen(),
          ),
        );
        break;
      case 'rpi_management':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => _buildRpiManagementScreen(),
          ),
        );
        break;
      case 'camera_stream':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => _buildCameraStreamScreen(),
          ),
        );
        break;
      case 'gpio_control':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => _buildGpioControlScreen(),
          ),
        );
        break;
      case 'log_viewer':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => _buildLogViewerScreen(),
          ),
        );
        break;
      case 'performance_monitor':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => _buildPerformanceMonitorScreen(),
          ),
        );
        break;
      case 'ai_assistant':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => _buildAiAssistantScreen(),
          ),
        );
        break;
      case 'rpi_diagnostics':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => _buildRpiDiagnosticsScreen(),
          ),
        );
        break;
      default:
        // For newly created apps, use the template screen
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => TemplateAppScreen(appModule: module),
          ),
        );
        break;
    }
  }

  void _launchLegacyApp(AppModule module) {
    switch (module.id) {
      case 'system_monitor':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const SystemMonitorScreen(),
          ),
        );
        break;
      default:
        _showComingSoon(context, module.title);
        break;
    }
  }

  Widget _buildFileManagerScreen() {
    // Return the actual FileManagerScreen implementation
    return const FileManagerScreen();
  }

  Widget _buildNetworkToolsScreen() {
    // Placeholder for network tools - in a real implementation this would
    // dynamically load the network tools app from its folder
    return Scaffold(
      appBar: AppBar(
        title: const Text('Network Tools'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.network_check,
              size: 64,
              color: Color(0xFF6366F1),
            ),
            SizedBox(height: 16),
            Text(
              'Network Tools',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Third-Party Modular App!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.blue,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'This demonstrates how third-party developers\ncan create their own modular apps.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _refreshApps() async {
    try {
      await AppModuleManager().refresh();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Apps refreshed successfully'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing apps: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  void _clearUserApps() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear User Apps'),
        content: const Text(
          'This will remove all imported apps from your device. '
          'Built-in apps will not be affected. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Clear'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await AppModuleManager().clearUserApps();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('User apps cleared successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error clearing user apps: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showComingSoon(BuildContext context, String appName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(appName),
        content: const Text('This app is coming soon! Stay tuned for updates.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAddAppDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddAppDialog(),
    );
  }

  void _showCreateAppDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const CreateAppDialog(),
    );
  }

  void _showEditAppDialog(BuildContext context, AppModule module) {
    showDialog(
      context: context,
      builder: (context) => EditAppDialog(
        module: module,
        onIconUpdated: () {
          // Force a rebuild of the apps screen to show updated icon
          if (mounted) {
            setState(() {});
          }
        },
        onAppUpdated: () {
          // Force a rebuild of the apps screen to show updated app settings
          if (mounted) {
            setState(() {});
          }
        },
      ),
    );
  }

  Widget _buildRpiManagementScreen() {
    return const RpiManagementScreen();
  }

  Widget _buildRpiDiagnosticsScreen() {
    return const RpiDiagnosticsScreen();
  }

  Widget _buildCameraStreamScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Camera Stream'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.camera_alt, size: 64, color: Color(0xFF6366F1)),
            SizedBox(height: 16),
            Text('Camera Stream',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('Live camera streaming coming soon!',
                style: TextStyle(fontSize: 16, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildGpioControlScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GPIO Control'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.sensors, size: 64, color: Color(0xFF6366F1)),
            SizedBox(height: 16),
            Text('GPIO Control',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('Hardware control coming soon!',
                style: TextStyle(fontSize: 16, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildLogViewerScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Log Viewer'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.article, size: 64, color: Color(0xFF6366F1)),
            SizedBox(height: 16),
            Text('Log Viewer',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('System log analysis coming soon!',
                style: TextStyle(fontSize: 16, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMonitorScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Performance Monitor'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.speed, size: 64, color: Color(0xFF6366F1)),
            SizedBox(height: 16),
            Text('Performance Monitor',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('Advanced monitoring coming soon!',
                style: TextStyle(fontSize: 16, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  Widget _buildAiAssistantScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Assistant'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.auto_awesome, size: 64, color: Color(0xFF6366F1)),
            SizedBox(height: 16),
            Text('AI Assistant',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('Built-in AI Assistant!',
                style: TextStyle(
                    fontSize: 16,
                    color: Colors.blue,
                    fontWeight: FontWeight.w600)),
            SizedBox(height: 16),
            Text(
                'This demonstrates the AI Assistant as a built-in app.\nIt\'s now part of the core Jelly Pi experience.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14, color: Colors.grey)),
          ],
        ),
      ),
    );
  }

  /// Build a simple Raspberry Pi logo using custom painting
  Widget _buildRaspberryPiLogo(double size) {
    return CustomPaint(
      size: Size(size, size),
      painter: RaspberryPiLogoPainter(),
    );
  }

  /// Check if a device is a Raspberry Pi based on its operating system and system info
  bool _isRaspberryPiDevice(Device device) {
    final os = device.operatingSystem.toLowerCase();
    final vendor = device.vendor.toLowerCase();

    // Primary check: explicit Raspberry Pi indicators
    if (os.contains('raspberry pi') ||
        os.contains('raspbian') ||
        os.contains('pi os')) {
      return true;
    }

    // Secondary check: vendor information
    if (vendor.contains('raspberry')) {
      return true;
    }

    // Tertiary check: system info for Raspberry Pi indicators
    final systemInfo = device.systemInfo;
    if (systemInfo.isNotEmpty) {
      // Check OS info
      final osInfo = systemInfo['os'];
      if (osInfo is Map<String, dynamic>) {
        final isRaspberryPi = osInfo['is_raspberry_pi'];
        if (isRaspberryPi == true) {
          return true;
        }

        final piModel = osInfo['pi_model'];
        if (piModel is String && piModel.isNotEmpty && piModel != 'Unknown') {
          return true;
        }
      }
    }

    return false;
  }
}

/// Custom painter for a simple Raspberry Pi logo
class RaspberryPiLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 3;

    // Draw a simple raspberry-like shape with circles
    // Main body (larger circle)
    canvas.drawCircle(center, radius, paint);

    // Smaller circles to create raspberry texture
    final smallRadius = radius / 4;
    canvas.drawCircle(Offset(center.dx - radius / 3, center.dy - radius / 3),
        smallRadius, Paint()..color = const Color(0xFFC51A4A));
    canvas.drawCircle(Offset(center.dx + radius / 3, center.dy - radius / 3),
        smallRadius, Paint()..color = const Color(0xFFC51A4A));
    canvas.drawCircle(Offset(center.dx, center.dy + radius / 3), smallRadius,
        Paint()..color = const Color(0xFFC51A4A));

    // Small leaf on top
    final leafPaint = Paint()
      ..color = const Color(0xFF4CAF50)
      ..style = PaintingStyle.fill;

    final leafPath = Path();
    leafPath.moveTo(center.dx, center.dy - radius);
    leafPath.quadraticBezierTo(
        center.dx - radius / 4,
        center.dy - radius - radius / 3,
        center.dx - radius / 6,
        center.dy - radius - radius / 6);
    leafPath.quadraticBezierTo(center.dx, center.dy - radius - radius / 4,
        center.dx + radius / 6, center.dy - radius - radius / 6);
    leafPath.quadraticBezierTo(center.dx + radius / 4,
        center.dy - radius - radius / 3, center.dx, center.dy - radius);

    canvas.drawPath(leafPath, leafPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
