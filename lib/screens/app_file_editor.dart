import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/app_module.dart';
import '../services/app_editor_service.dart';

class AppFileEditor extends StatefulWidget {
  final AppModule module;
  final String fileName;
  final AppFileType fileType;

  const AppFileEditor({
    super.key,
    required this.module,
    required this.fileName,
    required this.fileType,
  });

  @override
  State<AppFileEditor> createState() => _AppFileEditorState();
}

class _AppFileEditorState extends State<AppFileEditor> {
  final _controller = TextEditingController();
  final _editorService = AppEditorService();
  bool _isLoading = true;
  bool _hasChanges = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadFileContent();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _loadFileContent() async {
    try {
      final content = await _editorService.getFileContent(widget.module, widget.fileName);
      _controller.text = content;
      _controller.addListener(_onTextChanged);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading file: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  Future<void> _saveFile() async {
    setState(() {
      _isSaving = true;
    });

    try {
      await _editorService.saveFileContent(
        widget.module,
        widget.fileName,
        _controller.text,
      );
      setState(() {
        _hasChanges = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('File saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.fileName),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          if (_hasChanges)
            IconButton(
              onPressed: _isSaving ? null : _saveFile,
              icon: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              tooltip: 'Save',
            ),
          IconButton(
            onPressed: () => _copyToClipboard(),
            icon: const Icon(Icons.copy),
            tooltip: 'Copy to Clipboard',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // File info bar
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  child: Row(
                    children: [
                      Icon(
                        _getFileIcon(),
                        size: 16,
                        color: _getFileColor(),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.fileName,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: _getFileColor().withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _getFileColor().withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          _getFileTypeLabel(),
                          style: TextStyle(
                            color: _getFileColor(),
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const Spacer(),
                      if (_hasChanges)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'Modified',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                
                // Editor
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: TextField(
                      controller: _controller,
                      maxLines: null,
                      expands: true,
                      style: TextStyle(
                        fontFamily: _isCodeFile() ? 'monospace' : null,
                        fontSize: 14,
                      ),
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'Enter file content...',
                        contentPadding: EdgeInsets.all(16),
                      ),
                      textAlignVertical: TextAlignVertical.top,
                    ),
                  ),
                ),
                
                // Bottom toolbar
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    border: Border(
                      top: BorderSide(
                        color: Theme.of(context).dividerColor,
                      ),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Lines: ${_controller.text.split('\n').length} • Characters: ${_controller.text.length}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const Spacer(),
                      if (_hasChanges)
                        FilledButton.icon(
                          onPressed: _isSaving ? null : _saveFile,
                          icon: _isSaving
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : const Icon(Icons.save, size: 16),
                          label: const Text('Save'),
                        ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  IconData _getFileIcon() {
    switch (widget.fileType) {
      case AppFileType.source:
        return Icons.code;
      case AppFileType.configuration:
        return Icons.settings;
      case AppFileType.documentation:
        return Icons.description;
      case AppFileType.image:
        return Icons.image;
      case AppFileType.asset:
        return Icons.file_present;
      case AppFileType.other:
        return Icons.insert_drive_file;
    }
  }

  Color _getFileColor() {
    switch (widget.fileType) {
      case AppFileType.source:
        return Colors.blue;
      case AppFileType.configuration:
        return Colors.orange;
      case AppFileType.documentation:
        return Colors.green;
      case AppFileType.image:
        return Colors.purple;
      case AppFileType.asset:
        return Colors.teal;
      case AppFileType.other:
        return Colors.grey;
    }
  }

  String _getFileTypeLabel() {
    switch (widget.fileType) {
      case AppFileType.source:
        return 'SOURCE';
      case AppFileType.configuration:
        return 'CONFIG';
      case AppFileType.documentation:
        return 'DOCS';
      case AppFileType.image:
        return 'IMAGE';
      case AppFileType.asset:
        return 'ASSET';
      case AppFileType.other:
        return 'FILE';
    }
  }

  bool _isCodeFile() {
    return widget.fileType == AppFileType.source ||
           widget.fileType == AppFileType.configuration ||
           widget.fileName.endsWith('.json') ||
           widget.fileName.endsWith('.dart');
  }

  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: _controller.text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Content copied to clipboard')),
    );
  }
}
