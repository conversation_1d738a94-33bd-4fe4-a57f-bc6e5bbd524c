import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/app_state.dart';
import '../services/rpi_command_service.dart';

class ServiceManagerScreen extends StatefulWidget {
  final String deviceId;

  const ServiceManagerScreen({super.key, required this.deviceId});

  @override
  State<ServiceManagerScreen> createState() => _ServiceManagerScreenState();
}

class _ServiceManagerScreenState extends State<ServiceManagerScreen> {
  late RpiCommandService _commandService;
  List<SystemService> _services = [];
  List<SystemService> _filteredServices = [];
  bool _isLoading = true;
  String? _error;
  String _searchQuery = '';
  ServiceFilter _currentFilter = ServiceFilter.all;

  @override
  void initState() {
    super.initState();
    final appState = Provider.of<AppState>(context, listen: false);
    _commandService = RpiCommandService(appState);
    _loadServices();
  }

  Future<void> _loadServices() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await _commandService.executeCommand(
        widget.deviceId,
        'systemctl list-units --type=service --all --no-pager --plain | tail -n +2',
        progressTitle: 'Loading Services',
        progressSubtitle: 'Fetching systemd services...',
      );

      if (result.success) {
        final services = <SystemService>[];
        final lines = result.stdout.split('\n');

        for (final line in lines) {
          if (line.trim().isEmpty) continue;

          final parts = line.trim().split(RegExp(r'\s+'));
          if (parts.length >= 4) {
            final name = parts[0].replaceAll('.service', '');
            final loaded = parts[1] == 'loaded';
            final active = parts[2] == 'active';
            final running = parts[3] == 'running';
            final description =
                parts.length > 4 ? parts.sublist(4).join(' ') : '';

            services.add(SystemService(
              name: name,
              isLoaded: loaded,
              isActive: active,
              isRunning: running,
              description: description,
              isEnabled: false, // Will be loaded separately
            ));
          }
        }

        // Load enabled status for each service
        await _loadEnabledStatus(services);

        setState(() {
          _services = services;
          _applyFilters();
        });
      } else {
        throw Exception('Failed to load services: ${result.stderr}');
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadEnabledStatus(List<SystemService> services) async {
    try {
      final result = await _commandService.executeCommand(
        widget.deviceId,
        'systemctl list-unit-files --type=service --no-pager --plain | tail -n +2',
        showProgress: false,
      );

      if (result.success) {
        final enabledServices = <String>{};
        final lines = result.stdout.split('\n');

        for (final line in lines) {
          if (line.trim().isEmpty) continue;

          final parts = line.trim().split(RegExp(r'\s+'));
          if (parts.length >= 2 && parts[1] == 'enabled') {
            enabledServices.add(parts[0].replaceAll('.service', ''));
          }
        }

        for (final service in services) {
          service.isEnabled = enabledServices.contains(service.name);
        }
      }
    } catch (e) {
      debugPrint('Error loading enabled status: $e');
    }
  }

  void _applyFilters() {
    var filtered = _services.where((service) {
      // Apply search filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!service.name.toLowerCase().contains(query) &&
            !service.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // Apply status filter
      switch (_currentFilter) {
        case ServiceFilter.running:
          return service.isRunning;
        case ServiceFilter.stopped:
          return !service.isRunning;
        case ServiceFilter.enabled:
          return service.isEnabled;
        case ServiceFilter.disabled:
          return !service.isEnabled;
        case ServiceFilter.all:
          return true;
      }
    }).toList();

    // Sort by name
    filtered.sort((a, b) => a.name.compareTo(b.name));

    setState(() {
      _filteredServices = filtered;
    });
  }

  Future<void> _executeServiceAction(
      SystemService service, ServiceAction action) async {
    final actionName = action.toString().split('.').last;

    try {
      final command = 'sudo systemctl $actionName ${service.name}';
      await _commandService.executeCommand(
        widget.deviceId,
        command,
        progressTitle: '${actionName.toUpperCase()} Service',
        progressSubtitle: '${actionName}ing ${service.name}...',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Successfully ${actionName}ed ${service.name}'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh services after action
        await Future.delayed(const Duration(milliseconds: 500));
        _loadServices();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error ${actionName}ing service: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showServiceActions(SystemService service) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              service.name,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              service.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                if (!service.isRunning)
                  _buildActionButton(
                    'Start',
                    Icons.play_arrow,
                    Colors.green,
                    () => _executeServiceAction(service, ServiceAction.start),
                  ),
                if (service.isRunning)
                  _buildActionButton(
                    'Stop',
                    Icons.stop,
                    Colors.red,
                    () => _executeServiceAction(service, ServiceAction.stop),
                  ),
                if (service.isRunning)
                  _buildActionButton(
                    'Restart',
                    Icons.restart_alt,
                    Colors.orange,
                    () => _executeServiceAction(service, ServiceAction.restart),
                  ),
                if (!service.isEnabled)
                  _buildActionButton(
                    'Enable',
                    Icons.check_circle,
                    Colors.blue,
                    () => _executeServiceAction(service, ServiceAction.enable),
                  ),
                if (service.isEnabled)
                  _buildActionButton(
                    'Disable',
                    Icons.cancel,
                    Colors.grey,
                    () => _executeServiceAction(service, ServiceAction.disable),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
      String label, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton.icon(
      onPressed: () {
        Navigator.of(context).pop();
        onPressed();
      },
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Service Manager'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadServices,
            tooltip: 'Refresh Services',
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(120),
          child: Column(
            children: [
              // Search bar
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'Search services...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                    _applyFilters();
                  },
                ),
              ),
              // Filter chips
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: ServiceFilter.values.map((filter) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(_getFilterLabel(filter)),
                        selected: _currentFilter == filter,
                        onSelected: (selected) {
                          setState(() {
                            _currentFilter = filter;
                          });
                          _applyFilters();
                        },
                      ),
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error loading services'),
                      const SizedBox(height: 8),
                      Text(_error!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadServices,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _filteredServices.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.search_off, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('No services found'),
                          SizedBox(height: 8),
                          Text('Try adjusting your search or filter'),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _filteredServices.length,
                      itemBuilder: (context, index) {
                        final service = _filteredServices[index];
                        return _buildServiceTile(service);
                      },
                    ),
    );
  }

  Widget _buildServiceTile(SystemService service) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: service.isRunning ? Colors.green : Colors.grey,
        child: Icon(
          service.isRunning ? Icons.play_arrow : Icons.stop,
          color: Colors.white,
        ),
      ),
      title: Text(service.name),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            service.description.isNotEmpty
                ? service.description
                : 'No description',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              _buildStatusChip(
                service.isRunning ? 'Running' : 'Stopped',
                service.isRunning ? Colors.green : Colors.grey,
              ),
              const SizedBox(width: 8),
              _buildStatusChip(
                service.isEnabled ? 'Enabled' : 'Disabled',
                service.isEnabled ? Colors.blue : Colors.orange,
              ),
            ],
          ),
        ],
      ),
      trailing: const Icon(Icons.more_vert),
      onTap: () => _showServiceActions(service),
    );
  }

  Widget _buildStatusChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _getFilterLabel(ServiceFilter filter) {
    switch (filter) {
      case ServiceFilter.all:
        return 'All';
      case ServiceFilter.running:
        return 'Running';
      case ServiceFilter.stopped:
        return 'Stopped';
      case ServiceFilter.enabled:
        return 'Enabled';
      case ServiceFilter.disabled:
        return 'Disabled';
    }
  }
}

class SystemService {
  final String name;
  final bool isLoaded;
  final bool isActive;
  final bool isRunning;
  final String description;
  bool isEnabled;

  SystemService({
    required this.name,
    required this.isLoaded,
    required this.isActive,
    required this.isRunning,
    required this.description,
    required this.isEnabled,
  });
}

enum ServiceFilter { all, running, stopped, enabled, disabled }

enum ServiceAction { start, stop, restart, enable, disable }
