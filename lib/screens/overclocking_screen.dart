import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/app_state.dart';
import '../services/rpi_command_service.dart';

class OverclockingScreen extends StatefulWidget {
  final String deviceId;

  const OverclockingScreen({super.key, required this.deviceId});

  @override
  State<OverclockingScreen> createState() => _OverclockingScreenState();
}

class _OverclockingScreenState extends State<OverclockingScreen> {
  late RpiCommandService _commandService;
  Map<String, dynamic> _currentConfig = {};
  Map<String, dynamic> _originalConfig = {};
  bool _isLoading = true;
  bool _hasChanges = false;
  String? _error;

  // Configuration values
  double _armFreq = 1500;
  double _gpuFreq = 500;
  double _coreVolt = 1.2;
  int _gpuMemSplit = 64;
  String _cpuGovernor = 'ondemand';
  double _tempLimit = 85;

  final Map<String, double> _presetConfigs = {
    'Conservative': 1400,
    'Moderate': 1600,
    'Aggressive': 1800,
    'Extreme': 2000,
  };

  @override
  void initState() {
    super.initState();
    final appState = Provider.of<AppState>(context, listen: false);
    _commandService = RpiCommandService(appState);
    _loadCurrentConfig();
  }

  Future<void> _loadCurrentConfig() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load current config.txt values
      final configResult = await _commandService.executeCommand(
        widget.deviceId,
        'cat /boot/config.txt',
        progressTitle: 'Loading Configuration',
        progressSubtitle: 'Reading current settings...',
      );

      if (configResult.success) {
        _parseConfigFile(configResult.stdout);
      }

      // Load current CPU governor
      final governorResult = await _commandService.executeCommand(
        widget.deviceId,
        'cat /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor',
        showProgress: false,
      );

      if (governorResult.success) {
        _cpuGovernor = governorResult.stdout.trim();
      }

      // Load current temperature
      final tempResult = await _commandService.executeCommand(
        widget.deviceId,
        'vcgencmd measure_temp',
        showProgress: false,
      );

      if (tempResult.success) {
        final tempMatch =
            RegExp(r'temp=(\d+\.?\d*)').firstMatch(tempResult.stdout);
        if (tempMatch != null) {
          _currentConfig['current_temp'] = double.parse(tempMatch.group(1)!);
        }
      }

      _originalConfig = Map.from(_currentConfig);
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _parseConfigFile(String content) {
    final lines = content.split('\n');

    for (final line in lines) {
      final trimmed = line.trim();
      if (trimmed.isEmpty || trimmed.startsWith('#')) continue;

      if (trimmed.contains('=')) {
        final parts = trimmed.split('=');
        if (parts.length == 2) {
          final key = parts[0].trim();
          final value = parts[1].trim();

          switch (key) {
            case 'arm_freq':
              _armFreq = double.tryParse(value) ?? _armFreq;
              break;
            case 'gpu_freq':
              _gpuFreq = double.tryParse(value) ?? _gpuFreq;
              break;
            case 'over_voltage':
              _coreVolt = 1.2 + (double.tryParse(value) ?? 0) * 0.025;
              break;
            case 'gpu_mem':
              _gpuMemSplit = int.tryParse(value) ?? _gpuMemSplit;
              break;
            case 'temp_limit':
              _tempLimit = double.tryParse(value) ?? _tempLimit;
              break;
          }
        }
      }
    }

    _currentConfig = {
      'arm_freq': _armFreq,
      'gpu_freq': _gpuFreq,
      'core_volt': _coreVolt,
      'gpu_mem': _gpuMemSplit,
      'temp_limit': _tempLimit,
      'cpu_governor': _cpuGovernor,
    };
  }

  void _checkForChanges() {
    final hasChanges = _armFreq != (_originalConfig['arm_freq'] ?? 1500) ||
        _gpuFreq != (_originalConfig['gpu_freq'] ?? 500) ||
        _coreVolt != (_originalConfig['core_volt'] ?? 1.2) ||
        _gpuMemSplit != (_originalConfig['gpu_mem'] ?? 64) ||
        _tempLimit != (_originalConfig['temp_limit'] ?? 85) ||
        _cpuGovernor != (_originalConfig['cpu_governor'] ?? 'ondemand');

    setState(() {
      _hasChanges = hasChanges;
    });
  }

  Future<void> _applyPreset(String presetName) async {
    final freq = _presetConfigs[presetName]!;

    setState(() {
      _armFreq = freq;
      // Adjust other settings based on preset
      switch (presetName) {
        case 'Conservative':
          _gpuFreq = 400;
          _coreVolt = 1.2;
          break;
        case 'Moderate':
          _gpuFreq = 500;
          _coreVolt = 1.25;
          break;
        case 'Aggressive':
          _gpuFreq = 600;
          _coreVolt = 1.3;
          break;
        case 'Extreme':
          _gpuFreq = 700;
          _coreVolt = 1.35;
          break;
      }
    });

    _checkForChanges();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Applied $presetName preset')),
    );
  }

  Future<void> _saveConfiguration() async {
    try {
      // Create backup first
      await _commandService.executeCommand(
        widget.deviceId,
        'sudo cp /boot/firmware/config.txt /boot/firmware/config.txt.backup.${DateTime.now().millisecondsSinceEpoch}',
        progressTitle: 'Saving Configuration',
        progressSubtitle: 'Creating backup...',
      );

      // Generate new config content
      final overVoltage = ((_coreVolt - 1.2) / 0.025).round();
      final configUpdates = [
        'arm_freq=${_armFreq.round()}',
        'gpu_freq=${_gpuFreq.round()}',
        'over_voltage=$overVoltage',
        'gpu_mem=$_gpuMemSplit',
        'temp_limit=${_tempLimit.round()}',
      ];

      // Update config.txt
      for (final update in configUpdates) {
        final key = update.split('=')[0];

        await _commandService.executeCommand(
          widget.deviceId,
          'sudo sed -i "/^$key=/d" /boot/firmware/config.txt && echo "$update" | sudo tee -a /boot/firmware/config.txt',
          showProgress: false,
        );
      }

      // Update CPU governor
      await _commandService.executeCommand(
        widget.deviceId,
        'echo "$_cpuGovernor" | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor',
        showProgress: false,
      );

      // Make governor change persistent
      await _commandService.executeCommand(
        widget.deviceId,
        'echo "echo $_cpuGovernor | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor" | sudo tee -a /etc/rc.local',
        showProgress: false,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Configuration saved successfully. Reboot required for changes to take effect.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 5),
          ),
        );

        _originalConfig = Map.from(_currentConfig);
        setState(() {
          _hasChanges = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving configuration: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _resetToDefaults() {
    setState(() {
      _armFreq = 1500;
      _gpuFreq = 500;
      _coreVolt = 1.2;
      _gpuMemSplit = 64;
      _tempLimit = 85;
      _cpuGovernor = 'ondemand';
    });
    _checkForChanges();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Overclocking & Performance'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          if (_hasChanges)
            IconButton(
              onPressed: _saveConfiguration,
              icon: const Icon(Icons.save),
              tooltip: 'Save Configuration',
            ),
          IconButton(
            onPressed: _loadCurrentConfig,
            icon: const Icon(Icons.refresh),
            tooltip: 'Reload',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error loading configuration'),
                      const SizedBox(height: 8),
                      Text(_error!),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadCurrentConfig,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildWarningCard(),
                      const SizedBox(height: 16),
                      _buildCurrentStatusCard(),
                      const SizedBox(height: 16),
                      _buildPresetsCard(),
                      const SizedBox(height: 16),
                      _buildCpuConfigCard(),
                      const SizedBox(height: 16),
                      _buildGpuConfigCard(),
                      const SizedBox(height: 16),
                      _buildSystemConfigCard(),
                      const SizedBox(height: 16),
                      if (_hasChanges) _buildSaveCard(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildWarningCard() {
    return Card(
      color: Colors.orange.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.warning, color: Colors.orange),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Overclocking Warning',
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade800),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Overclocking can void your warranty and may cause system instability. Monitor temperatures and test thoroughly.',
                    style:
                        TextStyle(color: Colors.orange.shade900, fontSize: 13),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentStatusCard() {
    final currentTemp = _currentConfig['current_temp'] ?? 0.0;
    final tempColor = currentTemp > 70
        ? Colors.red
        : currentTemp > 60
            ? Colors.orange
            : Colors.green;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current Status',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatusItem(
                    'Temperature',
                    '${currentTemp.toStringAsFixed(1)}°C',
                    tempColor,
                    Icons.thermostat,
                  ),
                ),
                Expanded(
                  child: _buildStatusItem(
                    'CPU Governor',
                    _cpuGovernor,
                    Colors.blue,
                    Icons.tune,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(
      String label, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Overclocking Presets',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _presetConfigs.entries.map((entry) {
                return ElevatedButton(
                  onPressed: () => _applyPreset(entry.key),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(entry.key),
                      Text(
                        '${entry.value.round()} MHz',
                        style: const TextStyle(fontSize: 10),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _resetToDefaults,
                    child: const Text('Reset to Defaults'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCpuConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'CPU Configuration',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildSlider(
              'ARM CPU Frequency',
              _armFreq,
              1000,
              2200,
              'MHz',
              (value) {
                setState(() {
                  _armFreq = value;
                });
                _checkForChanges();
              },
            ),
            const SizedBox(height: 16),
            _buildSlider(
              'Core Voltage',
              _coreVolt,
              1.2,
              1.4,
              'V',
              (value) {
                setState(() {
                  _coreVolt = value;
                });
                _checkForChanges();
              },
              divisions: 8,
            ),
            const SizedBox(height: 16),
            _buildDropdown(
              'CPU Governor',
              _cpuGovernor,
              ['ondemand', 'performance', 'powersave', 'conservative'],
              (value) {
                setState(() {
                  _cpuGovernor = value!;
                });
                _checkForChanges();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGpuConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'GPU Configuration',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildSlider(
              'GPU Frequency',
              _gpuFreq,
              300,
              800,
              'MHz',
              (value) {
                setState(() {
                  _gpuFreq = value;
                });
                _checkForChanges();
              },
            ),
            const SizedBox(height: 16),
            _buildSlider(
              'GPU Memory Split',
              _gpuMemSplit.toDouble(),
              16,
              512,
              'MB',
              (value) {
                setState(() {
                  _gpuMemSplit = value.round();
                });
                _checkForChanges();
              },
              divisions: 31,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemConfigCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Configuration',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildSlider(
              'Temperature Limit',
              _tempLimit,
              70,
              90,
              '°C',
              (value) {
                setState(() {
                  _tempLimit = value;
                });
                _checkForChanges();
              },
              divisions: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSlider(
    String label,
    double value,
    double min,
    double max,
    String unit,
    ValueChanged<double> onChanged, {
    int? divisions,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label),
            Text('${value.round()} $unit'),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: divisions ?? (max - min).round(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildDropdown(
    String label,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          items: options.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(option),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildSaveCard() {
    return Card(
      color: Colors.blue.withValues(alpha: 0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(Icons.info, color: Colors.blue),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Unsaved Changes',
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: Colors.blue),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'You have unsaved configuration changes. Save and reboot to apply them.',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            ElevatedButton(
              onPressed: _saveConfiguration,
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }
}
