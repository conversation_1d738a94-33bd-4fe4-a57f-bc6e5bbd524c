import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'dart:convert';
import '../services/app_state.dart';
import '../widgets/progress_notification.dart';

class ConfigFileEditor extends StatefulWidget {
  final String filePath;
  final String deviceId;

  const ConfigFileEditor({
    super.key,
    required this.filePath,
    required this.deviceId,
  });

  @override
  State<ConfigFileEditor> createState() => _ConfigFileEditorState();
}

class _ConfigFileEditorState extends State<ConfigFileEditor> {
  final _controller = TextEditingController();
  final _scrollController = ScrollController();
  bool _isLoading = true;
  bool _hasChanges = false;
  bool _isSaving = false;
  String? _error;
  String _originalContent = '';
  int _lineCount = 0;
  int _characterCount = 0;

  @override
  void initState() {
    super.initState();
    _loadFileContent();
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;
    final newLineCount = text.split('\n').length;
    final newCharacterCount = text.length;
    final newHasChanges = text != _originalContent;

    if (newLineCount != _lineCount ||
        newCharacterCount != _characterCount ||
        newHasChanges != _hasChanges) {
      setState(() {
        _lineCount = newLineCount;
        _characterCount = newCharacterCount;
        _hasChanges = newHasChanges;
      });
    }
  }

  Future<void> _loadFileContent() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final connection = appState.getSSHConnection(widget.deviceId);

      if (connection?.client == null) {
        throw Exception('No SSH connection available');
      }

      // Check if file exists and read it
      final checkCommand =
          'test -f "${widget.filePath}" && echo "exists" || echo "not_found"';
      final checkSession = await connection!.client!.execute(checkCommand);
      final checkOutput = await utf8.decoder.bind(checkSession.stdout).join();

      if (checkOutput.trim() == 'not_found') {
        // File doesn't exist, create empty content
        _originalContent = '';
      } else {
        // File exists, read its content
        final readCommand = 'cat "${widget.filePath}"';
        final readSession = await connection.client!.execute(readCommand);
        final content = await utf8.decoder.bind(readSession.stdout).join();
        final error = await utf8.decoder.bind(readSession.stderr).join();

        if (error.isNotEmpty && !error.contains('No such file')) {
          throw Exception('Failed to read file: $error');
        }

        _originalContent = content;
      }

      _controller.text = _originalContent;
      _onTextChanged();
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveFile() async {
    if (!_hasChanges) return;

    setState(() {
      _isSaving = true;
    });

    ProgressNotificationService().show(
      title: 'Saving ${widget.filePath.split('/').last}',
      subtitle: 'Creating backup and writing changes...',
    );

    try {
      final appState = Provider.of<AppState>(context, listen: false);
      final connection = appState.getSSHConnection(widget.deviceId);

      if (connection?.client == null) {
        throw Exception('No SSH connection available');
      }

      // Create backup first
      final backupPath =
          '${widget.filePath}.backup.${DateTime.now().millisecondsSinceEpoch}';
      final backupCommand =
          'sudo cp "${widget.filePath}" "$backupPath" 2>/dev/null || true';
      await connection!.client!.execute(backupCommand);

      ProgressNotificationService().update(
        title: 'Saving ${widget.filePath.split('/').last}',
        subtitle: 'Writing file content...',
        progress: 0.5,
      );

      // Write the new content to a temporary file first
      final tempPath =
          '/tmp/config_edit_${DateTime.now().millisecondsSinceEpoch}';
      final content = _controller.text;

      // Escape content for shell
      final escapedContent = content
          .replaceAll(r'\', r'\\')
          .replaceAll('"', r'\"')
          .replaceAll(r'$', r'\$');

      // Write to temp file
      final writeCommand = 'echo "$escapedContent" > "$tempPath"';
      final writeSession = await connection.client!.execute(writeCommand);
      final writeError = await utf8.decoder.bind(writeSession.stderr).join();

      if (writeError.isNotEmpty) {
        throw Exception('Failed to write temporary file: $writeError');
      }

      // Move temp file to final location
      final moveCommand = 'sudo mv "$tempPath" "${widget.filePath}"';
      final moveSession = await connection.client!.execute(moveCommand);
      final moveError = await utf8.decoder.bind(moveSession.stderr).join();

      if (moveError.isNotEmpty) {
        throw Exception('Failed to move file to final location: $moveError');
      }

      ProgressNotificationService().update(
        title: 'File saved successfully',
        subtitle: 'Configuration updated',
        progress: 1.0,
      );

      setState(() {
        _originalContent = content;
        _hasChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('${widget.filePath.split('/').last} saved successfully'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Backup created',
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Backup saved as: $backupPath')),
                );
              },
            ),
          ),
        );
      }

      // Hide progress after a short delay
      Future.delayed(const Duration(seconds: 2), () {
        ProgressNotificationService().hide();
      });
    } catch (e) {
      ProgressNotificationService().hide();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  void _copyToClipboard() {
    Clipboard.setData(ClipboardData(text: _controller.text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Content copied to clipboard')),
    );
  }

  bool _isConfigFile() {
    final fileName = widget.filePath.toLowerCase();
    return fileName.endsWith('.conf') ||
        fileName.endsWith('.config') ||
        fileName.endsWith('.txt') ||
        fileName.contains('config');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.filePath.split('/').last),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          if (_hasChanges)
            IconButton(
              onPressed: _isSaving ? null : _saveFile,
              icon: _isSaving
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              tooltip: 'Save',
            ),
          IconButton(
            onPressed: _copyToClipboard,
            icon: const Icon(Icons.copy),
            tooltip: 'Copy to Clipboard',
          ),
          IconButton(
            onPressed: _loadFileContent,
            icon: const Icon(Icons.refresh),
            tooltip: 'Reload',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline,
                          size: 64, color: Colors.red),
                      const SizedBox(height: 16),
                      Text('Error loading file',
                          style: Theme.of(context).textTheme.headlineSmall),
                      const SizedBox(height: 8),
                      Text(_error!, textAlign: TextAlign.center),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadFileContent,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    // File info header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .surfaceContainerHighest,
                        border: Border(
                          bottom:
                              BorderSide(color: Theme.of(context).dividerColor),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            _isConfigFile()
                                ? Icons.settings
                                : Icons.description,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.filePath,
                                  style: const TextStyle(
                                      fontWeight: FontWeight.w500),
                                ),
                                Text(
                                  'Configuration file editor',
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),
                          if (_hasChanges)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'Modified',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),

                    // Editor
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        child: Consumer<AppState>(
                          builder: (context, appState, child) => TextField(
                            controller: _controller,
                            scrollController: _scrollController,
                            maxLines: null,
                            expands: true,
                            style: TextStyle(
                              fontFamily: appState.terminalFontFamily,
                              fontSize: appState.terminalFontSize,
                            ),
                            decoration: const InputDecoration(
                              border: OutlineInputBorder(),
                              hintText: 'Enter configuration content...',
                              contentPadding: EdgeInsets.all(16),
                            ),
                            textAlignVertical: TextAlignVertical.top,
                          ),
                        ),
                      ),
                    ),

                    // Bottom toolbar
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .surfaceContainerHighest,
                        border: Border(
                          top:
                              BorderSide(color: Theme.of(context).dividerColor),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.info_outline,
                              size: 16, color: Colors.grey[600]),
                          const SizedBox(width: 8),
                          Text(
                            'Lines: $_lineCount • Characters: $_characterCount',
                            style: TextStyle(
                                fontSize: 12, color: Colors.grey[600]),
                          ),
                          const Spacer(),
                          if (_hasChanges)
                            FilledButton.icon(
                              onPressed: _isSaving ? null : _saveFile,
                              icon: _isSaving
                                  ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    )
                                  : const Icon(Icons.save, size: 16),
                              label: const Text('Save'),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
    );
  }
}
