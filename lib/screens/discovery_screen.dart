import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/app_state.dart';
import '../widgets/discovered_device_card.dart';

class DiscoveryScreen extends StatefulWidget {
  const DiscoveryScreen({super.key});

  @override
  State<DiscoveryScreen> createState() => _DiscoveryScreenState();
}

class _DiscoveryScreenState extends State<DiscoveryScreen> {
  List<Map<String, String?>> _discoveredDevices = [];
  List<Map<String, String?>> _allDiscoveredDevices = [];
  bool _isScanning = false;
  bool _showOnlyRaspberryPi = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return Container(
          color: const Color(0xFFF8FAFC),
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              // Info Card
              Container(
                margin: const EdgeInsets.all(16),
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Network Discovery',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Scan your local network to discover devices. This will ping all IP addresses in your network range and identify active devices.',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        const SizedBox(height: 12),

                        // Device Filter Toggle
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              children: [
                                const Icon(Icons.filter_list, size: 20),
                                const SizedBox(width: 8),
                                const Text('Filter:'),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: SegmentedButton<bool>(
                                    style: SegmentedButton.styleFrom(
                                      selectedBackgroundColor: Theme.of(context)
                                          .colorScheme
                                          .primary, // Match scan button
                                      selectedForegroundColor: Colors.white,
                                      backgroundColor: Colors.grey[100],
                                      foregroundColor: Colors.grey[700],
                                    ),
                                    segments: const [
                                      ButtonSegment<bool>(
                                        value: false,
                                        label: Text('All Devices'),
                                        icon: Icon(Icons.devices, size: 16),
                                      ),
                                      ButtonSegment<bool>(
                                        value: true,
                                        label: Text('Raspberry Pi'),
                                        icon: Icon(Icons.memory, size: 16),
                                      ),
                                    ],
                                    selected: {_showOnlyRaspberryPi},
                                    onSelectionChanged: (Set<bool> selection) {
                                      setState(() {
                                        _showOnlyRaspberryPi = selection.first;
                                        _filterDevices();
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        const SizedBox(height: 16),

                        if (_isScanning)
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2),
                                      ),
                                      const SizedBox(width: 12),
                                      const Text('Scanning network...'),
                                      const Spacer(),
                                      Text(
                                        '${_allDiscoveredDevices.length} found',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodySmall,
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  LinearProgressIndicator(
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    backgroundColor: Theme.of(context)
                                        .colorScheme
                                        .primaryContainer,
                                  ),
                                ],
                              ),
                            ),
                          )
                        else
                          SizedBox(
                            width: double.infinity,
                            child: FilledButton.icon(
                              onPressed: _scanNetwork,
                              icon: const Icon(Icons.search),
                              label: const Text('Start Network Scan'),
                              style: FilledButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),

              // Results
              if (_discoveredDevices.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      Text(
                        'Discovered Devices (${_discoveredDevices.length})',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const Spacer(),
                      if (_getRaspberryPiDevices().isNotEmpty) ...[
                        FilledButton.icon(
                          onPressed: _addAllRaspberryPi,
                          icon: const Icon(Icons.memory, size: 16),
                          label: Text(
                              'Add All RPi (${_getRaspberryPiDevices().length})'),
                          // Use default FilledButton styling to match scan button
                        ),
                        const SizedBox(width: 8),
                      ],
                      TextButton.icon(
                        onPressed: _clearResults,
                        icon: const Icon(Icons.clear),
                        label: const Text('Clear'),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
              ],

              // Device List
              Expanded(
                child: _discoveredDevices.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: _discoveredDevices.length,
                        itemBuilder: (context, index) {
                          final device = _discoveredDevices[index];
                          final existingDevice = appState.devices
                              .where((d) => d.ip == device['ip'])
                              .firstOrNull;

                          return Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: DiscoveredDeviceCard(
                              ip: device['ip']!,
                              mac: device['mac']!,
                              vendor: device['vendor']!,
                              hostname: device['hostname'],
                              isAlreadyAdded: existingDevice != null,
                              onAdd: () => _addDevice(device),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    if (_isScanning) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Scanning network...'),
            SizedBox(height: 8),
            Text(
              'This may take a few minutes',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No devices discovered',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Start a network scan to discover devices',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
          ),
        ],
      ),
    );
  }

  void _filterDevices() {
    if (_showOnlyRaspberryPi) {
      _discoveredDevices = _allDiscoveredDevices.where((device) {
        final vendor = device['vendor'] ?? '';
        return vendor.toLowerCase().contains('raspberry');
      }).toList();
    } else {
      _discoveredDevices = List.from(_allDiscoveredDevices);
    }
  }

  Future<void> _scanNetwork() async {
    setState(() {
      _isScanning = true;
      _discoveredDevices.clear();
      _allDiscoveredDevices.clear();
    });

    try {
      final appState = context.read<AppState>();
      final devices = await appState.scanNetwork();

      setState(() {
        _allDiscoveredDevices = devices;
        _filterDevices();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Found ${_allDiscoveredDevices.length} devices${_showOnlyRaspberryPi ? ' (${_discoveredDevices.length} Raspberry Pi)' : ''}'),
            action: _discoveredDevices.isNotEmpty
                ? SnackBarAction(
                    label: 'Add All',
                    onPressed: _addAllDevices,
                  )
                : null,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Scan failed: $e')),
        );
      }
    } finally {
      setState(() {
        _isScanning = false;
      });
    }
  }

  void _clearResults() {
    setState(() {
      _discoveredDevices.clear();
      _allDiscoveredDevices.clear();
    });
  }

  void _addDevice(Map<String, String?> deviceData) {
    final appState = context.read<AppState>();

    // Check if device already exists
    final existingDevice =
        appState.devices.where((d) => d.ip == deviceData['ip']).firstOrNull;

    if (existingDevice != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Device already exists')),
      );
      return;
    }

    final device = Device(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      ip: deviceData['ip']!,
      mac: deviceData['mac']!,
      vendor: deviceData['vendor']!,
      name: _generateDeviceName(deviceData),
      operatingSystem: deviceData['os'] ?? 'Unknown',
    );

    appState.addDevice(device);

    // Trigger a rebuild to update the button state
    setState(() {});

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Added ${device.name}')),
    );
  }

  void _addAllDevices() {
    final appState = context.read<AppState>();
    int addedCount = 0;

    for (final deviceData in _discoveredDevices) {
      // Check if device already exists
      final existingDevice =
          appState.devices.where((d) => d.ip == deviceData['ip']).firstOrNull;

      if (existingDevice == null) {
        final device = Device(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          ip: deviceData['ip']!,
          mac: deviceData['mac']!,
          vendor: deviceData['vendor']!,
          name: _generateDeviceName(deviceData),
          operatingSystem: deviceData['os'] ?? 'Unknown',
        );

        appState.addDevice(device);
        addedCount++;
      }
    }

    // Trigger a rebuild to update button states
    setState(() {});

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Added $addedCount new devices')),
    );
  }

  List<Map<String, String?>> _getRaspberryPiDevices() {
    return _discoveredDevices.where((device) {
      final vendor = device['vendor'] ?? '';
      return vendor.toLowerCase().contains('raspberry');
    }).toList();
  }

  void _addAllRaspberryPi() {
    final appState = context.read<AppState>();
    final rpiDevices = _getRaspberryPiDevices();
    int addedCount = 0;

    for (final deviceData in rpiDevices) {
      // Check if device already exists
      final existingDevice =
          appState.devices.where((d) => d.ip == deviceData['ip']).firstOrNull;

      if (existingDevice == null) {
        final device = Device(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          ip: deviceData['ip']!,
          mac: deviceData['mac']!,
          vendor: deviceData['vendor']!,
          name: _generateDeviceName(deviceData),
          operatingSystem: deviceData['os'] ?? 'Unknown',
        );

        appState.addDevice(device);
        addedCount++;
      }
    }

    // Trigger a rebuild to update button states
    setState(() {});

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Added $addedCount Raspberry Pi devices')),
    );
  }

  String _generateDeviceName(Map<String, String?> deviceData) {
    final hostname = deviceData['hostname'];
    final vendor = deviceData['vendor']!;
    final ip = deviceData['ip']!;
    final lastOctet = ip.split('.').last;

    // Use hostname if available and meaningful
    if (hostname != null && hostname.isNotEmpty && hostname != '_gateway') {
      return hostname;
    }

    // Generate name based on vendor
    if (vendor.toLowerCase().contains('raspberry')) {
      return 'Raspberry Pi ($lastOctet)';
    } else if (vendor == 'Apple') {
      return 'iPhone/iPad ($lastOctet)';
    } else if (vendor == 'Samsung') {
      return 'Samsung Device ($lastOctet)';
    } else if (vendor == 'Google') {
      return 'Google Device ($lastOctet)';
    } else if (vendor == 'Amazon') {
      return 'Amazon Device ($lastOctet)';
    } else if (vendor == 'Microsoft') {
      return 'Microsoft Device ($lastOctet)';
    } else if (vendor == 'LG Electronics') {
      return 'LG Device ($lastOctet)';
    } else if (vendor == 'Sony') {
      return 'Sony Device ($lastOctet)';
    } else if (vendor != 'Unknown') {
      return '$vendor ($lastOctet)';
    } else {
      return 'Network Device ($lastOctet)';
    }
  }
}
