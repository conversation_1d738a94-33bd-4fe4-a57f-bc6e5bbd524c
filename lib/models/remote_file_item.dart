import 'package:flutter/material.dart';

/// Represents a file or directory on a remote device
class RemoteFileItem {
  final String name;
  final String path;
  final int size;
  final DateTime modifiedDate;
  final String permissions;
  final String owner;
  final String group;
  final bool isDirectory;
  final bool isSymlink;
  final String? linkTarget;

  RemoteFileItem({
    required this.name,
    required this.path,
    required this.size,
    required this.modifiedDate,
    required this.permissions,
    required this.owner,
    required this.group,
    required this.isDirectory,
    this.isSymlink = false,
    this.linkTarget,
  });

  /// Get file extension
  String get extension {
    if (isDirectory) return '';
    final lastDot = name.lastIndexOf('.');
    if (lastDot == -1 || lastDot == 0) return '';
    return name.substring(lastDot + 1).toLowerCase();
  }

  /// Get human-readable file size
  String get formattedSize {
    if (isDirectory) return '';

    if (size < 1024) return '$size B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)} KB';
    if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Get formatted modification date
  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(modifiedDate);

    if (difference.inDays == 0) {
      return '${modifiedDate.hour.toString().padLeft(2, '0')}:${modifiedDate.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${modifiedDate.day}/${modifiedDate.month}/${modifiedDate.year}';
    }
  }

  /// Get appropriate icon for the file type
  IconData get icon {
    if (isDirectory) {
      return Icons.folder;
    }

    if (isSymlink) {
      return Icons.link;
    }

    switch (extension) {
      case 'txt':
      case 'md':
      case 'readme':
        return Icons.description;
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mkv':
      case 'mov':
      case 'wmv':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return Icons.audio_file;
      case 'zip':
      case 'rar':
      case 'tar':
      case 'gz':
      case '7z':
        return Icons.archive;
      case 'py':
      case 'js':
      case 'dart':
      case 'java':
      case 'cpp':
      case 'c':
      case 'h':
      case 'html':
      case 'css':
      case 'json':
      case 'xml':
        return Icons.code;
      case 'sh':
      case 'bash':
      case 'bat':
        return Icons.terminal;
      case 'exe':
      case 'deb':
      case 'rpm':
      case 'dmg':
        return Icons.apps;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// Get color for the file type
  Color get color {
    if (isDirectory) {
      return const Color(0xFF2196F3); // Blue for directories
    }

    if (isSymlink) {
      return const Color(0xFF9C27B0); // Purple for symlinks
    }

    switch (extension) {
      case 'txt':
      case 'md':
      case 'readme':
        return const Color(0xFF607D8B); // Blue Grey
      case 'pdf':
        return const Color(0xFFD32F2F); // Red
      case 'doc':
      case 'docx':
        return const Color(0xFF1976D2); // Blue
      case 'xls':
      case 'xlsx':
        return const Color(0xFF388E3C); // Green
      case 'ppt':
      case 'pptx':
        return const Color(0xFFE64A19); // Deep Orange
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return const Color(0xFF7B1FA2); // Purple
      case 'mp4':
      case 'avi':
      case 'mkv':
      case 'mov':
      case 'wmv':
        return const Color(0xFFE91E63); // Pink
      case 'mp3':
      case 'wav':
      case 'flac':
      case 'aac':
        return const Color(0xFF00BCD4); // Cyan
      case 'zip':
      case 'rar':
      case 'tar':
      case 'gz':
      case '7z':
        return const Color(0xFF795548); // Brown
      case 'py':
      case 'js':
      case 'dart':
      case 'java':
      case 'cpp':
      case 'c':
      case 'h':
      case 'html':
      case 'css':
      case 'json':
      case 'xml':
        return const Color(0xFF4CAF50); // Green
      case 'sh':
      case 'bash':
      case 'bat':
        return const Color(0xFF424242); // Grey
      case 'exe':
      case 'deb':
      case 'rpm':
      case 'dmg':
        return const Color(0xFFFF5722); // Deep Orange
      default:
        return const Color(0xFF9E9E9E); // Grey
    }
  }

  /// Check if file is hidden (starts with .)
  bool get isHidden => name.startsWith('.');

  /// Check if file is executable
  bool get isExecutable => permissions.contains('x');

  /// Parse ls -la output line into RemoteFileItem
  static RemoteFileItem? fromLsOutput(String line, String basePath) {
    try {
      // Parse ls -la output format:
      // -rw-r--r-- 1 <USER> <GROUP> 1234 Jan 01 12:34 filename
      final parts = line.trim().split(RegExp(r'\s+'));
      if (parts.length < 9) return null;

      final permissions = parts[0];
      final owner = parts[2];
      final group = parts[3];
      final sizeStr = parts[4];
      final month = parts[5];
      final day = parts[6];
      final timeOrYear = parts[7];
      final name = parts.sublist(8).join(' ');

      // Skip . and .. entries
      if (name == '.' || name == '..') return null;

      final isDirectory = permissions.startsWith('d');
      final isSymlink = permissions.startsWith('l');
      final size = int.tryParse(sizeStr) ?? 0;

      // Parse date
      final now = DateTime.now();
      final monthNum = _monthToNumber(month);
      final dayNum = int.tryParse(day) ?? 1;

      DateTime modifiedDate;
      if (timeOrYear.contains(':')) {
        // Time format (same year)
        final timeParts = timeOrYear.split(':');
        final hour = int.tryParse(timeParts[0]) ?? 0;
        final minute = int.tryParse(timeParts[1]) ?? 0;
        modifiedDate = DateTime(now.year, monthNum, dayNum, hour, minute);
      } else {
        // Year format
        final year = int.tryParse(timeOrYear) ?? now.year;
        modifiedDate = DateTime(year, monthNum, dayNum);
      }

      final path =
          basePath.endsWith('/') ? '$basePath$name' : '$basePath/$name';

      return RemoteFileItem(
        name: name,
        path: path,
        size: size,
        modifiedDate: modifiedDate,
        permissions: permissions,
        owner: owner,
        group: group,
        isDirectory: isDirectory,
        isSymlink: isSymlink,
      );
    } catch (e) {
      return null;
    }
  }

  static int _monthToNumber(String month) {
    switch (month.toLowerCase()) {
      case 'jan':
        return 1;
      case 'feb':
        return 2;
      case 'mar':
        return 3;
      case 'apr':
        return 4;
      case 'may':
        return 5;
      case 'jun':
        return 6;
      case 'jul':
        return 7;
      case 'aug':
        return 8;
      case 'sep':
        return 9;
      case 'oct':
        return 10;
      case 'nov':
        return 11;
      case 'dec':
        return 12;
      default:
        return 1;
    }
  }
}

/// File view modes
enum FileViewMode { list, grid }

/// File sorting options
enum FileSortBy { name, size, date, type }

/// File operation types
enum FileOperationType { delete, rename, download, copy, move, upload, preview }

/// File operation data
class FileOperation {
  final FileOperationType type;
  final String path;
  final String? newPath;
  final Map<String, dynamic>? metadata;

  FileOperation({
    required this.type,
    required this.path,
    this.newPath,
    this.metadata,
  });
}
