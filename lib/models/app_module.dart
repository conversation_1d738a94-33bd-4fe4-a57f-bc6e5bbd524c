import 'package:flutter/material.dart';

/// Represents a modular app that can be loaded dynamically
class AppModule {
  final String id;
  final String title;
  final String description;
  final String iconName;
  final String? iconPath; // For custom icons
  final String route;
  final String category;
  final bool isEnabled;
  final int sortOrder;
  final Map<String, dynamic> metadata;
  final List<String> requiredPermissions;
  final String version;
  final String author;
  final String? appFolderPath; // Path to the app's folder in assets
  final String? mainDartFile; // Main Dart file for the app
  final List<String> assetFiles; // List of asset files for this app
  final Map<String, dynamic> appConfig; // App-specific configuration
  final bool
      raspberryPiOnly; // Whether this app is only for Raspberry Pi devices

  const AppModule({
    required this.id,
    required this.title,
    required this.description,
    required this.iconName,
    this.iconPath,
    required this.route,
    this.category = 'general',
    this.isEnabled = true,
    this.sortOrder = 0,
    this.metadata = const {},
    this.requiredPermissions = const [],
    this.version = '1.0.0',
    this.author = 'Unknown',
    this.appFolderPath,
    this.mainDartFile,
    this.assetFiles = const [],
    this.appConfig = const {},
    this.raspberryPiOnly = false,
  });

  /// Check if this app module uses a custom icon
  bool get hasCustomIcon => iconPath != null && iconPath!.isNotEmpty;

  /// Get the appropriate icon for this app module (fallback for custom icons)
  IconData get icon {
    switch (iconName.toLowerCase()) {
      // System icons
      case 'monitor':
      case 'system_monitor':
        return Icons.monitor;
      case 'settings':
        return Icons.settings;
      case 'memory':
        return Icons.memory;
      case 'speed':
        return Icons.speed;
      case 'power_settings_new':
        return Icons.power_settings_new;
      case 'system_update':
        return Icons.system_update;
      case 'hardware':
        return Icons.hardware;
      case 'developer_board':
        return Icons.developer_board;
      case 'sensors':
      case 'gpio':
      case 'gpio_control':
        return Icons.sensors;
      case 'thermostat':
        return Icons.thermostat;
      case 'computer':
        return Icons.computer;
      case 'desktop_windows':
        return Icons.desktop_windows;
      case 'laptop':
        return Icons.laptop;
      case 'tablet':
        return Icons.tablet;
      case 'smartphone':
        return Icons.smartphone;
      case 'device_hub':
        return Icons.device_hub;
      case 'devices':
        return Icons.devices;
      case 'battery_full':
        return Icons.battery_full;
      case 'battery_charging_full':
        return Icons.battery_charging_full;
      case 'power':
        return Icons.power;
      case 'electrical_services':
        return Icons.electrical_services;
      case 'cpu':
        return Icons.memory;
      case 'sd_storage':
        return Icons.sd_storage;

      // Utilities icons
      case 'folder':
      case 'file_manager':
        return Icons.folder;
      case 'folder_open':
        return Icons.folder_open;
      case 'description':
        return Icons.description;
      case 'article':
        return Icons.article;
      case 'calculate':
        return Icons.calculate;
      case 'timer':
        return Icons.timer;
      case 'schedule':
        return Icons.schedule;
      case 'event':
        return Icons.event;
      case 'note_add':
        return Icons.note_add;
      case 'edit':
        return Icons.edit;
      case 'content_copy':
        return Icons.content_copy;
      case 'content_paste':
        return Icons.content_paste;

      // Network icons
      case 'network':
      case 'network_tools':
      case 'network_check':
        return Icons.network_check;
      case 'wifi':
        return Icons.wifi;
      case 'router':
        return Icons.router;
      case 'lan':
        return Icons.lan;
      case 'bluetooth':
        return Icons.bluetooth;
      case 'usb':
        return Icons.usb;
      case 'cloud':
        return Icons.cloud;
      case 'cloud_download':
        return Icons.cloud_download;
      case 'cloud_upload':
        return Icons.cloud_upload;

      // Development icons
      case 'code':
      case 'code_editor':
        return Icons.code;
      case 'terminal':
        return Icons.terminal;
      case 'bug_report':
        return Icons.bug_report;
      case 'build':
        return Icons.build;
      case 'health_and_safety':
        return Icons.health_and_safety;
      case 'api':
        return Icons.api;
      case 'integration_instructions':
        return Icons.integration_instructions;
      case 'data_object':
        return Icons.data_object;
      case 'schema':
        return Icons.schema;

      // Media icons
      case 'camera':
      case 'camera_stream':
        return Icons.camera_alt;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'videocam':
        return Icons.videocam;
      case 'photo':
        return Icons.photo;
      case 'image':
        return Icons.image;
      case 'music_note':
        return Icons.music_note;
      case 'audiotrack':
        return Icons.audiotrack;
      case 'video_library':
        return Icons.video_library;
      case 'movie':
        return Icons.movie;

      // Security icons
      case 'security':
        return Icons.security;
      case 'lock':
        return Icons.lock;
      case 'shield':
        return Icons.shield;
      case 'vpn_key':
        return Icons.vpn_key;
      case 'fingerprint':
        return Icons.fingerprint;
      case 'verified_user':
        return Icons.verified_user;

      // General icons
      case 'apps':
        return Icons.apps;
      case 'dashboard':
        return Icons.dashboard;
      case 'widgets':
        return Icons.widgets;
      case 'extension':
        return Icons.extension;
      case 'tune':
        return Icons.tune;
      case 'analytics':
        return Icons.analytics;
      case 'storage':
        return Icons.storage;
      case 'backup':
        return Icons.backup;
      case 'update':
        return Icons.system_update;
      case 'logs':
        return Icons.article;
      case 'performance':
        return Icons.speed;

      // AI/Assistant icons
      case 'auto_awesome':
      case 'ai':
      case 'ai_assistant':
        return Icons.auto_awesome;
      case 'psychology':
        return Icons.psychology;
      case 'smart_toy':
        return Icons.smart_toy;
      case 'assistant':
        return Icons.assistant;
      case 'lightbulb':
        return Icons.lightbulb;
      case 'science':
        return Icons.science;
      case 'biotech':
        return Icons.biotech;
      case 'precision_manufacturing':
        return Icons.precision_manufacturing;
      case 'chat_bubble':
        return Icons.chat_bubble;
      case 'trending_up':
        return Icons.trending_up;

      default:
        return Icons.apps;
    }
  }

  /// Create an AppModule from JSON
  factory AppModule.fromJson(Map<String, dynamic> json) {
    return AppModule(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      // Handle both 'icon' and 'iconName' for backward compatibility
      iconName: (json['icon'] ?? json['iconName']) as String,
      iconPath: json['iconPath'] as String?,
      route: json['route'] as String,
      category: json['category'] as String? ?? 'general',
      isEnabled: json['isEnabled'] as bool? ?? true,
      sortOrder: json['sortOrder'] as int? ?? 0,
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      requiredPermissions: (json['requiredPermissions'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      version: json['version'] as String? ?? '1.0.0',
      author: json['author'] as String? ?? 'Unknown',
      appFolderPath: json['appFolderPath'] as String?,
      mainDartFile: json['mainDartFile'] as String?,
      assetFiles: (json['assetFiles'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      appConfig: json['appConfig'] as Map<String, dynamic>? ?? {},
      raspberryPiOnly: json['raspberryPiOnly'] as bool? ?? false,
    );
  }

  /// Convert AppModule to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'iconName': iconName,
      'iconPath': iconPath,
      'route': route,
      'category': category,
      'isEnabled': isEnabled,
      'sortOrder': sortOrder,
      'metadata': metadata,
      'requiredPermissions': requiredPermissions,
      'version': version,
      'author': author,
      'appFolderPath': appFolderPath,
      'mainDartFile': mainDartFile,
      'assetFiles': assetFiles,
      'appConfig': appConfig,
      'raspberryPiOnly': raspberryPiOnly,
    };
  }

  /// Create a copy with modified properties
  AppModule copyWith({
    String? id,
    String? title,
    String? description,
    String? iconName,
    String? iconPath,
    String? route,
    String? category,
    bool? isEnabled,
    int? sortOrder,
    Map<String, dynamic>? metadata,
    List<String>? requiredPermissions,
    String? version,
    String? author,
    String? appFolderPath,
    String? mainDartFile,
    List<String>? assetFiles,
    Map<String, dynamic>? appConfig,
    bool? raspberryPiOnly,
    bool clearIconPath = false, // Flag to explicitly clear iconPath
  }) {
    return AppModule(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      iconPath: clearIconPath ? null : (iconPath ?? this.iconPath),
      route: route ?? this.route,
      category: category ?? this.category,
      isEnabled: isEnabled ?? this.isEnabled,
      sortOrder: sortOrder ?? this.sortOrder,
      metadata: metadata ?? this.metadata,
      requiredPermissions: requiredPermissions ?? this.requiredPermissions,
      version: version ?? this.version,
      author: author ?? this.author,
      appFolderPath: appFolderPath ?? this.appFolderPath,
      mainDartFile: mainDartFile ?? this.mainDartFile,
      assetFiles: assetFiles ?? this.assetFiles,
      appConfig: appConfig ?? this.appConfig,
      raspberryPiOnly: raspberryPiOnly ?? this.raspberryPiOnly,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppModule && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AppModule(id: $id, title: $title, category: $category)';
  }
}

/// Categories for organizing apps
enum AppCategory {
  system('System', 'System monitoring and management'),
  development('Development', 'Development tools and utilities'),
  network('Network', 'Network tools and diagnostics'),
  media('Media', 'Media streaming and management'),
  security('Security', 'Security and access control'),
  utilities('Utilities', 'General utilities and tools'),
  custom('Custom', 'Custom user applications');

  const AppCategory(this.displayName, this.description);

  final String displayName;
  final String description;
}
