import 'package:flutter/material.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import '../models/app_module.dart';

class TemplateAppScreen extends StatelessWidget {
  final AppModule appModule;

  const TemplateAppScreen({super.key, required this.appModule});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(appModule.title),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildAppIcon(appModule, 64),
            const SizedBox(height: 16),
            Text(
              appModule.title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                appModule.description,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This app is coming soon! Stay tuned for updates.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6366F1),
                foregroundColor: Colors.white,
              ),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build app icon widget (supports both material icons and custom images)
  Widget _buildAppIcon(AppModule module, double size) {
    if (module.hasCustomIcon) {
      // Show custom image
      final iconFile = File(path.join(module.appFolderPath!, module.iconPath!));
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(size * 0.15),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(size * 0.15),
          child: Image.file(
            iconFile,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // Fallback to material icon if custom image fails
              return Icon(
                module.icon,
                size: size * 0.7,
                color: const Color(0xFF6366F1),
              );
            },
          ),
        ),
      );
    } else {
      // Show material icon
      return Icon(
        module.icon,
        size: size,
        color: const Color(0xFF6366F1),
      );
    }
  }
}
