import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../services/app_state.dart';

class DeviceCard extends StatelessWidget {
  final Device device;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onConnect;
  final VoidCallback? onDisconnect;
  final VoidCallback? onRefresh;
  final VoidCallback? onSetDefault;

  const DeviceCard({
    super.key,
    required this.device,
    this.isSelected = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onConnect,
    this.onDisconnect,
    this.onRefresh,
    this.onSetDefault,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      elevation: isSelected ? 8 : 2,
      color: isSelected ? colorScheme.primaryContainer : null,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row
              Row(
                children: [
                  // Device Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color:
                          _getStatusColor(device.status).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getDeviceIcon(),
                      color: _getStatusColor(device.status),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Device Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Device name with RPi badge
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                device.name,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            // RPi badge next to name
                            if (_isRaspberryPiDevice()) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: const Color(
                                      0xFFC51A4A), // Raspberry Pi red
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  _getRaspberryPiBadgeText(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          device.vendor,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: colorScheme.outline,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Badges Column
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Status Badge (shows operation status if active, otherwise network status)
                      Consumer<AppState>(
                        builder: (context, appState, child) {
                          final operation =
                              appState.getDeviceOperation(device.id);

                          return Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 3),
                            decoration: BoxDecoration(
                              color: operation != null
                                  ? _getOperationStatusColor(operation.state)
                                  : _getStatusColor(device.status),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  operation != null
                                      ? _getOperationStatusIcon(operation.state)
                                      : _getStatusIcon(device.status),
                                  color: Colors.white,
                                  size: 10,
                                ),
                                const SizedBox(width: 3),
                                Text(
                                  operation != null
                                      ? _getOperationStatusText(operation.state)
                                      : _getStatusText(device.status),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),

                      // OS Badge
                      if (device.operatingSystem != 'Unknown') ...[
                        const SizedBox(height: 3),
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 6, vertical: 3),
                          decoration: BoxDecoration(
                            color: _getOSColor(device.operatingSystem),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _getOSIcon(device.operatingSystem),
                                color: Colors.white,
                                size: 10,
                              ),
                              const SizedBox(width: 3),
                              Text(
                                _getOSDisplayText(device.operatingSystem),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 9,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Device Details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      'IP Address',
                      device.ip,
                      Icons.language,
                      onTap: () => _copyToClipboard(context, device.ip),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      'MAC Address',
                      device.mac,
                      Icons.fingerprint,
                      onTap: () => _copyToClipboard(context, device.mac),
                    ),
                  ),
                ],
              ),

              if (device.lastSeen != null) ...[
                const SizedBox(height: 8),
                Text(
                  'Last seen: ${_formatDateTime(device.lastSeen!)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.outline,
                  ),
                ),
              ],

              const SizedBox(height: 16),

              // Action Buttons
              Row(
                children: [
                  // Connect/Disconnect Button
                  if (device.status == DeviceStatus.online &&
                      !device.isConnected)
                    FilledButton.icon(
                      onPressed: onConnect,
                      icon: const Icon(Icons.link, size: 16),
                      label: const Text('Connect'),
                    )
                  else if (device.isConnected)
                    FilledButton.tonalIcon(
                      onPressed: onDisconnect,
                      icon: const Icon(Icons.link_off, size: 16),
                      label: const Text('Disconnect'),
                    ),

                  const Spacer(),

                  // Action Menu
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'refresh':
                          onRefresh?.call();
                          break;
                        case 'edit':
                          onEdit?.call();
                          break;
                        case 'setDefault':
                          onSetDefault?.call();
                          break;
                        case 'delete':
                          onDelete?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'refresh',
                        child: ListTile(
                          leading: Icon(Icons.refresh),
                          title: Text('Refresh'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('Edit'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      PopupMenuItem(
                        value: 'setDefault',
                        child: ListTile(
                          leading: Icon(
                            device.isDefaultDevice
                                ? Icons.star
                                : Icons.star_border,
                            color: device.isDefaultDevice ? Colors.amber : null,
                          ),
                          title: Text(device.isDefaultDevice
                              ? 'Default Device'
                              : 'Set as Default'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete),
                          title: Text('Delete'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                    child: IconButton(
                      icon: const Icon(Icons.more_vert),
                      onPressed: null,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: theme.colorScheme.outline,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.outline,
                    ),
                  ),
                  Text(
                    value,
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getDeviceIcon() {
    if (device.vendor.toLowerCase().contains('raspberry')) {
      return Icons.memory; // Raspberry Pi icon
    }
    return Icons.computer;
  }

  Color _getStatusColor(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.online:
      case DeviceStatus.connected:
        return Colors.green;
      case DeviceStatus.offline:
        return Colors.red;
      case DeviceStatus.connecting:
      case DeviceStatus.disconnecting:
        return Colors.orange;
      case DeviceStatus.rebooting:
      case DeviceStatus.shuttingDown:
        return Colors.orange.shade700;
      case DeviceStatus.error:
        return Colors.red.shade700;
      case DeviceStatus.unknown:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.online:
        return Icons.wifi;
      case DeviceStatus.connected:
        return Icons.link;
      case DeviceStatus.offline:
        return Icons.wifi_off;
      case DeviceStatus.connecting:
      case DeviceStatus.disconnecting:
        return Icons.sync;
      case DeviceStatus.rebooting:
        return Icons.restart_alt;
      case DeviceStatus.shuttingDown:
        return Icons.power_settings_new;
      case DeviceStatus.error:
        return Icons.error;
      case DeviceStatus.unknown:
        return Icons.help_outline;
    }
  }

  String _getStatusText(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.online:
        return 'Online';
      case DeviceStatus.offline:
        return 'Offline';
      case DeviceStatus.connected:
        return 'Connected';
      case DeviceStatus.connecting:
        return 'Connecting';
      case DeviceStatus.disconnecting:
        return 'Disconnecting';
      case DeviceStatus.rebooting:
        return 'Rebooting';
      case DeviceStatus.shuttingDown:
        return 'Shutting Down';
      case DeviceStatus.error:
        return 'Error';
      case DeviceStatus.unknown:
        return 'Unknown';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Copied $text to clipboard'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Color _getOSColor(String operatingSystem) {
    final os = operatingSystem.toLowerCase();
    if (os.contains('raspberry pi')) {
      return Colors.green.shade600; // Raspberry Pi specific color
    } else if (os.contains('linux') ||
        os.contains('ubuntu') ||
        os.contains('debian') ||
        os.contains('raspbian')) {
      return Colors.orange;
    } else if (os.contains('windows')) {
      return Colors.blue;
    } else if (os.contains('macos') || os.contains('darwin')) {
      return Colors.grey.shade600;
    } else if (os.contains('android')) {
      return Colors.lightGreen;
    } else if (os.contains('detecting')) {
      return Colors.purple;
    } else {
      return Colors.grey;
    }
  }

  IconData _getOSIcon(String operatingSystem) {
    final os = operatingSystem.toLowerCase();
    if (os.contains('raspberry pi')) {
      return Icons.memory; // Raspberry Pi specific icon
    } else if (os.contains('linux') ||
        os.contains('ubuntu') ||
        os.contains('debian') ||
        os.contains('raspbian')) {
      return Icons.terminal;
    } else if (os.contains('windows')) {
      return Icons.desktop_windows;
    } else if (os.contains('macos') || os.contains('darwin')) {
      return Icons.laptop_mac;
    } else if (os.contains('android')) {
      return Icons.android;
    } else if (os.contains('detecting')) {
      return Icons.search;
    } else {
      return Icons.computer;
    }
  }

  String _getOSDisplayText(String operatingSystem) {
    if (operatingSystem.toLowerCase().contains('detecting')) {
      return 'Detecting...';
    }

    // For comprehensive OS names, try to shorten intelligently
    final os = operatingSystem.toLowerCase();

    // Raspberry Pi specific
    if (os.contains('raspberry pi os')) {
      return 'Pi OS';
    } else if (os.contains('raspberry pi ubuntu')) {
      return 'Pi Ubuntu';
    } else if (os.contains('raspberry pi debian')) {
      return 'Pi Debian';
    } else if (os.contains('raspberry pi android')) {
      return 'Pi Android';
    } else if (os.contains('raspberry pi')) {
      // Extract just the distro name after "Raspberry Pi"
      final parts = operatingSystem.split(' ');
      if (parts.length > 2) {
        return 'Pi ${parts[2]}';
      }
      return 'Raspberry Pi';
    }

    // Regular distributions - keep version if short enough
    if (operatingSystem.length <= 15) {
      return operatingSystem;
    }

    // For longer names, try to extract the main distro name
    if (os.contains('ubuntu')) {
      final match = RegExp(r'ubuntu\s*(\d+\.?\d*)?', caseSensitive: false)
          .firstMatch(operatingSystem);
      return match?.group(0) ?? 'Ubuntu';
    } else if (os.contains('debian')) {
      final match = RegExp(r'debian\s*(\d+\.?\d*)?', caseSensitive: false)
          .firstMatch(operatingSystem);
      return match?.group(0) ?? 'Debian';
    } else if (os.contains('fedora')) {
      final match = RegExp(r'fedora\s*(\d+\.?\d*)?', caseSensitive: false)
          .firstMatch(operatingSystem);
      return match?.group(0) ?? 'Fedora';
    } else if (os.contains('centos')) {
      return 'CentOS';
    } else if (os.contains('red hat')) {
      return 'RHEL';
    } else if (os.contains('opensuse')) {
      return 'openSUSE';
    } else if (os.contains('arch linux')) {
      return 'Arch';
    } else if (os.contains('manjaro')) {
      return 'Manjaro';
    } else if (os.contains('linux mint')) {
      return 'Mint';
    } else if (os.contains('elementary')) {
      return 'elementary';
    } else if (os.contains('pop!_os')) {
      return 'Pop!_OS';
    } else if (os.contains('kali')) {
      return 'Kali';
    } else if (os.contains('alpine')) {
      return 'Alpine';
    } else if (os.contains('gentoo')) {
      return 'Gentoo';
    } else if (os.contains('nixos')) {
      return 'NixOS';
    } else if (os.contains('based')) {
      // For "X-based" distributions, just show the base
      return operatingSystem.replaceAll('-based', '');
    } else if (os.contains('linux')) {
      return 'Linux';
    } else if (os.contains('windows')) {
      return 'Windows';
    } else if (os.contains('macos') || os.contains('darwin')) {
      return 'macOS';
    } else if (os.contains('android')) {
      return 'Android';
    } else {
      // Truncate long OS names
      return operatingSystem.length > 12
          ? '${operatingSystem.substring(0, 12)}...'
          : operatingSystem;
    }
  }

  // Operation status helper methods
  Color _getOperationStatusColor(DeviceOperationState operationState) {
    switch (operationState) {
      case DeviceOperationState.rebooting:
        return Colors.orange.shade700;
      case DeviceOperationState.shuttingDown:
        return Colors.red.shade700;
      case DeviceOperationState.updating:
        return Colors.blue.shade700;
      case DeviceOperationState.none:
        return Colors.grey;
    }
  }

  IconData _getOperationStatusIcon(DeviceOperationState operationState) {
    switch (operationState) {
      case DeviceOperationState.rebooting:
        return Icons.restart_alt;
      case DeviceOperationState.shuttingDown:
        return Icons.power_settings_new;
      case DeviceOperationState.updating:
        return Icons.system_update;
      case DeviceOperationState.none:
        return Icons.help_outline;
    }
  }

  String _getOperationStatusText(DeviceOperationState operationState) {
    switch (operationState) {
      case DeviceOperationState.rebooting:
        return 'Rebooting';
      case DeviceOperationState.shuttingDown:
        return 'Shutting Down';
      case DeviceOperationState.updating:
        return 'Updating';
      case DeviceOperationState.none:
        return 'None';
    }
  }

  /// Check if the device is a Raspberry Pi
  bool _isRaspberryPiDevice() {
    final os = device.operatingSystem.toLowerCase();
    final vendor = device.vendor.toLowerCase();
    return os.contains('raspberry pi') ||
        os.contains('raspbian') ||
        os.contains('pi os') ||
        vendor.contains('raspberry');
  }

  /// Get the Raspberry Pi badge text (e.g., "RPi 4")
  String _getRaspberryPiBadgeText() {
    final os = device.operatingSystem.toLowerCase();

    // Try to extract model number from OS string
    if (os.contains('raspberry pi')) {
      final match =
          RegExp(r'raspberry pi (\w+(?:\s+\w+)?)', caseSensitive: false)
              .firstMatch(os);
      if (match != null) {
        final modelPart = match.group(1)!;
        // Convert "4 Model B" to "4", "Zero W" to "Zero", etc.
        if (modelPart.startsWith('4')) return 'RPi 4';
        if (modelPart.startsWith('5')) return 'RPi 5';
        if (modelPart.startsWith('3')) return 'RPi 3';
        if (modelPart.startsWith('2')) return 'RPi 2';
        if (modelPart.startsWith('1')) return 'RPi 1';
        if (modelPart.toLowerCase().contains('zero')) return 'RPi Zero';
        return 'RPi $modelPart';
      }
    }

    // Fallback
    return 'RPi';
  }
}
