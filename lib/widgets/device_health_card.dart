import 'package:flutter/material.dart';
import '../services/app_state.dart';

class DeviceHealthCard extends StatelessWidget {
  final Device device;
  final VoidCallback? onRefresh;

  const DeviceHealthCard({
    super.key,
    required this.device,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (!device.hasSystemInfo) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Icons.health_and_safety, color: colorScheme.primary),
                  const SizedBox(width: 8),
                  Text(
                    'Device Health',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (onRefresh != null)
                    IconButton(
                      onPressed: onRefresh,
                      icon: const Icon(Icons.refresh, size: 20),
                      tooltip: 'Refresh health data',
                    ),
                ],
              ),
              const SizedBox(height: 16),
              if (device.isConnected) ...[
                // Show loading state when connected but no system info yet
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: colorScheme.primary,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Getting System Health Info...',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ] else ...[
                Text(
                  'No health data available',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.health_and_safety,
                  color: _getHealthColor(device.healthStatus),
                ),
                const SizedBox(width: 8),
                Text(
                  'Device Health',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getHealthColor(device.healthStatus)
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _getHealthColor(device.healthStatus)
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    device.healthStatus,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: _getHealthColor(device.healthStatus),
                    ),
                  ),
                ),
                const Spacer(),
                if (onRefresh != null)
                  IconButton(
                    onPressed: onRefresh,
                    icon: const Icon(Icons.refresh, size: 20),
                    tooltip: 'Refresh health data',
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Primary Health Metrics
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'CPU Usage',
                    '${device.cpuUsage?.toStringAsFixed(1) ?? '--'}%',
                    Icons.memory,
                    device.cpuUsage != null ? device.cpuUsage! / 100 : 0,
                    _getCpuColor(device.cpuUsage),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Memory',
                    device.memoryInfo != null
                        ? '${(device.memoryInfo!['used'] / 1024).toStringAsFixed(1)}G / ${(device.memoryInfo!['total'] / 1024).toStringAsFixed(1)}G (${device.memoryInfo!['usage_percent']?.toStringAsFixed(1) ?? '--'}%)'
                        : 'N/A',
                    Icons.storage,
                    device.memoryInfo != null
                        ? device.memoryInfo!['usage_percent'] / 100
                        : 0,
                    _getMemoryColor(device.memoryInfo?['usage_percent']),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'CPU Temp',
                    '${device.temperature?.toStringAsFixed(1) ?? '--'}°C',
                    Icons.thermostat,
                    device.temperature != null
                        ? (device.temperature! / 80).clamp(0.0, 1.0)
                        : 0,
                    _getTemperatureColor(device.temperature),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Secondary Health Metrics
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'GPU Temp',
                    device.gpuTemperature != null
                        ? '${device.gpuTemperature!.toStringAsFixed(1)}°C'
                        : 'N/A',
                    Icons.videogame_asset,
                    device.gpuTemperature != null
                        ? (device.gpuTemperature! / 80).clamp(0.0, 1.0)
                        : 0,
                    _getTemperatureColor(device.gpuTemperature),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'CPU Freq',
                    device.cpuFrequency != null
                        ? '${device.cpuFrequency!.toStringAsFixed(0)} MHz'
                        : 'N/A',
                    Icons.speed,
                    device.cpuFrequency != null
                        ? (device.cpuFrequency! / 2000).clamp(0.0, 1.0)
                        : 0,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Disk Usage',
                    '${device.diskInfo?['usage_percent'] ?? '--'}%',
                    Icons.storage,
                    device.diskInfo != null
                        ? (device.diskInfo!['usage_percent'] / 100)
                            .clamp(0.0, 1.0)
                        : 0,
                    _getDiskColor(device.diskInfo?['usage_percent']),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Power and System Status
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Power Status',
                    device.isThrottled == true
                        ? 'Throttled'
                        : device.isThrottled == false
                            ? 'Normal'
                            : 'N/A',
                    Icons.power,
                    device.isThrottled == true ? 1.0 : 0.0,
                    device.isThrottled == true
                        ? Colors.red
                        : device.isThrottled == false
                            ? Colors.green
                            : Colors.grey,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Core Voltage',
                    device.coreVoltage != null
                        ? '${device.coreVoltage!.toStringAsFixed(2)}V'
                        : 'N/A',
                    Icons.electrical_services,
                    device.coreVoltage != null
                        ? (device.coreVoltage! / 1.5).clamp(0.0, 1.0)
                        : 0,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'GPU Memory',
                    device.gpuMemory != null ? '${device.gpuMemory} MB' : 'N/A',
                    Icons.memory,
                    device.gpuMemory != null
                        ? (device.gpuMemory! / 128).clamp(0.0, 1.0)
                        : 0,
                    Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // CPU Cores Information
            if (device.cpuCores != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color: colorScheme.outline.withValues(alpha: 0.2)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.memory,
                            size: 16, color: colorScheme.primary),
                        const SizedBox(width: 8),
                        Text(
                          'CPU Cores Usage',
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 6,
                      children: device.cpuCores!.entries.map<Widget>((entry) {
                        final coreUsage = entry.value?.toDouble() ?? 0.0;
                        return Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color:
                                _getCpuColor(coreUsage).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color: _getCpuColor(coreUsage)
                                  .withValues(alpha: 0.3),
                            ),
                          ),
                          child: Text(
                            '${entry.key.toUpperCase()}: ${coreUsage.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: _getCpuColor(coreUsage),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Additional Info
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Uptime',
                    device.uptime ?? '--',
                    Icons.schedule,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    context,
                    'Load Avg',
                    device.loadAverage != null
                        ? '${device.loadAverage!['1min']?.toStringAsFixed(2) ?? '--'}'
                        : '--',
                    Icons.trending_up,
                  ),
                ),
              ],
            ),

            if (device.lastSystemInfoUpdate != null) ...[
              const SizedBox(height: 12),
              Text(
                'Last updated: ${_formatTime(device.lastSystemInfoUpdate!)}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    double progress,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: color.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.primary),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Color _getHealthColor(String status) {
    switch (status) {
      case 'Good':
        return Colors.green;
      case 'Warning':
        return Colors.orange;
      case 'Critical':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getCpuColor(double? usage) {
    if (usage == null) return Colors.grey;
    if (usage < 50) return Colors.green;
    if (usage < 80) return Colors.orange;
    return Colors.red;
  }

  Color _getMemoryColor(double? usage) {
    if (usage == null) return Colors.grey;
    if (usage < 70) return Colors.green;
    if (usage < 85) return Colors.orange;
    return Colors.red;
  }

  Color _getTemperatureColor(double? temp) {
    if (temp == null) return Colors.grey;
    if (temp < 60) return Colors.green;
    if (temp < 70) return Colors.orange;
    return Colors.red;
  }

  Color _getDiskColor(int? usage) {
    if (usage == null) return Colors.grey;
    if (usage < 80) return Colors.green;
    if (usage < 90) return Colors.orange;
    return Colors.red;
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inMinutes < 1) return 'Just now';
    if (diff.inMinutes < 60) return '${diff.inMinutes}m ago';
    if (diff.inHours < 24) return '${diff.inHours}h ago';
    return '${diff.inDays}d ago';
  }
}
