import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'dart:convert';
import 'package:archive/archive.dart';
import 'package:path/path.dart' as path;
import '../models/app_module.dart';
import '../services/app_editor_service.dart';
import '../screens/app_source_code_editor.dart';
import '../screens/app_configuration_editor.dart';
import '../screens/app_file_browser.dart';
import 'icon_picker_dialog.dart';

class EditAppDialog extends StatefulWidget {
  final AppModule module;
  final VoidCallback? onIconUpdated;
  final VoidCallback? onAppUpdated;

  const EditAppDialog({
    super.key,
    required this.module,
    this.onIconUpdated,
    this.onAppUpdated,
  });

  @override
  State<EditAppDialog> createState() => _EditAppDialogState();
}

class _EditAppDialogState extends State<EditAppDialog> {
  bool _isLoading = false;
  late bool _raspberryPiOnly;

  @override
  void initState() {
    super.initState();
    _raspberryPiOnly = widget.module.raspberryPiOnly;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          const Icon(Icons.edit, color: Color(0xFF6366F1)),
          const SizedBox(width: 12),
          Text('Edit ${widget.module.title}'),
        ],
      ),
      content: SizedBox(
        width: 400,
        height: 600, // Fixed height to prevent overflow
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Developer tools for ${widget.module.title}',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),

              // App Info Card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _getIconData(widget.module.iconName),
                            size: 32,
                            color: const Color(0xFF6366F1),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.module.title,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'v${widget.module.version} • ${widget.module.author}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        widget.module.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Raspberry Pi Only checkbox
              Card(
                elevation: 1,
                color: const Color(0xFFFAFBFC),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: Colors.grey.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: CheckboxListTile(
                  contentPadding: const EdgeInsets.all(16),
                  title: const Row(
                    children: [
                      Icon(Icons.memory, color: Color(0xFF6366F1), size: 20),
                      SizedBox(width: 8),
                      Text(
                        'Raspberry Pi Only',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                    ],
                  ),
                  subtitle: const Text(
                    'This app will only be visible when connected to a Raspberry Pi device',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF6B7280),
                    ),
                  ),
                  value: _raspberryPiOnly,
                  activeColor: const Color(0xFF6366F1),
                  onChanged: (value) {
                    setState(() {
                      _raspberryPiOnly = value ?? false;
                    });
                    _updateRaspberryPiOnly();
                  },
                ),
              ),

              const SizedBox(height: 16),

              // Action Buttons
              const Text(
                'Developer Actions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),

              _buildActionButton(
                icon: Icons.folder_open,
                title: 'Browse App Files',
                subtitle: 'View all app assets and files',
                onTap: () => _browseAppFiles(),
              ),

              _buildActionButton(
                icon: Icons.image,
                title: 'Edit Icon',
                subtitle: 'Change the app icon',
                onTap: () => _editIcon(),
              ),

              _buildActionButton(
                icon: Icons.settings,
                title: 'Edit Configuration',
                subtitle: 'Modify app.json and config.json',
                onTap: () => _editConfiguration(),
              ),

              _buildActionButton(
                icon: Icons.code,
                title: 'Edit Source Code',
                subtitle: 'Modify app implementation',
                onTap: () => _editSourceCode(),
              ),

              if (widget.module.appFolderPath != null)
                _buildActionButton(
                  icon: Icons.file_download,
                  title: 'Export App',
                  subtitle: 'Export as ZIP file for sharing',
                  onTap: () => _exportApp(),
                ),

              // Only show delete option for user-imported apps
              if (_isUserImportedApp())
                _buildActionButton(
                  icon: Icons.delete_outline,
                  title: 'Delete App',
                  subtitle: 'Remove this app permanently',
                  onTap: () => _deleteApp(),
                  isDestructive: true,
                ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          icon,
          color: isDestructive ? Colors.red : const Color(0xFF6366F1),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isDestructive ? Colors.red : null,
          ),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: _isLoading ? null : onTap,
      ),
    );
  }

  /// Check if this is a user-imported app (not built-in)
  bool _isUserImportedApp() {
    return widget.module.appFolderPath?.contains('imported_apps') == true;
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      // System icons
      case 'settings':
        return Icons.settings;
      case 'monitor':
        return Icons.monitor;
      case 'memory':
        return Icons.memory;
      case 'speed':
        return Icons.speed;
      case 'power_settings_new':
        return Icons.power_settings_new;
      case 'system_update':
        return Icons.system_update;
      case 'hardware':
        return Icons.hardware;
      case 'developer_board':
        return Icons.developer_board;
      case 'sensors':
        return Icons.sensors;
      case 'thermostat':
        return Icons.thermostat;
      case 'computer':
        return Icons.computer;
      case 'desktop_windows':
        return Icons.desktop_windows;
      case 'laptop':
        return Icons.laptop;
      case 'tablet':
        return Icons.tablet;
      case 'smartphone':
        return Icons.smartphone;
      case 'device_hub':
        return Icons.device_hub;
      case 'devices':
        return Icons.devices;
      case 'battery_full':
        return Icons.battery_full;
      case 'battery_charging_full':
        return Icons.battery_charging_full;
      case 'power':
        return Icons.power;
      case 'electrical_services':
        return Icons.electrical_services;
      case 'cpu':
        return Icons.memory;
      case 'storage':
        return Icons.storage;
      case 'sd_storage':
        return Icons.sd_storage;
      case 'usb':
        return Icons.usb;

      // Network icons
      case 'network_check':
        return Icons.network_check;
      case 'wifi':
        return Icons.wifi;
      case 'router':
        return Icons.router;
      case 'lan':
        return Icons.lan;
      case 'signal_wifi_4_bar':
        return Icons.signal_wifi_4_bar;
      case 'network_wifi':
        return Icons.network_wifi;
      case 'public':
        return Icons.public;
      case 'dns':
        return Icons.dns;
      case 'vpn_lock':
        return Icons.vpn_lock;
      case 'cloud':
        return Icons.cloud;
      case 'cloud_download':
        return Icons.cloud_download;
      case 'cloud_upload':
        return Icons.cloud_upload;
      case 'cloud_sync':
        return Icons.cloud_sync;
      case 'wifi_off':
        return Icons.wifi_off;
      case 'signal_cellular_4_bar':
        return Icons.signal_cellular_4_bar;

      // Development icons
      case 'code':
        return Icons.code;
      case 'terminal':
        return Icons.terminal;
      case 'bug_report':
        return Icons.bug_report;
      case 'build':
        return Icons.build;
      case 'integration_instructions':
        return Icons.integration_instructions;
      case 'api':
        return Icons.api;
      case 'data_object':
        return Icons.data_object;
      case 'webhook':
        return Icons.webhook;
      case 'developer_mode':
        return Icons.developer_mode;
      case 'source':
        return Icons.source;
      case 'javascript':
        return Icons.code;
      case 'html':
        return Icons.code;
      case 'css':
        return Icons.palette;
      case 'database':
        return Icons.storage;
      case 'schema':
        return Icons.account_tree;
      case 'functions':
        return Icons.functions;
      case 'commit':
        return Icons.commit;
      case 'merge_type':
        return Icons.merge_type;
      case 'fork_right':
        return Icons.fork_right;
      case 'git_hub':
        return Icons.code;
      case 'version_control':
        return Icons.history;

      // Media icons
      case 'camera':
        return Icons.camera;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'videocam':
        return Icons.videocam;
      case 'photo_camera':
        return Icons.photo_camera;
      case 'image':
        return Icons.image;
      case 'video_library':
        return Icons.video_library;
      case 'photo_library':
        return Icons.photo_library;
      case 'play_circle':
        return Icons.play_circle;
      case 'movie':
        return Icons.movie;
      case 'music_note':
        return Icons.music_note;

      // Security icons
      case 'security':
        return Icons.security;
      case 'lock':
        return Icons.lock;
      case 'shield':
        return Icons.shield;
      case 'verified_user':
        return Icons.verified_user;
      case 'admin_panel_settings':
        return Icons.admin_panel_settings;
      case 'fingerprint':
        return Icons.fingerprint;
      case 'key':
        return Icons.key;
      case 'password':
        return Icons.password;
      case 'gpp_good':
        return Icons.gpp_good;
      case 'enhanced_encryption':
        return Icons.enhanced_encryption;

      // Utilities icons
      case 'apps':
        return Icons.apps;
      case 'dashboard':
        return Icons.dashboard;
      case 'widgets':
        return Icons.widgets;
      case 'extension':
        return Icons.extension;
      case 'tune':
        return Icons.tune;
      case 'build_circle':
        return Icons.build_circle;
      case 'handyman':
        return Icons.handyman;
      case 'construction':
        return Icons.construction;
      case 'precision_manufacturing':
        return Icons.precision_manufacturing;
      case 'engineering':
        return Icons.engineering;
      case 'calculate':
        return Icons.calculate;
      case 'timer':
        return Icons.timer;
      case 'alarm':
        return Icons.alarm;
      case 'schedule':
        return Icons.schedule;
      case 'calendar_today':
        return Icons.calendar_today;
      case 'event':
        return Icons.event;
      case 'task_alt':
        return Icons.task_alt;
      case 'checklist':
        return Icons.checklist;
      case 'list_alt':
        return Icons.list_alt;
      case 'note_add':
        return Icons.note_add;
      case 'sticky_note_2':
        return Icons.sticky_note_2;
      case 'bookmark':
        return Icons.bookmark;
      case 'star':
        return Icons.star;
      case 'favorite':
        return Icons.favorite;
      case 'thumb_up':
        return Icons.thumb_up;
      case 'trending_up':
        return Icons.trending_up;
      case 'analytics':
        return Icons.analytics;
      case 'insights':
        return Icons.insights;
      case 'assessment':
        return Icons.assessment;
      case 'bar_chart':
        return Icons.bar_chart;
      case 'pie_chart':
        return Icons.pie_chart;
      case 'show_chart':
        return Icons.show_chart;

      // AI/ML icons
      case 'psychology':
        return Icons.psychology;
      case 'smart_toy':
        return Icons.smart_toy;
      case 'auto_awesome':
        return Icons.auto_awesome;
      case 'lightbulb':
        return Icons.lightbulb;
      case 'science':
        return Icons.science;
      case 'biotech':
        return Icons.biotech;
      case 'model_training':
        return Icons.model_training;
      case 'neural_network':
        return Icons.account_tree;
      case 'data_exploration':
        return Icons.explore;
      case 'pattern_recognition':
        return Icons.pattern;
      case 'algorithm':
        return Icons.functions;
      case 'machine_learning':
        return Icons.psychology;
      case 'artificial_intelligence':
        return Icons.smart_toy;
      case 'deep_learning':
        return Icons.layers;
      case 'computer_vision':
        return Icons.visibility;
      case 'natural_language':
        return Icons.chat;
      case 'voice_recognition':
        return Icons.mic;
      case 'automation':
        return Icons.auto_mode;
      case 'robot':
        return Icons.smart_toy;
      case 'assistant':
        return Icons.assistant;
      case 'chat_bot':
        return Icons.chat_bubble;
      case 'prediction':
        return Icons.trending_up;
      case 'optimization':
        return Icons.tune;

      // Additional common icons
      case 'folder':
        return Icons.folder;
      default:
        return Icons.apps;
    }
  }

  void _editIcon() async {
    final result = await showDialog<dynamic>(
      context: context,
      builder: (context) => IconPickerDialog(
        selectedIcon: widget.module.iconName,
        selectedIconPath: widget.module.iconPath,
      ),
    );

    if (result != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        final editorService = AppEditorService();

        if (result is Map<String, dynamic>) {
          // Custom image selected
          final iconName = result['iconName'] ?? 'custom';
          final iconPath = result['iconPath'];
          await editorService.updateAppIcon(widget.module, iconName,
              iconPath: iconPath);
        } else {
          // Material icon selected - explicitly clear iconPath
          final selectedIcon = result as String;
          if (selectedIcon != widget.module.iconName ||
              widget.module.hasCustomIcon) {
            // Update icon and clear custom icon path
            await editorService.updateAppIcon(widget.module, selectedIcon,
                iconPath: null);
          } else {
            // No change, just return
            setState(() {
              _isLoading = false;
            });
            return;
          }
        }

        if (mounted) {
          Navigator.of(context).pop();

          // Call the callback to notify parent widget of icon update
          widget.onIconUpdated?.call();

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('App icon updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error updating icon: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _editSourceCode() {
    Navigator.of(context).pop();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AppSourceCodeEditor(module: widget.module),
      ),
    );
  }

  void _editConfiguration() {
    Navigator.of(context).pop();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AppConfigurationEditor(module: widget.module),
      ),
    );
  }

  void _browseAppFiles() {
    Navigator.of(context).pop();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AppFileBrowser(module: widget.module),
      ),
    );
  }

  void _deleteApp() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete App'),
        content: Text(
          'Are you sure you want to delete "${widget.module.title}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      setState(() {
        _isLoading = true;
      });

      try {
        final editorService = AppEditorService();
        await editorService.deleteApp(widget.module.id);

        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('App deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting app: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _exportApp() async {
    if (widget.module.appFolderPath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot export built-in apps'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      // Determine the correct app folder path
      String appFolderPath;
      if (widget.module.appFolderPath!.startsWith('/')) {
        // Absolute path (user apps)
        appFolderPath = widget.module.appFolderPath!;
      } else {
        // Relative path (built-in apps)
        final currentDir = Directory.current;
        String projectRoot = currentDir.path;
        if (projectRoot.contains('/build/')) {
          projectRoot = projectRoot.split('/build/').first;
        }
        appFolderPath = '$projectRoot/${widget.module.appFolderPath}';
      }

      final appFolder = Directory(appFolderPath);

      if (!await appFolder.exists()) {
        throw Exception('App folder not found: $appFolderPath');
      }

      // Let user choose export location
      final outputPath = await FilePicker.platform.saveFile(
        dialogTitle: 'Export ${widget.module.title}',
        fileName: '${widget.module.id}_v${widget.module.version}.zip',
        type: FileType.custom,
        allowedExtensions: ['zip'],
      );

      if (outputPath == null) {
        return; // User cancelled
      }

      // Create ZIP archive
      final archive = Archive();

      // Add all files from the app folder
      await _addDirectoryToArchive(archive, appFolder, '');

      // Encode the archive
      final zipData = ZipEncoder().encode(archive);
      if (zipData == null) {
        throw Exception('Failed to create ZIP archive');
      }

      // Write to file
      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(zipData);

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'App exported successfully to ${path.basename(outputPath)}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error exporting app: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _addDirectoryToArchive(
      Archive archive, Directory dir, String basePath) async {
    await for (final entity in dir.list()) {
      final relativePath = basePath.isEmpty
          ? path.basename(entity.path)
          : '$basePath/${path.basename(entity.path)}';

      if (entity is File) {
        final bytes = await entity.readAsBytes();
        final file = ArchiveFile(relativePath, bytes.length, bytes);
        archive.addFile(file);
      } else if (entity is Directory) {
        await _addDirectoryToArchive(archive, entity, relativePath);
      }
    }
  }

  Future<void> _updateRaspberryPiOnly() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Update the app configuration to reflect the new raspberryPiOnly setting
      final appEditorService = AppEditorService();

      // Create updated module with new raspberryPiOnly setting
      final updatedModule = widget.module.copyWith(
        raspberryPiOnly: _raspberryPiOnly,
      );

      // Update the app.json file with the new setting
      final updatedConfig = updatedModule.toJson();
      final configContent =
          const JsonEncoder.withIndent('  ').convert(updatedConfig);

      // Save the updated configuration
      await appEditorService.saveAppConfiguration(updatedModule, configContent);

      if (mounted) {
        // Call the callback to notify parent widget of app update
        widget.onAppUpdated?.call();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_raspberryPiOnly
                ? 'App is now Raspberry Pi only'
                : 'App is now available on all devices'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating app setting: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
