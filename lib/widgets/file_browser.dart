import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:jelly_pi/models/remote_file_item.dart';

/// File browser widget for displaying files and directories with modern selection support
class FileBrowser extends StatefulWidget {
  final List<RemoteFileItem> files;
  final String currentPath;
  final FileViewMode viewMode;
  final Function(String) onNavigate;
  final Function(FileOperation) onFileOperation;
  final Function(RemoteFileItem)? onFileOpen;
  final VoidCallback onUpload;
  final bool showHiddenFiles;
  final FileSortBy sortBy;
  final bool sortAscending;
  final Set<String> selectedFiles;
  final Function(String, {bool isCtrlPressed}) onFileSelectionToggle;
  final VoidCallback onClearSelection;
  final VoidCallback onSelectAll;

  const FileBrowser({
    super.key,
    required this.files,
    required this.currentPath,
    required this.viewMode,
    required this.onNavigate,
    required this.onFileOperation,
    this.onFileOpen,
    required this.onUpload,
    this.showHiddenFiles = false,
    this.sortBy = FileSortBy.name,
    this.sortAscending = true,
    this.selectedFiles = const {},
    required this.onFileSelectionToggle,
    required this.onClearSelection,
    required this.onSelectAll,
  });

  @override
  State<FileBrowser> createState() => _FileBrowserState();
}

class _FileBrowserState extends State<FileBrowser> {
  String? _lastSelectedFile;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Breadcrumb navigation
        _buildBreadcrumb(),
        const Divider(height: 1),
        // File list with drag selection support
        Expanded(
          child: widget.files.isEmpty
              ? _buildEmptyState()
              : GestureDetector(
                  onTap: () {
                    // Clear selection when clicking empty space
                    widget.onClearSelection();
                  },
                  child: widget.viewMode == FileViewMode.list
                      ? _buildListView()
                      : _buildGridView(),
                ),
        ),
      ],
    );
  }

  void _handleFileSelection(String fileName, int index) {
    final isCtrlPressed = HardwareKeyboard.instance.isControlPressed;
    final isShiftPressed = HardwareKeyboard.instance.isShiftPressed;

    if (isShiftPressed && _lastSelectedFile != null) {
      // Shift-click: select range
      final lastIndex =
          widget.files.indexWhere((f) => f.name == _lastSelectedFile);
      if (lastIndex != -1) {
        final startIndex = lastIndex < index ? lastIndex : index;
        final endIndex = lastIndex < index ? index : lastIndex;

        // Clear current selection first if not holding Ctrl
        if (!isCtrlPressed) {
          widget.onClearSelection();
        }

        // Select all files in range (force selection, don't toggle)
        for (int i = startIndex; i <= endIndex; i++) {
          if (i < widget.files.length) {
            final fileToSelect = widget.files[i].name;
            // Only select if not already selected
            if (!widget.selectedFiles.contains(fileToSelect)) {
              widget.onFileSelectionToggle(fileToSelect, isCtrlPressed: true);
            }
          }
        }
      }
    } else {
      // Normal click or Ctrl-click
      widget.onFileSelectionToggle(fileName, isCtrlPressed: isCtrlPressed);
    }

    // Update last selected file for shift-click functionality
    _lastSelectedFile = fileName;
  }

  Widget _buildBreadcrumb() {
    final pathParts =
        widget.currentPath.split('/').where((part) => part.isNotEmpty).toList();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Back button
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: pathParts.isNotEmpty
                ? () {
                    final parentPath = pathParts.length > 1
                        ? '/${pathParts.sublist(0, pathParts.length - 1).join('/')}'
                        : '/';
                    widget.onNavigate(parentPath);
                  }
                : null,
            tooltip: 'Back',
          ),
          // Home button
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () => widget.onNavigate('/'),
            tooltip: 'Home',
          ),
          // Path segments
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  for (int i = 0; i < pathParts.length; i++) ...[
                    const Icon(Icons.chevron_right, size: 16),
                    TextButton(
                      onPressed: () {
                        final path =
                            '/${pathParts.sublist(0, i + 1).join('/')}';
                        widget.onNavigate(path);
                      },
                      child: Text(pathParts[i]),
                    ),
                  ],
                ],
              ),
            ),
          ),
          // Current path info
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              '${widget.files.length} items',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open,
            size: 64,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'This folder is empty',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return Column(
      children: [
        // Column headers
        _buildListHeaders(),
        const Divider(height: 1),
        // File list
        Expanded(
          child: ListView.builder(
            itemCount: widget.files.length,
            itemBuilder: (context, index) {
              final file = widget.files[index];
              final isSelected = widget.selectedFiles.contains(file.name);

              return GestureDetector(
                onTap: () {
                  _handleFileSelection(file.name, index);
                },
                onDoubleTap: () {
                  // Double tap to open
                  if (file.isDirectory) {
                    widget.onNavigate(file.path);
                  } else if (widget.onFileOpen != null) {
                    widget.onFileOpen!(file);
                  }
                },
                child: _buildListItem(file, isSelected),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildGridView() {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 6, // More columns for better use of space
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.9, // Slightly more square
      ),
      itemCount: widget.files.length,
      itemBuilder: (context, index) {
        final file = widget.files[index];
        final isSelected = widget.selectedFiles.contains(file.name);

        return GestureDetector(
          onTap: () {
            _handleFileSelection(file.name, index);
          },
          onDoubleTap: () {
            // Double tap to open
            if (file.isDirectory) {
              widget.onNavigate(file.path);
            } else if (widget.onFileOpen != null) {
              widget.onFileOpen!(file);
            }
          },
          child: Card(
            elevation: isSelected ? 4 : 1,
            color: isSelected ? Colors.green.withValues(alpha: 0.3) : null,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    file.icon,
                    size: 32, // Smaller icons for better fit
                    color: file.color,
                  ),
                  const SizedBox(height: 6),
                  Text(
                    file.name,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                      color: isSelected ? Colors.green[800] : null,
                    ),
                  ),
                  if (!file.isDirectory) ...[
                    const SizedBox(height: 2),
                    Text(
                      file.formattedSize,
                      style: const TextStyle(
                        fontSize: 9,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildListHeaders() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Row(
        children: [
          const SizedBox(width: 40), // Space for icon
          const Expanded(
            child: Text(
              'Name',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(
            width: 80,
            child: Text(
              'Size',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.right,
            ),
          ),
          const SizedBox(width: 16),
          const SizedBox(
            width: 120,
            child: Text(
              'Modified',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.right,
            ),
          ),
          const SizedBox(width: 16),
          const SizedBox(
            width: 100,
            child: Text(
              'Permissions',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItem(RemoteFileItem file, bool isSelected) {
    return Container(
      color: isSelected ? Colors.green.withValues(alpha: 0.3) : null,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(
            file.icon,
            color: file.color,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              file.name,
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? Colors.green[800] : null,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(
            width: 80,
            child: Text(
              file.formattedSize,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 16),
          SizedBox(
            width: 120,
            child: Text(
              file.formattedDate,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 16),
          SizedBox(
            width: 100,
            child: Text(
              file.permissions,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.right,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
