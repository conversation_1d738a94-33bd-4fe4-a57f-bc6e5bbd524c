import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import 'package:archive/archive.dart';
import 'package:path_provider/path_provider.dart';
import '../services/app_module_manager.dart';

class AddAppDialog extends StatefulWidget {
  const AddAppDialog({super.key});

  @override
  State<AddAppDialog> createState() => _AddAppDialogState();
}

class _AddAppDialogState extends State<AddAppDialog> {
  bool _isLoading = false;
  String? _selectedPath;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.add_circle_outline, color: Color(0xFF6366F1)),
          SizedBox(width: 12),
          Text('Add App'),
        ],
      ),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Import a custom app into Jelly Pi',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            const Text(
              'Select an app folder or ZIP file:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[50],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.folder_open,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _selectedPath ?? 'No file selected',
                    style: TextStyle(
                      color: _selectedPath != null
                          ? Colors.black87
                          : Colors.grey[600],
                      fontWeight: _selectedPath != null
                          ? FontWeight.w500
                          : FontWeight.normal,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  ElevatedButton.icon(
                    onPressed: _isLoading ? null : _selectFile,
                    icon: const Icon(Icons.file_open),
                    label: const Text('Browse'),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Apps should be in a folder containing app.json and other required files.',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _isLoading || _selectedPath == null ? null : _installApp,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Install'),
        ),
      ],
    );
  }

  Future<void> _selectFile() async {
    try {
      // Allow both directories and ZIP files
      final result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
        dialogTitle: 'Select App Folder or ZIP File',
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedPath = result.files.first.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error selecting file: $e')),
        );
      }
    }
  }

  Future<void> _installApp() async {
    if (_selectedPath == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final file = File(_selectedPath!);
      final isZip = path.extension(_selectedPath!).toLowerCase() == '.zip';

      if (isZip) {
        await _installFromZip(file);
      } else {
        await _installFromFolder(Directory(_selectedPath!));
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('App installed successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh the app list
        AppModuleManager().refresh();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error installing app: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _installFromZip(File zipFile) async {
    try {
      // Read the ZIP file
      final bytes = await zipFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      // Find the app.json file to get the app ID
      ArchiveFile? appJsonFile;
      String? appId;

      for (final file in archive) {
        if (file.name.endsWith('app.json') && !file.name.contains('/')) {
          appJsonFile = file;
          break;
        }
      }

      if (appJsonFile == null) {
        throw Exception('No app.json found in the root of the ZIP file');
      }

      // Parse app.json to get the app ID
      final appJsonContent =
          String.fromCharCodes(appJsonFile.content as List<int>);
      final appJson = jsonDecode(appJsonContent) as Map<String, dynamic>;
      appId = appJson['id'] as String?;

      if (appId == null || appId.isEmpty) {
        throw Exception('Invalid app.json: missing or empty id field');
      }

      // Get the app data directory (hidden from user)
      final appDataDir = await getApplicationSupportDirectory();
      final userAppsDir =
          Directory(path.join(appDataDir.path, 'imported_apps'));
      if (!await userAppsDir.exists()) {
        await userAppsDir.create(recursive: true);
      }

      // Generate unique app ID if the original already exists
      final uniqueAppId = await _generateUniqueAppId(appId, appJson);
      final appDir = Directory(path.join(userAppsDir.path, uniqueAppId));

      await appDir.create(recursive: true);

      // Extract all files to the app directory
      for (final file in archive) {
        if (file.isFile) {
          final filePath = path.join(appDir.path, file.name);
          final fileDir = Directory(path.dirname(filePath));

          if (!await fileDir.exists()) {
            await fileDir.create(recursive: true);
          }

          final outputFile = File(filePath);
          await outputFile.writeAsBytes(file.content as List<int>);
        }
      }

      // Update the app.json to have the correct appFolderPath and unique ID
      final extractedAppJsonFile = File(path.join(appDir.path, 'app.json'));
      if (await extractedAppJsonFile.exists()) {
        final appJsonContent = await extractedAppJsonFile.readAsString();
        final updatedAppJson =
            jsonDecode(appJsonContent) as Map<String, dynamic>;
        updatedAppJson['appFolderPath'] = appDir.path;
        updatedAppJson['id'] = uniqueAppId; // Update with unique ID
        await extractedAppJsonFile.writeAsString(
          const JsonEncoder.withIndent('  ').convert(updatedAppJson),
        );
      }

      // Refresh the app manager to load the new app
      await AppModuleManager().refresh();
    } catch (e) {
      throw Exception('Failed to install ZIP app: $e');
    }
  }

  Future<void> _installFromFolder(Directory folder) async {
    try {
      // Check if app.json exists in the folder
      final appJsonFile = File(path.join(folder.path, 'app.json'));
      if (!await appJsonFile.exists()) {
        throw Exception('No app.json found in the selected folder');
      }

      // Parse app.json to get the app ID
      final appJsonContent = await appJsonFile.readAsString();
      final appJson = jsonDecode(appJsonContent) as Map<String, dynamic>;
      final appId = appJson['id'] as String?;

      if (appId == null || appId.isEmpty) {
        throw Exception('Invalid app.json: missing or empty id field');
      }

      // Get the app data directory (hidden from user)
      final appDataDir = await getApplicationSupportDirectory();
      final userAppsDir =
          Directory(path.join(appDataDir.path, 'imported_apps'));
      if (!await userAppsDir.exists()) {
        await userAppsDir.create(recursive: true);
      }

      // Generate unique app ID if the original already exists
      final uniqueAppId = await _generateUniqueAppId(appId, appJson);
      final appDir = Directory(path.join(userAppsDir.path, uniqueAppId));

      await appDir.create(recursive: true);

      // Copy all files from the source folder to the app directory
      await _copyDirectory(folder, appDir);

      // Update the app.json to have the correct appFolderPath and unique ID
      final copiedAppJsonFile = File(path.join(appDir.path, 'app.json'));
      if (await copiedAppJsonFile.exists()) {
        final appJsonContent = await copiedAppJsonFile.readAsString();
        final updatedAppJson =
            jsonDecode(appJsonContent) as Map<String, dynamic>;
        updatedAppJson['appFolderPath'] = appDir.path;
        updatedAppJson['id'] = uniqueAppId; // Update with unique ID
        await copiedAppJsonFile.writeAsString(
          const JsonEncoder.withIndent('  ').convert(updatedAppJson),
        );
      }

      // Refresh the app manager to load the new app
      await AppModuleManager().refresh();
    } catch (e) {
      throw Exception('Failed to install folder app: $e');
    }
  }

  /// Check if this is a duplicate app and handle accordingly
  Future<String> _generateUniqueAppId(
      String originalId, Map<String, dynamic> appJson) async {
    // Check if this exact app already exists (same ID + version + author)
    if (await _isExactAppDuplicate(appJson)) {
      throw Exception('This app (${appJson['title']}) is already installed. '
          'Cannot import the same app multiple times.');
    }

    // Check if just the ID conflicts (different app with same ID)
    if (!(await _isAppIdAvailable(originalId))) {
      // Generate unique ID by appending timestamp for different apps with same ID
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uniqueId = '${originalId}_$timestamp';

      // Update the app title to indicate it's a different app with same ID
      final originalTitle = appJson['title'] as String? ?? 'Unknown App';
      appJson['title'] = '$originalTitle (Copy)';

      return uniqueId;
    }

    return originalId;
  }

  /// Check if this exact app (same appUuid or same ID + version + author) already exists
  Future<bool> _isExactAppDuplicate(Map<String, dynamic> appJson) async {
    final appId = appJson['id'] as String?;
    final version = appJson['version'] as String?;
    final author = appJson['author'] as String?;
    final title = appJson['title'] as String?;

    // Check for appUuid in metadata (more reliable for newer apps)
    final metadata = appJson['metadata'] as Map<String, dynamic>?;
    final appUuid = metadata?['appUuid'] as String?;

    if (appId == null) return false;

    // Check in loaded modules
    final moduleManager = AppModuleManager();

    // First try to match by appUuid if available (most reliable)
    if (appUuid != null) {
      final existingModuleByUuid = moduleManager.orderedModules.where((m) {
        final existingMetadata = m.metadata;
        final existingUuid = existingMetadata['appUuid'] as String?;
        return existingUuid == appUuid;
      }).firstOrNull;

      if (existingModuleByUuid != null) {
        return true;
      }
    }

    // Fallback to matching by ID + version + author + title
    final existingModule = moduleManager.orderedModules
        .where((m) =>
            m.id == appId &&
            m.version == version &&
            m.author == author &&
            m.title == title)
        .firstOrNull;

    if (existingModule != null) {
      return true;
    }

    // Check in user apps directory
    final appDataDir = await getApplicationSupportDirectory();
    final userAppsDir = Directory(path.join(appDataDir.path, 'imported_apps'));

    if (await userAppsDir.exists()) {
      await for (final entity in userAppsDir.list()) {
        if (entity is Directory) {
          final appJsonFile = File(path.join(entity.path, 'app.json'));
          if (await appJsonFile.exists()) {
            try {
              final existingAppJsonContent = await appJsonFile.readAsString();
              final existingAppJson =
                  jsonDecode(existingAppJsonContent) as Map<String, dynamic>;

              // First try to match by appUuid if available
              if (appUuid != null) {
                final existingMetadata =
                    existingAppJson['metadata'] as Map<String, dynamic>?;
                final existingUuid = existingMetadata?['appUuid'] as String?;
                if (existingUuid == appUuid) {
                  return true;
                }
              }

              // Fallback to matching by ID + version + author + title
              if (existingAppJson['id'] == appId &&
                  existingAppJson['version'] == version &&
                  existingAppJson['author'] == author &&
                  existingAppJson['title'] == title) {
                return true;
              }
            } catch (e) {
              // Ignore errors reading individual app.json files
            }
          }
        }
      }
    }

    return false;
  }

  /// Check if an app ID is available (not already used)
  Future<bool> _isAppIdAvailable(String appId) async {
    // Check in user apps directory
    final appDataDir = await getApplicationSupportDirectory();
    final userAppsDir = Directory(path.join(appDataDir.path, 'imported_apps'));
    final appDir = Directory(path.join(userAppsDir.path, appId));

    if (await appDir.exists()) {
      return false;
    }

    // Check in loaded modules
    final moduleManager = AppModuleManager();
    final existingModule =
        moduleManager.orderedModules.where((m) => m.id == appId).firstOrNull;

    if (existingModule != null) {
      return false;
    }

    return true;
  }

  Future<void> _copyDirectory(Directory source, Directory destination) async {
    await for (final entity in source.list(recursive: false)) {
      if (entity is File) {
        final fileName = path.basename(entity.path);
        final destFile = File(path.join(destination.path, fileName));
        await entity.copy(destFile.path);
      } else if (entity is Directory) {
        final dirName = path.basename(entity.path);
        final destDir = Directory(path.join(destination.path, dirName));
        await destDir.create(recursive: true);
        await _copyDirectory(entity, destDir);
      }
    }
  }
}
