import 'package:flutter/material.dart';

/// Progress notification widget that appears in the bottom left corner
class ProgressNotification extends StatefulWidget {
  final String title;
  final String? subtitle;
  final double? progress; // 0.0 to 1.0, null for indeterminate
  final VoidCallback? onCancel;
  final bool showCancel;
  final Duration? duration;
  final VoidCallback? onDismiss;

  const ProgressNotification({
    super.key,
    required this.title,
    this.subtitle,
    this.progress,
    this.onCancel,
    this.showCancel = false,
    this.duration,
    this.onDismiss,
  });

  @override
  State<ProgressNotification> createState() => _ProgressNotificationState();
}

class _ProgressNotificationState extends State<ProgressNotification>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();

    // Auto-dismiss if duration is provided
    if (widget.duration != null) {
      Future.delayed(widget.duration!, () {
        if (mounted) {
          _dismiss();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _dismiss() {
    _animationController.reverse().then((_) {
      if (mounted) {
        widget.onDismiss?.call();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          constraints: const BoxConstraints(minWidth: 300, maxWidth: 400),
          decoration: BoxDecoration(
            color:
                const Color(0xFFFFFFFF), // White background to match app cards
            borderRadius: BorderRadius.circular(8), // Match app's card radius
            boxShadow: [
              BoxShadow(
                color: Colors.black
                    .withValues(alpha: 0.1), // Subtle shadow like app cards
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: const Color(0xFFD1D5DB), // Match app's outline color
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: const Color(
                                0xFF1F2937), // Match app's onSurface color
                            fontSize: 14,
                            fontWeight:
                                FontWeight.w600, // Match app's title weight
                          ),
                    ),
                  ),
                  if (widget.showCancel) ...[
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: widget.onCancel ?? _dismiss,
                      child: const Icon(
                        Icons.close,
                        color: Color(0xFF6B7280), // Muted gray for close button
                        size: 18,
                      ),
                    ),
                  ],
                ],
              ),
              if (widget.subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  widget.subtitle!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color:
                            const Color(0xFF6B7280), // Muted gray for subtitle
                        fontSize: 12,
                      ),
                ),
              ],
              const SizedBox(height: 8),
              // Progress bar
              Container(
                height: 4,
                decoration: BoxDecoration(
                  color: const Color(0xFFE5E7EB), // Light gray background
                  borderRadius: BorderRadius.circular(2),
                ),
                child: widget.progress != null
                    ? FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: widget.progress!.clamp(0.0, 1.0),
                        child: Container(
                          decoration: BoxDecoration(
                            color: const Color(
                                0xFF6366F1), // App's primary purple color
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      )
                    : LinearProgressIndicator(
                        backgroundColor: Colors.transparent,
                        valueColor: const AlwaysStoppedAnimation<Color>(
                            Color(0xFF6366F1)), // App's primary color
                        minHeight: 4,
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Service to manage progress notifications
class ProgressNotificationService {
  static final ProgressNotificationService _instance =
      ProgressNotificationService._internal();
  factory ProgressNotificationService() => _instance;
  ProgressNotificationService._internal();

  OverlayEntry? _currentOverlay;
  BuildContext? _context;

  void initialize(BuildContext context) {
    _context = context;
  }

  void show({
    required String title,
    String? subtitle,
    double? progress,
    VoidCallback? onCancel,
    bool showCancel = false,
    Duration? duration,
  }) {
    if (_context == null) return;

    // Remove existing notification
    hide();

    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 0,
        left: 0,
        child: ProgressNotification(
          title: title,
          subtitle: subtitle,
          progress: progress,
          onCancel: onCancel,
          showCancel: showCancel,
          duration: duration,
          onDismiss: hide,
        ),
      ),
    );

    Overlay.of(_context!).insert(_currentOverlay!);
  }

  void update({
    String? title,
    String? subtitle,
    double? progress,
  }) {
    if (_currentOverlay == null) return;

    // For now, we'll just replace the notification
    // In a more sophisticated implementation, we'd update the existing one
    hide();
    show(
      title: title ?? 'Processing...',
      subtitle: subtitle,
      progress: progress,
    );
  }

  void hide() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }

  void showSuccess(String message, {Duration? duration}) {
    show(
      title: message,
      duration: duration ?? const Duration(seconds: 3),
    );
  }

  void showError(String message, {Duration? duration}) {
    show(
      title: message,
      duration: duration ?? const Duration(seconds: 5),
      showCancel: true,
    );
  }
}
