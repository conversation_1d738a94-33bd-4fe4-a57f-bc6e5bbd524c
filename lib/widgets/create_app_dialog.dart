import 'package:flutter/material.dart';
import 'dart:io';
import '../services/app_creator_service.dart';
import '../widgets/icon_picker_dialog.dart';

class CreateAppDialog extends StatefulWidget {
  const CreateAppDialog({super.key});

  @override
  State<CreateAppDialog> createState() => _CreateAppDialogState();
}

class _CreateAppDialogState extends State<CreateAppDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _authorController = TextEditingController();
  final _versionController = TextEditingController(text: '1.0.0');

  String _selectedCategory = 'utilities';
  String _selectedIcon = 'apps';
  String? _selectedIconPath;
  String _selectedTemplate = 'basic';
  bool _isLoading = false;
  bool _raspberryPiOnly = false;

  final List<String> _categories = [
    'system',
    'utilities',
    'network',
    'development',
    'media',
    'security',
    'custom'
  ];

  final List<Map<String, String>> _templates = [
    {
      'id': 'basic',
      'name': 'Basic App',
      'description': 'Simple app with basic UI components',
      'details':
          'Perfect for: Simple utilities, calculators, converters, basic tools',
      'features': 'Basic UI, settings, simple interactions'
    },
    {
      'id': 'system_tool',
      'name': 'System Tool',
      'description': 'App that interacts with system resources',
      'details':
          'Perfect for: System monitors, process managers, hardware controllers',
      'features':
          'System access, device connection required, hardware interaction'
    },
    {
      'id': 'network_tool',
      'name': 'Network Tool',
      'description': 'App for network operations and diagnostics',
      'details':
          'Perfect for: Network scanners, ping tools, bandwidth monitors',
      'features': 'Network access, connection testing, remote operations'
    },
    {
      'id': 'data_viewer',
      'name': 'Data Viewer',
      'description': 'App for displaying and analyzing data',
      'details': 'Perfect for: Log viewers, chart displays, data dashboards',
      'features': 'Data visualization, charts, tables, export functionality'
    },
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _authorController.dispose();
    _versionController.dispose();
    super.dispose();
  }

  String _formatCategoryName(String category) {
    return category
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.create, color: Color(0xFF6366F1)),
          SizedBox(width: 12),
          Text('Create New App'),
        ],
      ),
      content: SizedBox(
        width: 500,
        height: 700,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Create a new app from a template',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 24),

                // App Name
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'App Name *',
                    hintText: 'My Awesome App',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.title),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'App name is required';
                    }
                    if (value.trim().length < 3) {
                      return 'App name must be at least 3 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description *',
                    hintText: 'Brief description of your app',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.description),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Description is required';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Author
                TextFormField(
                  controller: _authorController,
                  decoration: const InputDecoration(
                    labelText: 'Author',
                    hintText: 'Your name or organization',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.person),
                  ),
                ),
                const SizedBox(height: 16),

                // Version
                TextFormField(
                  controller: _versionController,
                  decoration: const InputDecoration(
                    labelText: 'Version',
                    hintText: '1.0.0',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.tag),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Version is required';
                    }
                    // Basic version format validation
                    final versionRegex = RegExp(r'^\d+\.\d+\.\d+$');
                    if (!versionRegex.hasMatch(value.trim())) {
                      return 'Version must be in format x.y.z (e.g., 1.0.0)';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Category
                DropdownButtonFormField<String>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.category),
                  ),
                  items: _categories.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(_formatCategoryName(category)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCategory = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Icon
                InkWell(
                  onTap: _showIconPicker,
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Icon',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.image),
                    ),
                    child: Row(
                      children: [
                        if (_selectedIconPath != null) ...[
                          // Show custom image
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: Image.file(
                                File(_selectedIconPath!),
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(
                                    Icons.error,
                                    size: 16,
                                    color: Colors.red,
                                  );
                                },
                              ),
                            ),
                          ),
                        ] else ...[
                          // Show material icon
                          Icon(
                            _getIconData(_selectedIcon),
                            size: 20,
                            color: const Color(0xFF6366F1),
                          ),
                        ],
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _selectedIconPath != null
                                ? 'Custom Image: ${_selectedIconPath!.split('/').last}'
                                : _selectedIcon
                                    .replaceAll('_', ' ')
                                    .toUpperCase(),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Raspberry Pi Only checkbox
                Card(
                  elevation: 1,
                  color: const Color(0xFFFAFBFC),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: Colors.grey.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: CheckboxListTile(
                    contentPadding: const EdgeInsets.all(16),
                    title: const Row(
                      children: [
                        Icon(Icons.memory, color: Color(0xFF6366F1), size: 20),
                        SizedBox(width: 8),
                        Text(
                          'Raspberry Pi Only',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: Color(0xFF1F2937),
                          ),
                        ),
                      ],
                    ),
                    subtitle: const Text(
                      'This app will only be visible when connected to a Raspberry Pi device',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                    value: _raspberryPiOnly,
                    activeColor: const Color(0xFF6366F1),
                    onChanged: (value) {
                      setState(() {
                        _raspberryPiOnly = value ?? false;
                      });
                    },
                  ),
                ),
                const SizedBox(height: 16),

                // Template
                const Text(
                  'Template',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                ..._templates.map((template) {
                  final isSelected = template['id'] == _selectedTemplate;
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Card(
                      elevation: isSelected ? 3 : 1,
                      color: const Color(0xFFFAFBFC),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: isSelected
                              ? const Color(0xFF6366F1)
                              : Colors.grey.withValues(alpha: 0.2),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: RadioListTile<String>(
                        contentPadding: const EdgeInsets.all(16),
                        title: Text(
                          template['name']!,
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                            color: isSelected
                                ? const Color(0xFF6366F1)
                                : const Color(0xFF1F2937),
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 4),
                            Text(
                              template['description']!,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF6B7280),
                              ),
                            ),
                            if (isSelected) ...[
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF6366F1)
                                      .withValues(alpha: 0.05),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: const Color(0xFF6366F1)
                                        .withValues(alpha: 0.2),
                                  ),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      template['details']!,
                                      style: const TextStyle(
                                        fontSize: 13,
                                        color: Color(0xFF4B5563),
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                    const SizedBox(height: 6),
                                    Text(
                                      'Features: ${template['features']!}',
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Color(0xFF6B7280),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                        value: template['id']!,
                        groupValue: _selectedTemplate,
                        activeColor: const Color(0xFF6366F1),
                        onChanged: (value) {
                          setState(() {
                            _selectedTemplate = value!;
                          });
                        },
                      ),
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _isLoading ? null : _createApp,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Create'),
        ),
      ],
    );
  }

  void _showIconPicker() async {
    final result = await showDialog<dynamic>(
      context: context,
      builder: (context) => IconPickerDialog(
        selectedIcon: _selectedIcon,
        selectedIconPath: _selectedIconPath,
      ),
    );

    if (result != null) {
      setState(() {
        if (result is Map<String, dynamic>) {
          // Custom image selected
          _selectedIcon = result['iconName'] ?? 'custom';
          _selectedIconPath = result['iconPath'];
        } else {
          // Material icon selected
          _selectedIcon = result as String;
          _selectedIconPath = null;
        }
      });
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      // System icons
      case 'apps':
        return Icons.apps;
      case 'settings':
        return Icons.settings;
      case 'monitor':
        return Icons.monitor;
      case 'memory':
        return Icons.memory;
      case 'speed':
        return Icons.speed;
      case 'power_settings_new':
        return Icons.power_settings_new;
      case 'system_update':
        return Icons.system_update;
      case 'hardware':
        return Icons.hardware;
      case 'developer_board':
        return Icons.developer_board;
      case 'sensors':
        return Icons.sensors;
      case 'thermostat':
        return Icons.thermostat;
      case 'computer':
        return Icons.computer;
      case 'desktop_windows':
        return Icons.desktop_windows;
      case 'laptop':
        return Icons.laptop;
      case 'tablet':
        return Icons.tablet;
      case 'smartphone':
        return Icons.smartphone;
      case 'device_hub':
        return Icons.device_hub;
      case 'devices':
        return Icons.devices;
      case 'battery_full':
        return Icons.battery_full;
      case 'battery_charging_full':
        return Icons.battery_charging_full;
      case 'power':
        return Icons.power;
      case 'electrical_services':
        return Icons.electrical_services;
      case 'cpu':
        return Icons.memory;
      case 'storage':
        return Icons.storage;
      case 'sd_storage':
        return Icons.sd_storage;
      case 'usb':
        return Icons.usb;

      // Network icons
      case 'network_check':
        return Icons.network_check;
      case 'wifi':
        return Icons.wifi;
      case 'router':
        return Icons.router;
      case 'lan':
        return Icons.lan;
      case 'signal_wifi_4_bar':
        return Icons.signal_wifi_4_bar;
      case 'network_wifi':
        return Icons.network_wifi;
      case 'public':
        return Icons.public;
      case 'dns':
        return Icons.dns;
      case 'vpn_lock':
        return Icons.vpn_lock;
      case 'cloud':
        return Icons.cloud;
      case 'cloud_download':
        return Icons.cloud_download;
      case 'cloud_upload':
        return Icons.cloud_upload;
      case 'cloud_sync':
        return Icons.cloud_sync;
      case 'wifi_off':
        return Icons.wifi_off;
      case 'signal_cellular_4_bar':
        return Icons.signal_cellular_4_bar;

      // Development icons
      case 'code':
        return Icons.code;
      case 'terminal':
        return Icons.terminal;
      case 'bug_report':
        return Icons.bug_report;
      case 'build':
        return Icons.build;
      case 'integration_instructions':
        return Icons.integration_instructions;
      case 'api':
        return Icons.api;
      case 'data_object':
        return Icons.data_object;
      case 'webhook':
        return Icons.webhook;
      case 'developer_mode':
        return Icons.developer_mode;
      case 'source':
        return Icons.source;
      case 'javascript':
        return Icons.code;
      case 'html':
        return Icons.code;
      case 'css':
        return Icons.palette;
      case 'database':
        return Icons.storage;
      case 'schema':
        return Icons.account_tree;
      case 'functions':
        return Icons.functions;
      case 'commit':
        return Icons.commit;
      case 'merge_type':
        return Icons.merge_type;
      case 'fork_right':
        return Icons.fork_right;
      case 'git_hub':
        return Icons.code;
      case 'version_control':
        return Icons.history;

      // Files icons
      case 'folder':
        return Icons.folder;
      case 'folder_open':
        return Icons.folder_open;
      case 'file_copy':
        return Icons.file_copy;
      case 'insert_drive_file':
        return Icons.insert_drive_file;
      case 'description':
        return Icons.description;
      case 'article':
        return Icons.article;
      case 'archive':
        return Icons.archive;
      case 'backup':
        return Icons.backup;

      // Media icons
      case 'camera':
        return Icons.camera;
      case 'camera_alt':
        return Icons.camera_alt;
      case 'videocam':
        return Icons.videocam;
      case 'photo_camera':
        return Icons.photo_camera;
      case 'image':
        return Icons.image;
      case 'video_library':
        return Icons.video_library;
      case 'photo_library':
        return Icons.photo_library;
      case 'play_circle':
        return Icons.play_circle;
      case 'movie':
        return Icons.movie;
      case 'music_note':
        return Icons.music_note;

      // Security icons
      case 'security':
        return Icons.security;
      case 'lock':
        return Icons.lock;
      case 'shield':
        return Icons.shield;
      case 'verified_user':
        return Icons.verified_user;
      case 'admin_panel_settings':
        return Icons.admin_panel_settings;
      case 'fingerprint':
        return Icons.fingerprint;
      case 'key':
        return Icons.key;
      case 'password':
        return Icons.password;
      case 'gpp_good':
        return Icons.gpp_good;
      case 'enhanced_encryption':
        return Icons.enhanced_encryption;

      // Utilities icons
      case 'dashboard':
        return Icons.dashboard;
      case 'widgets':
        return Icons.widgets;
      case 'extension':
        return Icons.extension;
      case 'tune':
        return Icons.tune;
      case 'build_circle':
        return Icons.build_circle;
      case 'handyman':
        return Icons.handyman;
      case 'construction':
        return Icons.construction;
      case 'precision_manufacturing':
        return Icons.precision_manufacturing;
      case 'engineering':
        return Icons.engineering;
      case 'calculate':
        return Icons.calculate;
      case 'timer':
        return Icons.timer;
      case 'alarm':
        return Icons.alarm;
      case 'schedule':
        return Icons.schedule;
      case 'calendar_today':
        return Icons.calendar_today;
      case 'event':
        return Icons.event;
      case 'task_alt':
        return Icons.task_alt;
      case 'checklist':
        return Icons.checklist;
      case 'list_alt':
        return Icons.list_alt;
      case 'note_add':
        return Icons.note_add;
      case 'sticky_note_2':
        return Icons.sticky_note_2;
      case 'bookmark':
        return Icons.bookmark;
      case 'star':
        return Icons.star;
      case 'favorite':
        return Icons.favorite;
      case 'thumb_up':
        return Icons.thumb_up;
      case 'trending_up':
        return Icons.trending_up;
      case 'analytics':
        return Icons.analytics;
      case 'insights':
        return Icons.insights;
      case 'assessment':
        return Icons.assessment;
      case 'bar_chart':
        return Icons.bar_chart;
      case 'pie_chart':
        return Icons.pie_chart;
      case 'show_chart':
        return Icons.show_chart;

      // AI/ML icons
      case 'psychology':
        return Icons.psychology;
      case 'smart_toy':
        return Icons.smart_toy;
      case 'auto_awesome':
        return Icons.auto_awesome;
      case 'lightbulb':
        return Icons.lightbulb;
      case 'science':
        return Icons.science;
      case 'biotech':
        return Icons.biotech;
      case 'model_training':
        return Icons.model_training;
      case 'neural_network':
        return Icons.account_tree;
      case 'data_exploration':
        return Icons.explore;
      case 'pattern_recognition':
        return Icons.pattern;
      case 'algorithm':
        return Icons.functions;
      case 'machine_learning':
        return Icons.psychology;
      case 'artificial_intelligence':
        return Icons.smart_toy;
      case 'deep_learning':
        return Icons.layers;
      case 'computer_vision':
        return Icons.visibility;
      case 'natural_language':
        return Icons.chat;
      case 'voice_recognition':
        return Icons.mic;
      case 'automation':
        return Icons.auto_mode;
      case 'robot':
        return Icons.smart_toy;
      case 'assistant':
        return Icons.assistant;
      case 'chat_bot':
        return Icons.chat_bubble;
      case 'prediction':
        return Icons.trending_up;
      case 'optimization':
        return Icons.tune;

      default:
        return Icons.apps;
    }
  }

  Future<void> _createApp() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final appCreator = AppCreatorService();
      await appCreator.createApp(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        author: _authorController.text.trim().isEmpty
            ? 'Unknown'
            : _authorController.text.trim(),
        version: _versionController.text.trim(),
        category: _selectedCategory,
        icon: _selectedIcon,
        template: _selectedTemplate,
        iconPath: _selectedIconPath,
        raspberryPiOnly: _raspberryPiOnly,
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('App created successfully! Check the Apps page.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating app: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
