import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/app_state.dart';

class AddDeviceDialog extends StatefulWidget {
  final Device? device; // For editing existing device

  const AddDeviceDialog({super.key, this.device});

  @override
  State<AddDeviceDialog> createState() => _AddDeviceDialogState();
}

class _AddDeviceDialogState extends State<AddDeviceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ipController = TextEditingController();
  final _portController = TextEditingController();
  final _macController = TextEditingController();
  final _vendorController = TextEditingController();

  bool _connectOnLaunch = false;
  bool _isDefaultDevice = false;

  bool get isEditing => widget.device != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _nameController.text = widget.device!.name;
      _ipController.text = widget.device!.ip;
      _portController.text = widget.device!.port.toString();
      _macController.text = widget.device!.mac;
      _vendorController.text = widget.device!.vendor;
      _connectOnLaunch = widget.device!.connectOnLaunch;
      _isDefaultDevice = widget.device!.isDefaultDevice;
    } else {
      _portController.text = '22'; // Default SSH port
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ipController.dispose();
    _portController.dispose();
    _macController.dispose();
    _vendorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(isEditing ? 'Edit Device' : 'Add Device'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Device Name
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Device Name',
                  hintText: 'e.g., Raspberry Pi 4',
                  prefixIcon: Icon(Icons.label),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a device name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // IP Address
              TextFormField(
                controller: _ipController,
                decoration: const InputDecoration(
                  labelText: 'IP Address',
                  hintText: 'e.g., *************',
                  prefixIcon: Icon(Icons.language),
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an IP address';
                  }
                  if (!_isValidIP(value.trim())) {
                    return 'Please enter a valid IP address';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Port
              TextFormField(
                controller: _portController,
                decoration: const InputDecoration(
                  labelText: 'Port',
                  hintText: 'e.g., 22 (default SSH port)',
                  prefixIcon: Icon(Icons.settings_ethernet),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a port number';
                  }
                  final port = int.tryParse(value.trim());
                  if (port == null || port < 1 || port > 65535) {
                    return 'Please enter a valid port number (1-65535)';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              if (isEditing) ...[
                // Connect on Launch
                CheckboxListTile(
                  title: const Text('Connect on launch'),
                  subtitle: const Text('Automatically connect when app starts'),
                  value: _connectOnLaunch,
                  onChanged: (value) {
                    setState(() {
                      _connectOnLaunch = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                ),

                // Set as Default Device
                CheckboxListTile(
                  title: const Text('Set as default device'),
                  subtitle: const Text(
                      'Use this device as the default for terminal and apps'),
                  value: _isDefaultDevice,
                  onChanged: (value) {
                    setState(() {
                      _isDefaultDevice = value ?? false;
                    });
                  },
                  controlAffinity: ListTileControlAffinity.leading,
                ),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: _saveDevice,
          child: Text(isEditing ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  bool _isValidIP(String ip) {
    final parts = ip.split('.');
    if (parts.length != 4) return false;

    for (final part in parts) {
      final num = int.tryParse(part);
      if (num == null || num < 0 || num > 255) return false;
    }
    return true;
  }

  void _saveDevice() {
    if (!_formKey.currentState!.validate()) return;

    final appState = context.read<AppState>();
    final name = _nameController.text.trim();
    final ip = _ipController.text.trim();
    final port = int.parse(_portController.text.trim());

    if (isEditing) {
      // Update existing device
      final updatedDevice = widget.device!.copyWith(
        name: name,
        port: port,
        connectOnLaunch: _connectOnLaunch,
        isDefaultDevice: _isDefaultDevice,
      );
      appState.updateDevice(updatedDevice);

      // If setting as default, also call setDefaultDevice to clear other defaults
      if (_isDefaultDevice && !widget.device!.isDefaultDevice) {
        appState.setDefaultDevice(widget.device!.id);
      }
    } else {
      // Add new device with default MAC and vendor (will be detected automatically)
      final device = Device(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        ip: ip,
        port: port,
        mac: 'Unknown', // Will be detected automatically
        vendor: 'Unknown', // Will be detected automatically
        name: name,
        connectOnLaunch: _connectOnLaunch,
        isDefaultDevice: _isDefaultDevice,
        operatingSystem: 'Unknown',
      );
      appState.addDevice(device);

      // If setting as default, call setDefaultDevice to clear other defaults
      if (_isDefaultDevice) {
        appState.setDefaultDevice(device.id);
      }
    }

    Navigator.of(context).pop();
  }
}
