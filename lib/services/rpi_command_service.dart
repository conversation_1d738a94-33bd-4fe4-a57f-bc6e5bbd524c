import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import '../services/app_state.dart';
import '../widgets/progress_notification.dart';

class RpiCommandService {
  final AppState appState;

  RpiCommandService(this.appState);

  /// Execute a command with progress tracking
  Future<CommandResult> executeCommand(
    String deviceId,
    String command, {
    String? progressTitle,
    String? progressSubtitle,
    bool showProgress = true,
    Duration timeout = const Duration(minutes: 5),
  }) async {
    if (showProgress && progressTitle != null) {
      ProgressNotificationService().show(
        title: progressTitle,
        subtitle: progressSubtitle ?? 'Executing command...',
      );
    }

    try {
      final connection = appState.getSSHConnection(deviceId);
      if (connection?.client == null) {
        throw Exception('No SSH connection available');
      }

      final session = await connection!.client!.execute(command);
      final stdout = await utf8.decoder.bind(session.stdout).join();
      final stderr = await utf8.decoder.bind(session.stderr).join();
      final exitCode = session.exitCode ?? 0;

      final result = CommandResult(
        command: command,
        stdout: stdout,
        stderr: stderr,
        exitCode: exitCode,
        success: exitCode == 0,
      );

      if (showProgress) {
        if (result.success) {
          ProgressNotificationService().update(
            title: progressTitle ?? 'Command completed',
            subtitle: 'Success',
            progress: 1.0,
          );
          Future.delayed(const Duration(seconds: 2), () {
            ProgressNotificationService().hide();
          });
        } else {
          ProgressNotificationService().showError(
            'Command failed: ${result.stderr.isNotEmpty ? result.stderr : 'Unknown error'}',
          );
        }
      }

      return result;
    } catch (e) {
      if (showProgress) {
        ProgressNotificationService().showError('Error executing command: $e');
      }
      rethrow;
    }
  }

  /// Execute a long-running command with real-time output
  Future<CommandResult> executeLongRunningCommand(
    String deviceId,
    String command, {
    String? progressTitle,
    Function(String)? onOutput,
    Duration timeout = const Duration(minutes: 10),
  }) async {
    if (progressTitle != null) {
      ProgressNotificationService().show(
        title: progressTitle,
        subtitle: 'Starting...',
      );
    }

    try {
      final connection = appState.getSSHConnection(deviceId);
      if (connection?.client == null) {
        throw Exception('No SSH connection available');
      }

      final session = await connection!.client!.execute(command);
      final stdoutCompleter = Completer<String>();
      final stderrCompleter = Completer<String>();
      final stdoutBuffer = StringBuffer();
      final stderrBuffer = StringBuffer();

      // Listen to stdout
      session.stdout.cast<List<int>>().transform(utf8.decoder).listen(
        (data) {
          stdoutBuffer.write(data);
          onOutput?.call(data);
          if (progressTitle != null) {
            ProgressNotificationService().update(
              title: progressTitle,
              subtitle: 'Processing...',
            );
          }
        },
        onDone: () => stdoutCompleter.complete(stdoutBuffer.toString()),
        onError: (error) => stdoutCompleter.completeError(error),
      );

      // Listen to stderr
      session.stderr.cast<List<int>>().transform(utf8.decoder).listen(
        (data) {
          stderrBuffer.write(data);
          onOutput?.call(data);
        },
        onDone: () => stderrCompleter.complete(stderrBuffer.toString()),
        onError: (error) => stderrCompleter.completeError(error),
      );

      // Wait for completion
      final stdout = await stdoutCompleter.future.timeout(timeout);
      final stderr = await stderrCompleter.future.timeout(timeout);
      final exitCode = session.exitCode ?? 0;

      final result = CommandResult(
        command: command,
        stdout: stdout,
        stderr: stderr,
        exitCode: exitCode,
        success: exitCode == 0,
      );

      if (progressTitle != null) {
        if (result.success) {
          ProgressNotificationService().update(
            title: 'Command completed',
            subtitle: 'Success',
            progress: 1.0,
          );
          Future.delayed(const Duration(seconds: 2), () {
            ProgressNotificationService().hide();
          });
        } else {
          ProgressNotificationService().showError(
            'Command failed: ${result.stderr.isNotEmpty ? result.stderr : 'Unknown error'}',
          );
        }
      }

      return result;
    } catch (e) {
      if (progressTitle != null) {
        ProgressNotificationService().showError('Error executing command: $e');
      }
      rethrow;
    }
  }

  /// System update with progress tracking
  Future<CommandResult> updateSystem(String deviceId) async {
    return executeLongRunningCommand(
      deviceId,
      'sudo apt update && sudo apt upgrade -y',
      progressTitle: 'System Update',
      timeout: const Duration(minutes: 30),
    );
  }

  /// Firmware update
  Future<CommandResult> updateFirmware(String deviceId) async {
    return executeLongRunningCommand(
      deviceId,
      'sudo rpi-update',
      progressTitle: 'Firmware Update',
      timeout: const Duration(minutes: 15),
    );
  }

  /// Reboot system
  Future<CommandResult> rebootSystem(String deviceId) async {
    return executeCommand(
      deviceId,
      'sudo reboot',
      progressTitle: 'Rebooting System',
      progressSubtitle: 'System will restart...',
    );
  }

  /// Shutdown system
  Future<CommandResult> shutdownSystem(String deviceId) async {
    return executeCommand(
      deviceId,
      'sudo shutdown -h now',
      progressTitle: 'Shutting Down',
      progressSubtitle: 'System will power off...',
    );
  }

  /// Get system temperature
  Future<double?> getSystemTemperature(String deviceId) async {
    try {
      final result = await executeCommand(
        deviceId,
        'vcgencmd measure_temp',
        showProgress: false,
      );

      if (result.success) {
        final tempMatch = RegExp(r'temp=(\d+\.?\d*)').firstMatch(result.stdout);
        if (tempMatch != null) {
          return double.tryParse(tempMatch.group(1)!);
        }
      }
    } catch (e) {
      debugPrint('Error getting temperature: $e');
    }
    return null;
  }

  /// Get system information
  Future<Map<String, dynamic>> getSystemInfo(String deviceId) async {
    final info = <String, dynamic>{};

    try {
      // Get multiple system info commands in parallel
      final futures = await Future.wait([
        executeCommand(deviceId, 'uname -a', showProgress: false),
        executeCommand(
            deviceId, 'cat /proc/cpuinfo | grep "model name" | head -1',
            showProgress: false),
        executeCommand(deviceId, 'free -h | grep Mem', showProgress: false),
        executeCommand(deviceId, 'uptime -p', showProgress: false),
        executeCommand(
            deviceId, 'vcgencmd measure_temp 2>/dev/null || echo "temp=N/A"',
            showProgress: false),
        executeCommand(deviceId, 'df -h / | tail -1', showProgress: false),
      ]);

      info['uname'] = futures[0].success ? futures[0].stdout.trim() : 'Unknown';
      info['cpu'] = futures[1].success
          ? (futures[1].stdout.split(':').length > 1
              ? futures[1].stdout.split(':')[1].trim()
              : 'Unknown')
          : 'Unknown';
      info['memory'] =
          futures[2].success ? futures[2].stdout.trim() : 'Unknown';
      info['uptime'] =
          futures[3].success ? futures[3].stdout.trim() : 'Unknown';

      // Parse temperature
      if (futures[4].success) {
        final tempMatch =
            RegExp(r'temp=(\d+\.?\d*)').firstMatch(futures[4].stdout);
        info['temperature'] = tempMatch?.group(1) ?? 'N/A';
      } else {
        info['temperature'] = 'N/A';
      }

      // Parse disk usage
      if (futures[5].success) {
        final diskParts = futures[5].stdout.trim().split(RegExp(r'\s+'));
        if (diskParts.length >= 5) {
          info['disk_total'] = diskParts[1];
          info['disk_used'] = diskParts[2];
          info['disk_available'] = diskParts[3];
          info['disk_usage_percent'] = diskParts[4];
        }
      }
    } catch (e) {
      debugPrint('Error getting system info: $e');
      info['error'] = e.toString();
    }

    return info;
  }

  /// Validate configuration file syntax
  Future<ValidationResult> validateConfigFile(
      String deviceId, String filePath) async {
    try {
      // Basic validation based on file type
      if (filePath.contains('config.txt')) {
        return _validateBootConfig(deviceId, filePath);
      } else if (filePath.contains('wpa_supplicant.conf')) {
        return _validateWpaSupplicant(deviceId, filePath);
      } else {
        // Generic validation - check if file is readable
        final result = await executeCommand(
          deviceId,
          'test -r "$filePath" && echo "valid" || echo "invalid"',
          showProgress: false,
        );

        return ValidationResult(
          isValid: result.success && result.stdout.trim() == 'valid',
          errors: result.success && result.stdout.trim() == 'valid'
              ? []
              : ['File is not readable or does not exist'],
          warnings: [],
        );
      }
    } catch (e) {
      return ValidationResult(
        isValid: false,
        errors: ['Validation failed: $e'],
        warnings: [],
      );
    }
  }

  Future<ValidationResult> _validateBootConfig(
      String deviceId, String filePath) async {
    try {
      final result = await executeCommand(
        deviceId,
        'cat "$filePath"',
        showProgress: false,
      );

      if (!result.success) {
        return ValidationResult(
          isValid: false,
          errors: ['Cannot read config file'],
          warnings: [],
        );
      }

      final lines = result.stdout.split('\n');
      final errors = <String>[];
      final warnings = <String>[];

      for (int i = 0; i < lines.length; i++) {
        final line = lines[i].trim();
        if (line.isEmpty || line.startsWith('#')) continue;

        // Check for common configuration errors
        if (line.contains('=')) {
          final parts = line.split('=');
          if (parts.length != 2) {
            errors.add('Line ${i + 1}: Invalid syntax - $line');
          } else {
            final key = parts[0].trim();
            final value = parts[1].trim();

            // Validate specific parameters
            if (key == 'arm_freq' && int.tryParse(value) == null) {
              errors.add('Line ${i + 1}: arm_freq must be a number');
            } else if (key == 'gpu_mem' && int.tryParse(value) == null) {
              errors.add('Line ${i + 1}: gpu_mem must be a number');
            } else if (key.startsWith('dtoverlay') && value.isEmpty) {
              warnings.add('Line ${i + 1}: Empty dtoverlay value');
            }
          }
        } else {
          warnings.add('Line ${i + 1}: Unrecognized format - $line');
        }
      }

      return ValidationResult(
        isValid: errors.isEmpty,
        errors: errors,
        warnings: warnings,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        errors: ['Validation error: $e'],
        warnings: [],
      );
    }
  }

  Future<ValidationResult> _validateWpaSupplicant(
      String deviceId, String filePath) async {
    try {
      final result = await executeCommand(
        deviceId,
        'wpa_supplicant -c "$filePath" -i wlan0 -D nl80211 -d 2>&1 | head -20',
        showProgress: false,
      );

      final errors = <String>[];
      final warnings = <String>[];

      if (result.stderr.contains('Failed to read config file')) {
        errors.add('Configuration file format is invalid');
      }

      if (result.stderr.contains('Line') && result.stderr.contains('error')) {
        errors.add('Syntax error in configuration file');
      }

      return ValidationResult(
        isValid: errors.isEmpty,
        errors: errors,
        warnings: warnings,
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        errors: ['Validation error: $e'],
        warnings: [],
      );
    }
  }
}

class CommandResult {
  final String command;
  final String stdout;
  final String stderr;
  final int exitCode;
  final bool success;

  CommandResult({
    required this.command,
    required this.stdout,
    required this.stderr,
    required this.exitCode,
    required this.success,
  });

  @override
  String toString() {
    return 'CommandResult(command: $command, exitCode: $exitCode, success: $success)';
  }
}

class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasWarnings => warnings.isNotEmpty;
}
