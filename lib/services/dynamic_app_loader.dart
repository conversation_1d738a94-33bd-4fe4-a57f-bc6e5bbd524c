import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import '../models/app_module.dart';
import '../widgets/template_app_screen.dart';
import '../screens/file_manager_screen.dart';

/// Service for dynamically loading and managing modular apps
class DynamicAppLoader {
  static final DynamicAppLoader _instance = DynamicAppLoader._internal();
  factory DynamicAppLoader() => _instance;
  DynamicAppLoader._internal();

  final Map<String, AppModule> _loadedApps = {};
  final Map<String, Function(BuildContext)> _appLaunchers = {};

  /// Load an app from its folder structure
  Future<AppModule?> loadAppFromFolder(String appId, String folderPath) async {
    try {
      // Try to load from file system first (for dynamic apps)
      String? configData;
      final configPath = '$folderPath/app.json';

      configData = await _loadFromFileSystem(configPath);

      // If file system fails, try asset bundle (for built-in apps)
      if (configData == null) {
        try {
          configData = await rootBundle.loadString(configPath);
          debugPrint('Loaded from asset bundle: $configPath');
        } catch (e) {
          debugPrint('Asset bundle also failed for $configPath: $e');
        }
      } else {
        debugPrint('Loaded from file system: $configPath');
      }

      if (configData == null) {
        debugPrint('Could not load app config from $configPath');
        return null;
      }

      final configJson = jsonDecode(configData) as Map<String, dynamic>;

      // Create the app module
      final appModule = AppModule.fromJson(configJson);

      // Load additional app assets if specified
      if (appModule.assetFiles.isNotEmpty) {
        await _loadAppAssets(appModule);
      }

      // Register the app launcher if main dart file is specified
      if (appModule.mainDartFile != null) {
        await _registerAppLauncher(appModule);
      }

      _loadedApps[appId] = appModule;
      debugPrint('Successfully loaded app: ${appModule.title}');

      return appModule;
    } catch (e) {
      debugPrint('Error loading app from folder $folderPath: $e');
      return null;
    }
  }

  /// Load content from file system when asset bundle is not available
  Future<String?> _loadFromFileSystem(String assetPath) async {
    try {
      // Get the project root directory
      final currentDir = Directory.current;
      String projectRoot = currentDir.path;
      if (projectRoot.contains('/build/')) {
        projectRoot = projectRoot.split('/build/').first;
      }

      // Convert asset path to file system path
      final filePath =
          assetPath.replaceFirst('assets/', '$projectRoot/assets/');
      final file = File(filePath);

      if (await file.exists()) {
        return await file.readAsString();
      }
      return null;
    } catch (e) {
      debugPrint('Error loading from file system: $e');
      return null;
    }
  }

  /// Load app assets (icons, configs, etc.)
  Future<void> _loadAppAssets(AppModule appModule) async {
    if (appModule.appFolderPath == null) return;

    for (final assetFile in appModule.assetFiles) {
      try {
        final assetPath = '${appModule.appFolderPath}/$assetFile';

        // Load different types of assets
        if (assetFile.endsWith('.json')) {
          final assetData = await rootBundle.loadString(assetPath);
          jsonDecode(assetData); // Validate JSON format
          debugPrint('Loaded JSON asset: $assetFile for ${appModule.id}');
        } else if (assetFile.endsWith('.png') || assetFile.endsWith('.jpg')) {
          // For image assets, we just verify they exist
          await rootBundle.load(assetPath);
          debugPrint('Verified image asset: $assetFile for ${appModule.id}');
        }
      } catch (e) {
        debugPrint(
            'Warning: Could not load asset $assetFile for ${appModule.id}: $e');
      }
    }
  }

  /// Register app launcher function
  Future<void> _registerAppLauncher(AppModule appModule) async {
    // For now, we'll use a simple mapping system
    // In a more advanced implementation, you could use dart:mirrors or code generation
    debugPrint('Registering app launcher for: ${appModule.id}');
    switch (appModule.id) {
      case 'system_monitor':
        _appLaunchers[appModule.id] =
            (context) => _launchSystemMonitor(context);
        debugPrint('Registered system_monitor launcher');
        break;
      case 'file_manager':
        _appLaunchers[appModule.id] = (context) => _launchFileManager(context);
        debugPrint('Registered file_manager launcher');
        break;
      default:
        // For newly created apps, try to launch their template page
        _appLaunchers[appModule.id] =
            (context) => _launchTemplateApp(context, appModule);
        debugPrint('Registered template launcher for: ${appModule.id}');
    }
  }

  /// Launch an app by its ID
  void launchApp(String appId, BuildContext context) {
    debugPrint('Attempting to launch app: $appId');
    final launcher = _appLaunchers[appId];
    if (launcher != null) {
      debugPrint('Found launcher for $appId, executing...');
      launcher(context);
    } else {
      debugPrint('No launcher found for $appId, showing coming soon dialog');
      _showComingSoon(context, appId);
    }
  }

  /// Check if an app can run
  bool canAppRun(String appId, BuildContext context) {
    final app = _loadedApps[appId];
    if (app == null) return false;

    // Check if app requires connection and we have connected devices
    final requiresConnection =
        app.metadata['requiresConnection'] as bool? ?? false;
    if (requiresConnection) {
      // You would check the app state here
      return true; // Simplified for now
    }

    return true;
  }

  /// Get loaded app by ID
  AppModule? getApp(String appId) => _loadedApps[appId];

  /// Get all loaded apps
  List<AppModule> get loadedApps => _loadedApps.values.toList();

  /// Clear all loaded apps
  void clearApps() {
    _loadedApps.clear();
    _appLaunchers.clear();
  }

  // App launcher implementations
  void _launchSystemMonitor(BuildContext context) {
    // Import and launch system monitor
    Navigator.of(context).pushNamed('/system_monitor');
  }

  void _launchFileManager(BuildContext context) {
    // Import and launch file manager directly
    debugPrint('Launching File Manager screen...');
    try {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const FileManagerScreen(),
        ),
      );
      debugPrint('File Manager screen launched successfully');
    } catch (e) {
      debugPrint('Error launching File Manager: $e');
    }
  }

  void _launchTemplateApp(BuildContext context, AppModule appModule) {
    // Launch the template "coming soon" page for newly created apps
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TemplateAppScreen(appModule: appModule),
      ),
    );
  }

  void _showComingSoon(BuildContext context, String appName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(appName),
        content: const Text('This app is coming soon! Stay tuned for updates.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
