import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../models/app_module.dart';
import 'dynamic_app_loader.dart';

/// Service for managing app modules
class AppModuleManager extends ChangeNotifier {
  static final AppModuleManager _instance = AppModuleManager._internal();
  factory AppModuleManager() => _instance;
  AppModuleManager._internal();

  final List<AppModule> _modules = [];
  final List<String> _moduleOrder = [];
  bool _isLoading = false;
  String? _error;

  List<AppModule> get modules => List.unmodifiable(_modules);
  List<AppModule> get enabledModules =>
      _modules.where((m) => m.isEnabled).toList();
  List<AppModule> get orderedModules {
    final ordered = <AppModule>[];

    // Add modules in the saved order
    for (final moduleId in _moduleOrder) {
      final module = _modules.where((m) => m.id == moduleId).firstOrNull;
      if (module != null && module.isEnabled) {
        ordered.add(module);
      }
    }

    // Add any modules not in the order list (new modules)
    for (final module in _modules) {
      if (module.isEnabled && !_moduleOrder.contains(module.id)) {
        ordered.add(module);
      }
    }

    return ordered;
  }

  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Initialize the app module manager
  Future<void> initialize({bool force = false}) async {
    // Prevent re-initialization if already loaded (unless forced)
    if (_modules.isNotEmpty && !_isLoading && !force) {
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _loadModuleOrder();
      await _loadCustomModules();
      _sortModules();
    } catch (e) {
      _error = 'Failed to initialize app modules: $e';
      debugPrint(_error);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Load the saved module order from preferences
  Future<void> _loadModuleOrder() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _moduleOrder.clear();
      _moduleOrder.addAll(prefs.getStringList('app_module_order') ?? []);
    } catch (e) {
      debugPrint('Error loading module order: $e');
    }
  }

  /// Save the current module order to preferences
  Future<void> _saveModuleOrder() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('app_module_order', _moduleOrder);
    } catch (e) {
      debugPrint('Error saving module order: $e');
    }
  }

  /// Load app modules from assets and external files
  Future<void> _loadCustomModules() async {
    try {
      // Load from new folder-based structure
      await _loadModulesFromFolders();

      // Also load legacy modules for backward compatibility
      await _loadLegacyModules();
    } catch (e) {
      debugPrint('Error loading custom modules: $e');
    }

    // Load user-imported apps
    await _loadUserImportedApps();
  }

  /// Load modules from new folder-based structure
  Future<void> _loadModulesFromFolders() async {
    try {
      // Try to load from file system first (for dynamic apps)
      Map<String, dynamic>? manifest = await _loadManifestFromFileSystem();

      // Fallback to asset bundle if file system fails
      if (manifest == null) {
        try {
          final manifestData =
              await rootBundle.loadString('assets/apps/manifest.json');
          manifest = jsonDecode(manifestData) as Map<String, dynamic>;
        } catch (e) {
          debugPrint('Could not load manifest from assets: $e');
          return;
        }
      }

      // Check if this is the new folder-based structure
      if (manifest['structure'] == 'folder-based') {
        final apps = manifest['apps'] as List<dynamic>;
        final appLoader = DynamicAppLoader();

        for (final appInfo in apps) {
          final appData = appInfo as Map<String, dynamic>;
          if (appData['enabled'] == true) {
            final appId = appData['id'] as String;
            final folderName = appData['folder'] as String;
            final folderPath = 'assets/apps/$folderName';

            final appModule =
                await appLoader.loadAppFromFolder(appId, folderPath);
            if (appModule != null) {
              _modules.add(appModule);
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading modules from folders: $e');
    }
  }

  /// Load manifest from file system (for dynamic apps)
  Future<Map<String, dynamic>?> _loadManifestFromFileSystem() async {
    try {
      // Get the project root directory
      final currentDir = Directory.current;
      String projectRoot = currentDir.path;
      if (projectRoot.contains('/build/')) {
        projectRoot = projectRoot.split('/build/').first;
      }

      final manifestFile = File('$projectRoot/assets/apps/manifest.json');
      if (await manifestFile.exists()) {
        final manifestContent = await manifestFile.readAsString();
        return jsonDecode(manifestContent) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('Error loading manifest from file system: $e');
      return null;
    }
  }

  /// Load user-imported apps from application data directory
  Future<void> _loadUserImportedApps() async {
    try {
      final appDataDir = await getApplicationSupportDirectory();
      final userAppsDir =
          Directory(path.join(appDataDir.path, 'imported_apps'));

      if (!await userAppsDir.exists()) {
        debugPrint('User apps directory does not exist yet');
        return;
      }

      // Scan for app folders in the user directory
      await for (final entity in userAppsDir.list()) {
        if (entity is Directory) {
          final appId = path.basename(entity.path);
          final appJsonFile = File(path.join(entity.path, 'app.json'));

          if (await appJsonFile.exists()) {
            try {
              final appJsonContent = await appJsonFile.readAsString();
              final appJson =
                  jsonDecode(appJsonContent) as Map<String, dynamic>;

              // Create AppModule from the JSON
              final appModule = AppModule.fromJson(appJson);

              // Update the app folder path to point to the user directory
              final updatedModule = appModule.copyWith(
                appFolderPath: entity.path,
              );

              _modules.add(updatedModule);
              debugPrint('Loaded user app: ${updatedModule.title}');
            } catch (e) {
              debugPrint('Error loading user app $appId: $e');
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading user-imported apps: $e');
    }
  }

  /// Load legacy modules for backward compatibility
  Future<void> _loadLegacyModules() async {
    try {
      final manifestData =
          await rootBundle.loadString('assets/apps/manifest.json');
      final manifest = jsonDecode(manifestData) as Map<String, dynamic>;

      // Load legacy modules if they exist
      final legacyModules = manifest['legacy_modules'] as List<dynamic>? ?? [];

      for (final moduleFile in legacyModules) {
        try {
          final moduleData =
              await rootBundle.loadString('assets/apps/$moduleFile');
          final moduleJson = jsonDecode(moduleData) as Map<String, dynamic>;
          final module = AppModule.fromJson(moduleJson);
          _modules.add(module);
        } catch (e) {
          debugPrint('Error loading legacy module $moduleFile: $e');
        }
      }
    } catch (e) {
      debugPrint('Error loading legacy modules: $e');
    }
  }

  /// Sort modules by their order and category
  void _sortModules() {
    _modules.sort((a, b) {
      // First sort by enabled status
      if (a.isEnabled != b.isEnabled) {
        return a.isEnabled ? -1 : 1;
      }

      // Then by sort order
      final orderComparison = a.sortOrder.compareTo(b.sortOrder);
      if (orderComparison != 0) return orderComparison;

      // Finally by title
      return a.title.compareTo(b.title);
    });
  }

  /// Reorder modules based on user interaction
  Future<void> reorderModules(int oldIndex, int newIndex) async {
    final currentOrderedModules = orderedModules;
    if (oldIndex < 0 ||
        oldIndex >= currentOrderedModules.length ||
        newIndex < 0 ||
        newIndex >= currentOrderedModules.length) {
      return;
    }

    // Update the module order list
    _moduleOrder.clear();
    final reorderedModules = List<AppModule>.from(currentOrderedModules);
    final module = reorderedModules.removeAt(oldIndex);
    reorderedModules.insert(newIndex, module);

    _moduleOrder.addAll(reorderedModules.map((m) => m.id));

    await _saveModuleOrder();
    notifyListeners();
  }

  /// Update module order with a new list of modules
  Future<void> updateModuleOrder(List<AppModule> reorderedModules) async {
    _moduleOrder.clear();
    _moduleOrder.addAll(reorderedModules.map((m) => m.id));
    await _saveModuleOrder();
    notifyListeners();
  }

  /// Get a module by its ID
  AppModule? getModule(String id) {
    return _modules.where((m) => m.id == id).firstOrNull;
  }

  /// Get modules by category
  List<AppModule> getModulesByCategory(String category) {
    return _modules
        .where((m) => m.category == category && m.isEnabled)
        .toList();
  }

  /// Toggle module enabled state
  Future<void> toggleModule(String moduleId, bool enabled) async {
    final index = _modules.indexWhere((m) => m.id == moduleId);
    if (index != -1) {
      _modules[index] = _modules[index].copyWith(isEnabled: enabled);
      notifyListeners();

      // Save to preferences
      await _saveModuleStates();
    }
  }

  /// Save module states to preferences
  Future<void> _saveModuleStates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final states = <String, bool>{};
      for (final module in _modules) {
        states[module.id] = module.isEnabled;
      }
      await prefs.setString('app_module_states', jsonEncode(states));
    } catch (e) {
      debugPrint('Error saving module states: $e');
    }
  }

  /// Refresh modules (reload from sources)
  Future<void> refresh() async {
    debugPrint('AppModuleManager: Starting refresh...');
    _modules.clear();
    _moduleOrder.clear();
    _error = null;

    // Clear the dynamic app loader cache
    final appLoader = DynamicAppLoader();
    appLoader.clearApps();
    debugPrint('AppModuleManager: Cleared caches, reinitializing...');

    await initialize(force: true);
    debugPrint(
        'AppModuleManager: Refresh completed. Loaded ${_modules.length} modules');
  }

  /// Clear all user-imported apps (for testing purposes)
  Future<void> clearUserApps() async {
    try {
      final appDataDir = await getApplicationSupportDirectory();
      final userAppsDir =
          Directory(path.join(appDataDir.path, 'imported_apps'));

      if (await userAppsDir.exists()) {
        await userAppsDir.delete(recursive: true);
        debugPrint('Cleared user apps directory');
      }

      // Force clear all cached data
      _modules.clear();
      _moduleOrder.clear();
      _error = null;

      // Clear the dynamic app loader cache
      final appLoader = DynamicAppLoader();
      appLoader.clearApps();

      // Clear any shared preferences cache
      await _clearModuleOrderCache();

      debugPrint('Cleared all app caches');

      // Refresh to reload without user apps
      await refresh();
    } catch (e) {
      debugPrint('Error clearing user apps: $e');
    }
  }

  /// Clear module order cache from shared preferences
  Future<void> _clearModuleOrderCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('module_order');
      await prefs.remove('module_states');
      debugPrint('Cleared module cache from shared preferences');
    } catch (e) {
      debugPrint('Error clearing module cache: $e');
    }
  }
}
