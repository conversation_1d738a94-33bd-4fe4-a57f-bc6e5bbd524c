import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:jelly_pi/services/app_state.dart';
import 'package:jelly_pi/models/remote_file_item.dart';

/// Service for managing files on remote devices via SSH
class FileManagerService {
  final AppState _appState;

  FileManagerService(this._appState);

  /// List directory contents
  Future<List<RemoteFileItem>> listDirectory(String deviceId, String path,
      {bool showHidden = false}) async {
    try {
      final connection = _appState.getSSHConnection(deviceId);
      if (connection == null || !connection.isConnected) {
        throw Exception('Device not connected');
      }

      // Build ls command with options
      final hiddenFlag = showHidden ? 'a' : '';
      final command =
          'ls -l${hiddenFlag}h "$path" 2>/dev/null || echo "ERROR: Cannot access directory"';

      final session = await connection.client!.execute(command);
      final output = await utf8.decoder.bind(session.stdout).join();

      if (output.contains('ERROR: Cannot access directory')) {
        throw Exception('Cannot access directory: $path');
      }

      return _parseDirectoryListing(output, path);
    } catch (e) {
      debugPrint('Error listing directory: $e');
      rethrow;
    }
  }

  /// Parse ls -l output into RemoteFileItem objects
  List<RemoteFileItem> _parseDirectoryListing(String output, String basePath) {
    final lines =
        output.split('\n').where((line) => line.trim().isNotEmpty).toList();
    final items = <RemoteFileItem>[];

    for (final line in lines) {
      if (line.startsWith('total ') || line.trim().isEmpty) continue;

      try {
        final item = _parseFileEntry(line, basePath);
        if (item != null) {
          items.add(item);
        }
      } catch (e) {
        debugPrint('Error parsing line: $line - $e');
      }
    }

    return items;
  }

  /// Parse a single file entry from ls -l output
  RemoteFileItem? _parseFileEntry(String line, String basePath) {
    final parts = line.split(RegExp(r'\s+'));
    if (parts.length < 9) return null;

    final permissions = parts[0];
    final isDirectory = permissions.startsWith('d');
    final sizeStr = parts[4];
    final name = parts.sublist(8).join(' ');

    // Skip . and .. entries
    if (name == '.' || name == '..') return null;

    final size = _parseFileSize(sizeStr);
    final path = basePath.endsWith('/') ? '$basePath$name' : '$basePath/$name';

    return RemoteFileItem(
      path: path,
      name: name,
      isDirectory: isDirectory,
      size: size,
      modifiedDate: DateTime.now(), // Simplified - could parse from ls output
      permissions: permissions,
      owner: 'unknown', // Could be parsed from ls -l output
      group: 'unknown', // Could be parsed from ls -l output
    );
  }

  /// Parse file size from human-readable format (e.g., "1.2K", "3.4M", "567")
  int _parseFileSize(String sizeStr) {
    if (sizeStr.isEmpty) return 0;

    // Try parsing as plain number first
    final plainSize = int.tryParse(sizeStr);
    if (plainSize != null) return plainSize;

    // Handle human-readable format (K, M, G, etc.)
    final regex =
        RegExp(r'^(\d+(?:\.\d+)?)\s*([KMGTPE]?)$', caseSensitive: false);
    final match = regex.firstMatch(sizeStr);

    if (match == null) return 0;

    final number = double.tryParse(match.group(1) ?? '0') ?? 0;
    final unit = (match.group(2) ?? '').toUpperCase();

    switch (unit) {
      case 'K':
        return (number * 1024).round();
      case 'M':
        return (number * 1024 * 1024).round();
      case 'G':
        return (number * 1024 * 1024 * 1024).round();
      case 'T':
        return (number * 1024 * 1024 * 1024 * 1024).round();
      case 'P':
        return (number * 1024 * 1024 * 1024 * 1024 * 1024).round();
      case 'E':
        return (number * 1024 * 1024 * 1024 * 1024 * 1024 * 1024).round();
      default:
        return number.round();
    }
  }

  /// Delete a file or directory
  Future<void> deleteItem(String deviceId, String path) async {
    try {
      final connection = _appState.getSSHConnection(deviceId);
      if (connection == null || !connection.isConnected) {
        throw Exception('Device not connected');
      }

      final session = await connection.client!.execute('rm -rf "$path"');
      final exitCode = session.exitCode;

      if (exitCode != null && exitCode != 0) {
        final error = await utf8.decoder.bind(session.stderr).join();
        throw Exception('Failed to delete item: $error');
      }
    } catch (e) {
      debugPrint('Error deleting item: $e');
      rethrow;
    }
  }

  /// Rename/move a file or directory
  Future<void> renameItem(
      String deviceId, String oldPath, String newPath) async {
    try {
      final connection = _appState.getSSHConnection(deviceId);
      if (connection == null || !connection.isConnected) {
        throw Exception('Device not connected');
      }

      final session =
          await connection.client!.execute('mv "$oldPath" "$newPath"');
      final exitCode = session.exitCode;

      if (exitCode != null && exitCode != 0) {
        final error = await utf8.decoder.bind(session.stderr).join();
        throw Exception('Failed to rename item: $error');
      }
    } catch (e) {
      debugPrint('Error renaming item: $e');
      rethrow;
    }
  }

  /// Create a new directory
  Future<void> createDirectory(
      String deviceId, String parentPath, String name) async {
    try {
      final connection = _appState.getSSHConnection(deviceId);
      if (connection == null || !connection.isConnected) {
        throw Exception('Device not connected');
      }

      final fullPath =
          parentPath.endsWith('/') ? '$parentPath$name' : '$parentPath/$name';
      final session = await connection.client!.execute('mkdir -p "$fullPath"');
      final exitCode = session.exitCode;

      if (exitCode != null && exitCode != 0) {
        final error = await utf8.decoder.bind(session.stderr).join();
        throw Exception('Failed to create directory: $error');
      }
    } catch (e) {
      debugPrint('Error creating directory: $e');
      rethrow;
    }
  }

  /// Create a new empty file
  Future<void> createFile(
      String deviceId, String parentPath, String name) async {
    try {
      final connection = _appState.getSSHConnection(deviceId);
      if (connection == null || !connection.isConnected) {
        throw Exception('Device not connected');
      }

      final fullPath =
          parentPath.endsWith('/') ? '$parentPath$name' : '$parentPath/$name';
      final session = await connection.client!.execute('touch "$fullPath"');
      final exitCode = session.exitCode;

      if (exitCode != null && exitCode != 0) {
        final error = await utf8.decoder.bind(session.stderr).join();
        throw Exception('Failed to create file: $error');
      }
    } catch (e) {
      debugPrint('Error creating file: $e');
      rethrow;
    }
  }

  /// Upload a file to remote device
  Future<void> uploadFile(
    String deviceId,
    String remotePath,
    Uint8List fileData, {
    Function(double)? onProgress,
  }) async {
    try {
      final connection = _appState.getSSHConnection(deviceId);
      if (connection == null || !connection.isConnected) {
        throw Exception('Device not connected');
      }

      // For small files, use base64 encoding
      if (fileData.length < 1024 * 1024) {
        // Less than 1MB
        final base64Data = base64Encode(fileData);

        // Split large base64 strings into chunks to avoid command line limits
        const chunkSize = 8192; // 8KB chunks
        final chunks = <String>[];
        for (int i = 0; i < base64Data.length; i += chunkSize) {
          final end = (i + chunkSize < base64Data.length)
              ? i + chunkSize
              : base64Data.length;
          chunks.add(base64Data.substring(i, end));
        }

        // Create empty file first
        await connection.client!.execute('touch "$remotePath"');

        // Write chunks to file
        for (int i = 0; i < chunks.length; i++) {
          final chunk = chunks[i];
          final appendCmd = i == 0
              ? 'echo -n "$chunk" | base64 -d > "$remotePath"'
              : 'echo -n "$chunk" | base64 -d >> "$remotePath"';

          final session = await connection.client!.execute(appendCmd);
          final exitCode = session.exitCode;

          if (exitCode != null && exitCode != 0) {
            final error = await utf8.decoder.bind(session.stderr).join();
            throw Exception('Failed to upload file chunk ${i + 1}: $error');
          }

          // Report progress
          if (onProgress != null) {
            onProgress((i + 1) / chunks.length);
          }
        }
      } else {
        // For larger files, use SFTP if available, otherwise use chunked approach
        // This is a simplified approach - in production you might want to use SFTP
        throw Exception(
            'File too large for current upload method. Please use files smaller than 1MB.');
      }

      // Verify the file was created successfully
      final verifySession = await connection.client!
          .execute('test -f "$remotePath" && echo "success"');
      final verifyOutput = await utf8.decoder.bind(verifySession.stdout).join();

      if (!verifyOutput.trim().contains('success')) {
        throw Exception('File upload verification failed');
      }
    } catch (e) {
      debugPrint('Error uploading file: $e');
      rethrow;
    }
  }

  /// Move a file or directory
  Future<void> moveItem(
      String deviceId, String sourcePath, String destinationPath) async {
    try {
      final connection = _appState.getSSHConnection(deviceId);
      if (connection == null || !connection.isConnected) {
        throw Exception('Device not connected');
      }

      final session = await connection.client!
          .execute('mv "$sourcePath" "$destinationPath"');
      final exitCode = session.exitCode;

      if (exitCode != null && exitCode != 0) {
        final error = await utf8.decoder.bind(session.stderr).join();
        throw Exception('Failed to move item: $error');
      }
    } catch (e) {
      debugPrint('Error moving item: $e');
      rethrow;
    }
  }

  /// Copy a file or directory
  Future<void> copyItem(
      String deviceId, String sourcePath, String destinationPath) async {
    try {
      final connection = _appState.getSSHConnection(deviceId);
      if (connection == null || !connection.isConnected) {
        throw Exception('Device not connected');
      }

      final session = await connection.client!
          .execute('cp -r "$sourcePath" "$destinationPath"');
      final exitCode = session.exitCode;

      if (exitCode != null && exitCode != 0) {
        final error = await utf8.decoder.bind(session.stderr).join();
        throw Exception('Failed to copy item: $error');
      }
    } catch (e) {
      debugPrint('Error copying item: $e');
      rethrow;
    }
  }

  /// Download a file from remote device
  Future<Uint8List> downloadFile(String deviceId, String remotePath) async {
    try {
      final connection = _appState.getSSHConnection(deviceId);
      if (connection == null || !connection.isConnected) {
        throw Exception('Device not connected');
      }

      // First check if file exists and get its size
      final checkSession = await connection.client!.execute(
          'test -f "$remotePath" && stat -c%s "$remotePath" 2>/dev/null || echo "0"');
      final sizeOutput = await utf8.decoder.bind(checkSession.stdout).join();
      final fileSize = int.tryParse(sizeOutput.trim()) ?? 0;

      if (fileSize == 0) {
        throw Exception('File does not exist, is empty, or is not accessible');
      }

      // Always use base64 for reliable binary transfer
      final session =
          await connection.client!.execute('base64 -w 0 "$remotePath"');

      // Check if command executed successfully
      if (session.exitCode != null && session.exitCode != 0) {
        final error = await utf8.decoder.bind(session.stderr).join();
        throw Exception('Failed to download file: $error');
      }

      // Collect all base64 content
      final base64Content = await utf8.decoder.bind(session.stdout).join();

      if (base64Content.trim().isEmpty) {
        throw Exception('File appears to be empty or could not be read');
      }

      try {
        // Clean up the base64 content (remove any whitespace/newlines)
        final cleanBase64 = base64Content.replaceAll(RegExp(r'\s'), '');
        final result = base64Decode(cleanBase64);
        return Uint8List.fromList(result);
      } catch (e) {
        throw Exception('Failed to decode downloaded file: $e');
      }
    } catch (e) {
      debugPrint('Error downloading file: $e');
      rethrow;
    }
  }

  /// Read a file from remote device (alias for downloadFile)
  Future<Uint8List> readFile(String deviceId, String path) async {
    return downloadFile(deviceId, path);
  }
}
