import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

class MacVendorService {
  static const String _ouiUrl = 'http://standards-oui.ieee.org/oui/oui.txt';
  static const Duration _cacheExpiry = Duration(days: 7);

  static File? _ouiFile;
  static final Map<String, String> _vendorCache = {};

  /// Initialize the MAC vendor service
  static Future<void> initialize() async {
    try {
      final appDir = await getApplicationSupportDirectory();
      _ouiFile = File('${appDir.path}/oui.txt');

      // Download OUI database if needed
      await _ensureOuiDatabase();

      debugPrint('MAC vendor service initialized');
    } catch (e) {
      debugPrint('Failed to initialize MAC vendor service: $e');
    }
  }

  /// Get vendor name from MAC address
  static Future<String> getVendorFromMac(String mac) async {
    if (mac.isEmpty || mac == 'Unknown') {
      return 'Unknown';
    }

    try {
      // Extract OUI (first 3 bytes) from MAC address
      final macClean = mac.replaceAll(RegExp(r'[:-]'), '').toUpperCase();
      if (macClean.length < 6) {
        return 'Unknown';
      }

      final oui = macClean.substring(0, 6);

      // Check cache first
      if (_vendorCache.containsKey(oui)) {
        return _vendorCache[oui]!;
      }

      // Lookup in OUI database
      final vendor = await _lookupOuiVendor(oui);

      // Cache the result
      _vendorCache[oui] = vendor;

      return vendor;
    } catch (e) {
      debugPrint('Error looking up vendor for MAC $mac: $e');
      return 'Unknown';
    }
  }

  /// Ensure OUI database is available and up to date
  static Future<void> _ensureOuiDatabase() async {
    if (_ouiFile == null) return;

    try {
      // Check if file exists and is recent
      if (await _ouiFile!.exists()) {
        final lastModified = await _ouiFile!.lastModified();
        final age = DateTime.now().difference(lastModified);

        if (age < _cacheExpiry) {
          debugPrint('OUI database is up to date');
          return;
        }
      }

      // Download fresh OUI database
      debugPrint('Downloading IEEE OUI database...');
      final response = await http.get(Uri.parse(_ouiUrl));

      if (response.statusCode == 200) {
        await _ouiFile!.writeAsString(response.body);
        debugPrint('OUI database downloaded successfully');
      } else {
        debugPrint('Failed to download OUI database: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error downloading OUI database: $e');
    }
  }

  /// Lookup vendor in OUI database
  static Future<String> _lookupOuiVendor(String oui) async {
    if (_ouiFile == null || !await _ouiFile!.exists()) {
      return _getFallbackVendor(oui);
    }

    try {
      // Format OUI with dashes for searching (XX-XX-XX)
      final ouiFormatted =
          '${oui.substring(0, 2)}-${oui.substring(2, 4)}-${oui.substring(4, 6)}';

      // Read and search the OUI file
      final lines = await _ouiFile!.readAsLines();

      for (final line in lines) {
        if (line.startsWith(ouiFormatted)) {
          // Extract vendor name from the line
          // Format: "XX-XX-XX   (hex)		Vendor Name"
          if (line.contains('(hex)')) {
            final hexPos = line.indexOf('(hex)');
            if (hexPos != -1) {
              final vendor = line.substring(hexPos + 5).trim();
              if (vendor.isNotEmpty) {
                return vendor;
              }
            }
          }
        }
      }

      return _getFallbackVendor(oui);
    } catch (e) {
      debugPrint('Error searching OUI database: $e');
      return _getFallbackVendor(oui);
    }
  }

  /// Get vendor from fallback list (common manufacturers)
  static String _getFallbackVendor(String oui) {
    // Common vendor prefixes
    final fallbackVendors = {
      // Raspberry Pi Foundation
      '28CDC1': 'Raspberry Pi Foundation',
      '3A3541': 'Raspberry Pi Foundation',
      'D83ADD': 'Raspberry Pi Foundation',
      'E45F01': 'Raspberry Pi Foundation',
      '2CCF67': 'Raspberry Pi Foundation',
      'B827EB': 'Raspberry Pi Foundation',
      'DCA632': 'Raspberry Pi Foundation',

      // Apple
      '001EC2': 'Apple',
      '0050E4': 'Apple',
      '0017F2': 'Apple',
      '001F5B': 'Apple',
      '0025BC': 'Apple',

      // Intel
      '001B21': 'Intel Corporation',
      '0015F2': 'Intel Corporation',
      '001E64': 'Intel Corporation',

      // Broadcom
      '001018': 'Broadcom',
      '0010F3': 'Broadcom',

      // Realtek
      '001E06': 'Realtek Semiconductor',
      '00E04C': 'Realtek Semiconductor',

      // TP-Link
      '001F3F': 'TP-Link Technologies',
      '50C7BF': 'TP-Link Technologies',

      // D-Link
      '001346': 'D-Link Corporation',
      '0015E9': 'D-Link Corporation',
    };

    return fallbackVendors[oui] ?? 'Unknown';
  }

  /// Clear the vendor cache
  static void clearCache() {
    _vendorCache.clear();
    debugPrint('MAC vendor cache cleared');
  }

  /// Get cache statistics
  static Map<String, dynamic> getCacheStats() {
    return {
      'cached_vendors': _vendorCache.length,
      'database_exists': _ouiFile?.existsSync() ?? false,
    };
  }
}
