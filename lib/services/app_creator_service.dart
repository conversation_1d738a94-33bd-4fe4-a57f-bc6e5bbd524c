import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../services/app_module_manager.dart';

class AppCreatorService {
  /// Create a new app from a template
  Future<void> createApp({
    required String name,
    required String description,
    required String author,
    required String version,
    required String category,
    required String icon,
    required String template,
    String? iconPath,
    bool raspberryPiOnly = false,
  }) async {
    // Generate unique app ID from name
    final appId = await _generateUniqueAppId(name);

    // Create app folder
    final appFolder = await _createAppFolder(appId);

    // Generate app files based on template
    await _generateAppFiles(
      appFolder: appFolder,
      appId: appId,
      name: name,
      description: description,
      author: author,
      version: version,
      category: category,
      icon: icon,
      template: template,
      iconPath: iconPath,
      raspberryPiOnly: raspberryPiOnly,
    );

    // Generate template page
    await _generateTemplatePage(
      appFolder: appFolder,
      appId: appId,
      name: name,
      description: description,
      template: template,
    );

    // Note: User apps don't need to be added to manifest.json since they're
    // loaded from the user directory automatically

    // Refresh app manager to pick up the new app immediately
    debugPrint('Refreshing app manager to pick up new app: $appId');
    await AppModuleManager().refresh();
    debugPrint('App manager refresh completed');

    debugPrint('Created app: $name (ID: $appId)');
  }

  /// Generate a unique app ID from name, ensuring no conflicts
  Future<String> _generateUniqueAppId(String name) async {
    // Convert name to snake_case ID
    String baseId = name
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .replaceAll(RegExp(r'_+'), '_')
        .replaceAll(RegExp(r'^_|_$'), '');

    // If baseId is empty, use a default
    if (baseId.isEmpty) {
      baseId = 'app';
    }

    // Check if this ID already exists
    if (!(await _isAppIdAvailable(baseId))) {
      // Generate unique ID by appending timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      baseId = '${baseId}_$timestamp';
    }

    return baseId;
  }

  /// Check if an app ID is available (not already used)
  Future<bool> _isAppIdAvailable(String appId) async {
    // Check in user apps directory
    final appDataDir = await getApplicationSupportDirectory();
    final userAppsDir = Directory(path.join(appDataDir.path, 'imported_apps'));
    final appDir = Directory(path.join(userAppsDir.path, appId));

    if (await appDir.exists()) {
      return false;
    }

    // Check in loaded modules
    final moduleManager = AppModuleManager();
    final existingModule =
        moduleManager.orderedModules.where((m) => m.id == appId).firstOrNull;

    if (existingModule != null) {
      return false;
    }

    return true;
  }

  Future<Directory> _createAppFolder(String appId) async {
    // Get the user apps directory (same as imported apps)
    final appDataDir = await getApplicationSupportDirectory();
    final userAppsDir = Directory(path.join(appDataDir.path, 'imported_apps'));

    // Ensure the user apps directory exists
    if (!await userAppsDir.exists()) {
      await userAppsDir.create(recursive: true);
    }

    // Create the app folder in the user directory
    final appFolder = Directory(path.join(userAppsDir.path, appId));

    // Since we've already ensured the ID is unique, we can create the folder
    // But add a safety check just in case
    if (await appFolder.exists()) {
      throw Exception(
          'App with ID "$appId" already exists (this should not happen)');
    }

    await appFolder.create(recursive: true);
    debugPrint('Created user app folder: ${appFolder.path}');
    return appFolder;
  }

  /// Copy custom icon to app folder and return relative path
  Future<String> _copyCustomIcon(Directory appFolder, String iconPath) async {
    final sourceFile = File(iconPath);
    if (!await sourceFile.exists()) {
      throw Exception('Custom icon file does not exist: $iconPath');
    }

    // Get file extension
    final extension = path.extension(iconPath);

    // Add timestamp to ensure unique filename for each update
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final iconFileName = 'icon_$timestamp$extension';

    // Copy to app folder
    final targetFile = File(path.join(appFolder.path, iconFileName));
    await sourceFile.copy(targetFile.path);

    debugPrint('Copied custom icon to: ${targetFile.path}');
    return iconFileName; // Return relative path within app folder
  }

  Future<void> _generateAppFiles({
    required Directory appFolder,
    required String appId,
    required String name,
    required String description,
    required String author,
    required String version,
    required String category,
    required String icon,
    required String template,
    String? iconPath,
    bool raspberryPiOnly = false,
  }) async {
    // Copy custom icon if provided
    String? finalIconPath;
    if (iconPath != null) {
      finalIconPath = await _copyCustomIcon(appFolder, iconPath);
    }

    // Generate app.json
    await _generateAppJson(
      appFolder: appFolder,
      appId: appId,
      name: name,
      description: description,
      author: author,
      version: version,
      category: category,
      icon: icon,
      iconPath: finalIconPath,
      raspberryPiOnly: raspberryPiOnly,
    );

    // Generate main Dart file
    await _generateMainDartFile(
      appFolder: appFolder,
      appId: appId,
      name: name,
      template: template,
    );

    // Generate config.json
    await _generateConfigJson(
      appFolder: appFolder,
      name: name,
      template: template,
    );

    // Generate README.md
    await _generateReadme(
      appFolder: appFolder,
      name: name,
      description: description,
      author: author,
    );
  }

  Future<void> _generateAppJson({
    required Directory appFolder,
    required String appId,
    required String name,
    required String description,
    required String author,
    required String version,
    required String category,
    required String icon,
    String? iconPath,
    bool raspberryPiOnly = false,
  }) async {
    // Generate a unique app UUID for tracking the same app across exports/imports
    final appUuid = DateTime.now().millisecondsSinceEpoch.toString();
    final createdAt = DateTime.now().toIso8601String();

    final appJson = {
      'id': appId,
      'title': name,
      'description': description,
      'iconName': icon, // Use iconName instead of icon for consistency
      if (iconPath != null)
        'iconPath': iconPath, // Add custom icon path if provided
      'route': '/$appId',
      'category': category,
      'isEnabled': true,
      'sortOrder': 100,
      'version': version,
      'author': author,
      'appFolderPath': appFolder.path, // Use actual folder path
      'mainDartFile': '${appId}_app.dart',
      'assetFiles': [
        'config.json',
        'README.md',
        if (iconPath != null) iconPath, // Include custom icon in asset files
      ],
      'appConfig': {'setting1': 'default_value', 'enableFeature': true},
      'raspberryPiOnly': raspberryPiOnly,
      'metadata': {
        'requiresConnection': false,
        'realTimeUpdates': false,
        'features': ['Basic functionality', 'User interface'],
        'appUuid': appUuid, // Unique identifier for this specific app
        'createdAt': createdAt, // When this app was originally created
      },
      'requiredPermissions': []
    };

    final file = File(path.join(appFolder.path, 'app.json'));
    await file.writeAsString(
      const JsonEncoder.withIndent('  ').convert(appJson),
    );
  }

  Future<void> _generateMainDartFile({
    required Directory appFolder,
    required String appId,
    required String name,
    required String template,
  }) async {
    final className = _toPascalCase(appId);
    final dartContent = _getTemplateContent(template, className, name, appId);

    final file = File(path.join(appFolder.path, '${appId}_app.dart'));
    await file.writeAsString(dartContent);
  }

  Future<void> _generateConfigJson({
    required Directory appFolder,
    required String name,
    required String template,
  }) async {
    final config = {
      'name': name,
      'version': '1.0.0',
      'description': 'Configuration for $name',
      'settings': {
        'defaultValue': 'example',
        'enableFeature': true,
        'maxItems': 100
      },
      'ui': {'theme': 'auto', 'showTooltips': true},
      'permissions': {
        'systemAccess': false,
        'networkAccess': false,
        'fileAccess': false
      }
    };

    final file = File(path.join(appFolder.path, 'config.json'));
    await file.writeAsString(
      const JsonEncoder.withIndent('  ').convert(config),
    );
  }

  Future<void> _generateReadme({
    required Directory appFolder,
    required String name,
    required String description,
    required String author,
  }) async {
    final readme = '''# $name

$description

## Author
$author

## Version
1.0.0

## Features
- Basic functionality
- User-friendly interface
- Configurable settings

## Installation
This app was created using the Jelly Pi app development tools.

## Usage
Launch the app from the Jelly Pi Apps page.

## Configuration
Edit the config.json file to customize app settings.

## Development
This app was generated from a template. You can modify the source code
in the main Dart file to add new features.
''';

    final file = File(path.join(appFolder.path, 'README.md'));
    await file.writeAsString(readme);
  }

  Future<void> _generateTemplatePage({
    required Directory appFolder,
    required String appId,
    required String name,
    required String description,
    required String template,
  }) async {
    final className = _toPascalCase(appId);
    final templatePage = '''import 'package:flutter/material.dart';

class ${className}Screen extends StatefulWidget {
  const ${className}Screen({super.key});

  @override
  State<${className}Screen> createState() => _${className}ScreenState();
}

class _${className}ScreenState extends State<${className}Screen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('$name'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              ${_getTemplateIcon(template)},
              size: 64,
              color: const Color(0xFF6366F1),
            ),
            const SizedBox(height: 16),
            Text(
              '$name',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '$description',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              'This app is coming soon! Stay tuned for updates.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6366F1),
                foregroundColor: Colors.white,
              ),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}
''';

    final file = File(path.join(appFolder.path, '${appId}_screen.dart'));
    await file.writeAsString(templatePage);
  }

  String _getTemplateIcon(String template) {
    switch (template) {
      case 'system_tool':
        return 'Icons.monitor';
      case 'network_tool':
        return 'Icons.network_check';
      case 'data_viewer':
        return 'Icons.analytics';
      default:
        return 'Icons.apps';
    }
  }

  String _toPascalCase(String input) {
    return input
        .split('_')
        .map((word) =>
            word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1))
        .join('');
  }

  String _getTemplateContent(
      String template, String className, String name, String appId) {
    switch (template) {
      case 'basic':
        return _getBasicTemplate(className, name, appId);
      case 'system_tool':
        return _getSystemToolTemplate(className, name, appId);
      case 'network_tool':
        return _getNetworkToolTemplate(className, name, appId);
      case 'data_viewer':
        return _getDataViewerTemplate(className, name, appId);
      default:
        return _getBasicTemplate(className, name, appId);
    }
  }

  String _getBasicTemplate(String className, String name, String appId) {
    return '''import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../lib/services/app_state.dart';

/// $name App Entry Point
class ${className}App {
  static const String id = '$appId';
  static const String title = '$name';
  static const String version = '1.0.0';

  /// Launch the $name app
  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ${className}Screen(),
      ),
    );
  }

  /// Check if the app can run
  static bool canRun(BuildContext context) {
    return true; // Basic apps can always run
  }
}

/// $name Screen
class ${className}Screen extends StatefulWidget {
  const ${className}Screen({super.key});

  @override
  State<${className}Screen> createState() => _${className}ScreenState();
}

class _${className}ScreenState extends State<${className}Screen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('$name'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.apps,
              size: 64,
              color: Color(0xFF6366F1),
            ),
            SizedBox(height: 16),
            Text(
              '$name',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Your custom app is ready!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Edit the source code to add your functionality.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
''';
  }

  String _getSystemToolTemplate(String className, String name, String appId) {
    // Similar to basic but with system interaction capabilities
    return _getBasicTemplate(className, name, appId)
        .replaceAll(
            'Your custom app is ready!', 'System tool ready for development!')
        .replaceAll('return true; // Basic apps can always run', '''
    final appState = Provider.of<AppState>(context, listen: false);
    return appState.connectedDevices.isNotEmpty;''');
  }

  String _getNetworkToolTemplate(String className, String name, String appId) {
    // Similar to basic but with network capabilities
    return _getBasicTemplate(className, name, appId)
        .replaceAll(
            'Your custom app is ready!', 'Network tool ready for development!')
        .replaceAll('Icons.apps', 'Icons.network_check');
  }

  String _getDataViewerTemplate(String className, String name, String appId) {
    // Similar to basic but with data viewing capabilities
    return _getBasicTemplate(className, name, appId)
        .replaceAll(
            'Your custom app is ready!', 'Data viewer ready for development!')
        .replaceAll('Icons.apps', 'Icons.table_chart');
  }
}
