import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import '../models/app_module.dart';
import '../services/app_module_manager.dart';

class AppEditorService {
  /// Delete an app and its files
  Future<void> deleteApp(String appId) async {
    try {
      // First, find the app in the module manager to get its path
      final moduleManager = AppModuleManager();
      final appModule =
          moduleManager.orderedModules.where((m) => m.id == appId).firstOrNull;

      if (appModule == null) {
        throw Exception('App not found: $appId');
      }

      // Check if this is a user-imported app or built-in app
      final isUserApp =
          appModule.appFolderPath?.contains('imported_apps') == true;

      if (!isUserApp) {
        throw Exception('Cannot delete built-in apps');
      }

      // Delete the user app folder
      if (appModule.appFolderPath != null) {
        final appFolder = Directory(appModule.appFolderPath!);
        if (await appFolder.exists()) {
          await appFolder.delete(recursive: true);
          debugPrint('Deleted user app folder: $appId');
        }
      } else {
        // Fallback: try to find in user apps directory
        final appDataDir = await getApplicationSupportDirectory();
        final userAppsDir =
            Directory(path.join(appDataDir.path, 'imported_apps'));
        final appFolder = Directory(path.join(userAppsDir.path, appId));

        if (await appFolder.exists()) {
          await appFolder.delete(recursive: true);
          debugPrint('Deleted user app folder (fallback): $appId');
        }
      }

      debugPrint('Successfully deleted user app: $appId');
    } catch (e) {
      debugPrint('Error deleting app $appId: $e');
      throw Exception('Failed to delete app: $e');
    }

    // Refresh the app manager to reflect changes immediately
    await AppModuleManager().refresh();
  }

  /// Get app source code content
  Future<String> getAppSourceCode(AppModule module) async {
    if (module.appFolderPath == null || module.mainDartFile == null) {
      throw Exception('App does not have source code files');
    }

    // In a real implementation, this would read the actual file
    // For now, return a placeholder
    return '''import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../lib/services/app_state.dart';

/// ${module.title} App Entry Point
class ${_toPascalCase(module.id)}App {
  static const String id = '${module.id}';
  static const String title = '${module.title}';
  static const String version = '${module.version}';

  /// Launch the ${module.title} app
  static void launch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ${_toPascalCase(module.id)}Screen(),
      ),
    );
  }

  /// Check if the app can run
  static bool canRun(BuildContext context) {
    return true;
  }
}

/// ${module.title} Screen
class ${_toPascalCase(module.id)}Screen extends StatefulWidget {
  const ${_toPascalCase(module.id)}Screen({super.key});

  @override
  State<${_toPascalCase(module.id)}Screen> createState() => _${_toPascalCase(module.id)}ScreenState();
}

class _${_toPascalCase(module.id)}ScreenState extends State<${_toPascalCase(module.id)}Screen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('${module.title}'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.${module.iconName},
              size: 64,
              color: Color(0xFF6366F1),
            ),
            SizedBox(height: 16),
            Text(
              '${module.title}',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'App is running!',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
''';
  }

  /// Save app source code
  Future<void> saveAppSourceCode(AppModule module, String content) async {
    if (module.appFolderPath == null || module.mainDartFile == null) {
      throw Exception('App does not have source code files');
    }

    // In a real implementation, this would write to the actual file
    debugPrint('Would save source code for app: ${module.id}');
    debugPrint('Content length: ${content.length} characters');
  }

  /// Get app configuration content
  Future<String> getAppConfiguration(AppModule module) async {
    if (module.appFolderPath == null) {
      throw Exception('App does not have configuration files');
    }

    // Return the app.json content
    return const JsonEncoder.withIndent('  ').convert(module.toJson());
  }

  /// Save app configuration
  Future<void> saveAppConfiguration(AppModule module, String content) async {
    if (module.appFolderPath == null) {
      throw Exception('App does not have configuration files');
    }

    try {
      // Get the app.json file path
      final appJsonPath = path.join(module.appFolderPath!, 'app.json');
      final appJsonFile = File(appJsonPath);

      if (await appJsonFile.exists()) {
        // Write the updated JSON to the file
        await appJsonFile.writeAsString(content);

        debugPrint('Updated app configuration for ${module.id}');
        debugPrint('Content length: ${content.length} characters');

        // Small delay to ensure file operations are complete
        await Future.delayed(const Duration(milliseconds: 100));

        // Refresh the app manager to reflect changes
        await AppModuleManager().refresh();
      } else {
        throw Exception('app.json file not found at $appJsonPath');
      }
    } catch (e) {
      debugPrint('Error saving app configuration: $e');
      throw Exception('Failed to save app configuration: $e');
    }
  }

  /// Update app icon
  Future<void> updateAppIcon(AppModule module, String newIconName,
      {String? iconPath}) async {
    if (module.appFolderPath == null) {
      throw Exception('App does not have configuration files');
    }

    try {
      String? finalIconPath;

      // Handle custom icon if provided
      if (iconPath != null) {
        finalIconPath =
            await _copyCustomIcon(Directory(module.appFolderPath!), iconPath);
      } else {
        // If no iconPath provided, explicitly clear it (switching back to material icon)
        finalIconPath = null;
      }

      // Update the module with new icon
      final updatedModule = module.copyWith(
        iconName: newIconName,
        iconPath: finalIconPath,
        clearIconPath: iconPath == null &&
            module.hasCustomIcon, // Clear if switching from custom to material
      );

      // Get the app.json file path
      final appJsonPath = path.join(module.appFolderPath!, 'app.json');
      final appJsonFile = File(appJsonPath);

      if (await appJsonFile.exists()) {
        // Write the updated JSON to the file
        final jsonContent =
            const JsonEncoder.withIndent('  ').convert(updatedModule.toJson());
        await appJsonFile.writeAsString(jsonContent);

        debugPrint('Updated app icon for ${module.id} to $newIconName');

        // Small delay to ensure file operations are complete
        await Future.delayed(const Duration(milliseconds: 100));

        // Refresh the app manager to reflect changes
        await AppModuleManager().refresh();
      } else {
        throw Exception('app.json file not found at $appJsonPath');
      }
    } catch (e) {
      debugPrint('Error updating app icon: $e');
      throw Exception('Failed to update app icon: $e');
    }
  }

  /// Copy custom icon to app folder and return relative path
  Future<String> _copyCustomIcon(Directory appFolder, String iconPath) async {
    final sourceFile = File(iconPath);
    if (!await sourceFile.exists()) {
      throw Exception('Custom icon file does not exist: $iconPath');
    }

    // Get file extension
    final extension = path.extension(iconPath);

    // Add timestamp to ensure unique filename for each update
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final iconFileName = 'icon_$timestamp$extension';

    // Remove old icon files to prevent accumulation
    await _cleanupOldIcons(appFolder);

    // Copy to app folder
    final targetFile = File(path.join(appFolder.path, iconFileName));
    await sourceFile.copy(targetFile.path);

    debugPrint('Copied custom icon to: ${targetFile.path}');
    return iconFileName; // Return relative path within app folder
  }

  /// Clean up old icon files to prevent accumulation
  Future<void> _cleanupOldIcons(Directory appFolder) async {
    try {
      final files = await appFolder.list().toList();
      for (final file in files) {
        if (file is File) {
          final fileName = path.basename(file.path);
          // Remove old icon files (both timestamped and non-timestamped)
          if (fileName.startsWith('icon') &&
              (fileName.contains('_') ||
                  fileName == 'icon.png' ||
                  fileName == 'icon.jpg' ||
                  fileName == 'icon.jpeg' ||
                  fileName == 'icon.gif' ||
                  fileName == 'icon.webp')) {
            await file.delete();
            debugPrint('Cleaned up old icon file: ${file.path}');
          }
        }
      }
    } catch (e) {
      debugPrint('Error cleaning up old icons: $e');
      // Don't throw - this is just cleanup
    }
  }

  /// Get app config.json content
  Future<String> getAppConfigJson(AppModule module) async {
    if (module.appFolderPath == null) {
      throw Exception('App does not have config files');
    }

    // Return a sample config.json
    final config = {
      'name': module.title,
      'version': module.version,
      'description': module.description,
      'settings': module.appConfig,
      'ui': {'theme': 'auto', 'showTooltips': true},
      'permissions': {
        'systemAccess': false,
        'networkAccess': false,
        'fileAccess': false
      }
    };

    return const JsonEncoder.withIndent('  ').convert(config);
  }

  /// Save app config.json
  Future<void> saveAppConfigJson(AppModule module, String content) async {
    if (module.appFolderPath == null) {
      throw Exception('App does not have config files');
    }

    // In a real implementation, this would write to the actual file
    debugPrint('Would save config.json for app: ${module.id}');
    debugPrint('Content length: ${content.length} characters');
  }

  /// Get list of app files
  Future<List<AppFileInfo>> getAppFiles(AppModule module) async {
    if (module.appFolderPath == null) {
      throw Exception('App does not have a folder path');
    }

    // Return a list of files that would be in the app folder
    return [
      AppFileInfo(
        name: 'app.json',
        type: AppFileType.configuration,
        size: 1024,
        lastModified: DateTime.now().subtract(const Duration(days: 1)),
      ),
      if (module.mainDartFile != null)
        AppFileInfo(
          name: module.mainDartFile!,
          type: AppFileType.source,
          size: 2048,
          lastModified: DateTime.now().subtract(const Duration(hours: 2)),
        ),
      AppFileInfo(
        name: 'config.json',
        type: AppFileType.configuration,
        size: 512,
        lastModified: DateTime.now().subtract(const Duration(hours: 6)),
      ),
      AppFileInfo(
        name: 'README.md',
        type: AppFileType.documentation,
        size: 1536,
        lastModified: DateTime.now().subtract(const Duration(days: 2)),
      ),
      ...module.assetFiles.map((file) => AppFileInfo(
            name: file,
            type: _getFileType(file),
            size: 256,
            lastModified: DateTime.now().subtract(const Duration(hours: 12)),
          )),
    ];
  }

  /// Get file content
  Future<String> getFileContent(AppModule module, String fileName) async {
    if (module.appFolderPath == null) {
      throw Exception('App does not have a folder path');
    }

    // Return placeholder content based on file type
    if (fileName.endsWith('.json')) {
      return '{\n  "placeholder": "content"\n}';
    } else if (fileName.endsWith('.dart')) {
      return await getAppSourceCode(module);
    } else if (fileName.endsWith('.md')) {
      return '''# ${module.title}

${module.description}

## Version
${module.version}

## Author
${module.author}

## Features
- Feature 1
- Feature 2
- Feature 3
''';
    } else {
      return 'File content placeholder';
    }
  }

  /// Save file content
  Future<void> saveFileContent(
      AppModule module, String fileName, String content) async {
    if (module.appFolderPath == null) {
      throw Exception('App does not have a folder path');
    }

    // In a real implementation, this would write to the actual file
    debugPrint('Would save file: $fileName for app: ${module.id}');
    debugPrint('Content length: ${content.length} characters');
  }

  String _toPascalCase(String input) {
    return input
        .split('_')
        .map((word) =>
            word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1))
        .join('');
  }

  AppFileType _getFileType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();
    switch (extension) {
      case '.dart':
        return AppFileType.source;
      case '.json':
        return AppFileType.configuration;
      case '.md':
        return AppFileType.documentation;
      case '.png':
      case '.jpg':
      case '.jpeg':
      case '.gif':
        return AppFileType.image;
      case '.css':
      case '.js':
        return AppFileType.asset;
      default:
        return AppFileType.other;
    }
  }
}

class AppFileInfo {
  final String name;
  final AppFileType type;
  final int size;
  final DateTime lastModified;

  AppFileInfo({
    required this.name,
    required this.type,
    required this.size,
    required this.lastModified,
  });
}

enum AppFileType {
  source,
  configuration,
  documentation,
  image,
  asset,
  other,
}
