import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dartssh2/dartssh2.dart';
import 'package:xterm/xterm.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:io';

// Device model
class Device {
  final String id;
  final String ip;
  final String mac;
  final String vendor;
  String name;
  DeviceStatus status;
  bool isConnected;
  bool connectOnLaunch;
  bool isDefaultDevice;
  DateTime? lastSeen;
  Map<String, dynamic> systemInfo;
  String operatingSystem;
  int port;

  Device({
    required this.id,
    required this.ip,
    required this.mac,
    required this.vendor,
    required this.name,
    this.status = DeviceStatus.unknown,
    this.isConnected = false,
    this.connectOnLaunch = false,
    this.isDefaultDevice = false,
    this.lastSeen,
    this.systemInfo = const {},
    this.operatingSystem = 'Unknown',
    this.port = 22,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'ip': ip,
        'mac': mac,
        'vendor': vendor,
        'name': name,
        'status': status.name,
        'isConnected': isConnected,
        'connectOnLaunch': connectOnLaunch,
        'isDefaultDevice': isDefaultDevice,
        'lastSeen': lastSeen?.toIso8601String(),
        'systemInfo': systemInfo,
        'operatingSystem': operatingSystem,
        'port': port,
      };

  factory Device.fromJson(Map<String, dynamic> json) => Device(
        id: json['id'],
        ip: json['ip'],
        mac: json['mac'],
        vendor: json['vendor'],
        name: json['name'],
        status: DeviceStatus.values.firstWhere(
          (e) => e.name == json['status'],
          orElse: () => DeviceStatus.unknown,
        ),
        isConnected: json['isConnected'] ?? false,
        connectOnLaunch: json['connectOnLaunch'] ?? false,
        isDefaultDevice: json['isDefaultDevice'] ?? false,
        lastSeen:
            json['lastSeen'] != null ? DateTime.parse(json['lastSeen']) : null,
        systemInfo: json['systemInfo'] ?? {},
        operatingSystem: json['operatingSystem'] ?? 'Unknown',
        port: json['port'] ?? 22,
      );

  Device copyWith({
    String? ip,
    String? name,
    DeviceStatus? status,
    bool? isConnected,
    bool? connectOnLaunch,
    bool? isDefaultDevice,
    DateTime? lastSeen,
    Map<String, dynamic>? systemInfo,
    String? operatingSystem,
    int? port,
  }) {
    return Device(
      id: id,
      ip: ip ?? this.ip,
      mac: mac,
      vendor: vendor,
      name: name ?? this.name,
      status: status ?? this.status,
      isConnected: isConnected ?? this.isConnected,
      connectOnLaunch: connectOnLaunch ?? this.connectOnLaunch,
      isDefaultDevice: isDefaultDevice ?? this.isDefaultDevice,
      lastSeen: lastSeen ?? this.lastSeen,
      systemInfo: systemInfo ?? this.systemInfo,
      operatingSystem: operatingSystem ?? this.operatingSystem,
      port: port ?? this.port,
    );
  }

  // Helper methods for system info
  bool get hasSystemInfo =>
      systemInfo.isNotEmpty && systemInfo['timestamp'] != null;

  // CPU Information
  Map<String, dynamic>? get cpuInfo => systemInfo['cpu'];
  double? get cpuUsage =>
      cpuInfo?['overall']?.toDouble() ?? systemInfo['cpu_usage']?.toDouble();
  Map<String, dynamic>? get cpuCores => cpuInfo?['cores'];
  double? get cpuFrequency => cpuInfo?['frequency_mhz']?.toDouble();

  // Memory Information
  Map<String, dynamic>? get memoryInfo => systemInfo['memory'];

  // Temperature Information
  Map<String, dynamic>? get temperatureInfo => systemInfo['temperature'];
  double? get temperature =>
      temperatureInfo?['cpu']?.toDouble() ??
      systemInfo['temperature']?.toDouble();
  double? get gpuTemperature => temperatureInfo?['gpu']?.toDouble();

  // GPU Information
  Map<String, dynamic>? get gpuInfo => systemInfo['gpu'];
  int? get gpuMemory => gpuInfo?['memory_mb'];
  double? get gpuFrequency => gpuInfo?['frequency_mhz']?.toDouble();

  // Power Information
  Map<String, dynamic>? get powerInfo => systemInfo['power'];
  bool? get isThrottled => powerInfo?['throttled'];
  double? get coreVoltage => powerInfo?['voltage_core']?.toDouble();

  // Storage Information
  Map<String, dynamic>? get diskInfo => systemInfo['disk'];

  // System Information
  String? get uptime => systemInfo['uptime'];
  Map<String, dynamic>? get loadAverage => systemInfo['load_average'];
  List<dynamic>? get networkInterfaces => systemInfo['network_interfaces'];

  DateTime? get lastSystemInfoUpdate {
    final timestamp = systemInfo['timestamp'];
    if (timestamp != null) {
      return DateTime.fromMillisecondsSinceEpoch((timestamp * 1000).round());
    }
    return null;
  }

  String get healthStatus {
    if (!hasSystemInfo) return 'No data';

    final cpu = cpuUsage;
    final memory = memoryInfo;
    final temp = temperature;

    List<String> issues = [];

    if (cpu != null && cpu > 80) issues.add('High CPU');
    if (memory != null && memory['usage_percent'] > 85) {
      issues.add('High Memory');
    }
    if (temp != null && temp > 70) issues.add('High Temp');

    if (issues.isEmpty) return 'Good';
    if (issues.length == 1) return 'Warning';
    return 'Critical';
  }
}

enum DeviceStatus {
  unknown,
  online,
  offline,
  connecting,
  connected,
  disconnecting,
  error,
  rebooting,
  shuttingDown,
}

// Device operation states for tracking ongoing operations
enum DeviceOperationState {
  none,
  rebooting,
  shuttingDown,
  updating,
}

// Device operation tracking
class DeviceOperation {
  final String deviceId;
  final DeviceOperationState state;
  final DateTime startTime;
  final String? description;

  DeviceOperation({
    required this.deviceId,
    required this.state,
    required this.startTime,
    this.description,
  });
}

// Process state enum for tracking process actions
enum ProcessState {
  normal,
  suspended,
  terminated,
  killed,
}

// SSH Connection model
class SSHConnection {
  final String deviceId;
  final String deviceIp;
  final String username;
  SSHClient? client;
  SSHSession? session;
  bool isConnected = false;
  bool isTerminalReady = false;
  String? error;
  StreamController<String>? _outputController;
  Stream<String>? outputStream;
  List<String> commandHistory = [];
  Terminal? terminal;
  int? lastKnownWidth;
  int? lastKnownHeight;

  SSHConnection({
    required this.deviceId,
    required this.deviceIp,
    required this.username,
  }) {
    _outputController = StreamController<String>.broadcast();
    outputStream = _outputController?.stream;
  }

  void addOutput(String output) {
    _outputController?.add(output);
  }

  void addCommand(String command) {
    commandHistory.add(command);
    if (commandHistory.length > 100) {
      commandHistory.removeAt(0);
    }
  }

  Future<void> dispose() async {
    await _outputController?.close();
    session?.close();
    client?.close();
  }
}

// Main App State
class AppState extends ChangeNotifier {
  final List<Device> _devices = [];
  final Map<String, SSHConnection> _sshConnections = {};
  final Map<String, Terminal> _deviceTerminals =
      {}; // Store terminal per device
  final Map<String, bool> _terminalNeedsInitialPrompt =
      {}; // Track which terminals need initial prompt
  final List<String> _terminalOutput = [];
  late SharedPreferences _prefs;

  // UI Settings
  double _terminalFontSize = 18.0; // Default larger font size
  String _terminalFontFamily = 'Menlo'; // Default font family

  // App Hub terminal output persistence
  final List<String> _appHubTerminalOutput = [];

  // Error fix dialog callback
  Future<bool> Function(String error, String fix)? showErrorFixDialog;

  // Terminal color settings
  Color _terminalBackground = const Color(0xFF0D1117); // GitHub dark background
  Color _terminalForeground = const Color(0xFFE8EAED); // Soft white text
  Color _terminalCursor = const Color(0xFF64FFDA); // Bright cyan cursor
  Color _terminalSelection =
      const Color(0x4D3F51B5); // Semi-transparent selection
  Color _terminalBold = const Color(0xFFFFFFFF); // Bold text color

  // Standard ANSI colors
  final Color _terminalBlack = const Color(0xFF1E1E1E); // Black
  final Color _terminalRed = const Color(0xFFFF6B6B); // Red (errors)
  final Color _terminalGreen = const Color(0xFF4ECDC4); // Green (success)
  final Color _terminalYellow = const Color(0xFFFFE66D); // Yellow (warnings)
  final Color _terminalBlue =
      const Color(0xFF4FC3F7); // Blue (info/directories)
  final Color _terminalMagenta = const Color(0xFFBA68C8); // Magenta
  final Color _terminalCyan = const Color(0xFF26C6DA); // Cyan
  final Color _terminalWhite = const Color(0xFFE8EAED); // White

  // Bright ANSI colors
  final Color _terminalBrightBlack =
      const Color(0xFF6C7B7F); // Bright black (gray)
  final Color _terminalBrightRed = const Color(0xFFFF8A80); // Bright red
  final Color _terminalBrightGreen = const Color(0xFF69F0AE); // Bright green
  final Color _terminalBrightYellow = const Color(0xFFFFFF8D); // Bright yellow
  final Color _terminalBrightBlue = const Color(0xFF80D8FF); // Bright blue
  final Color _terminalBrightMagenta =
      const Color(0xFFE1BEE7); // Bright magenta
  final Color _terminalBrightCyan = const Color(0xFF84FFFF); // Bright cyan
  final Color _terminalBrightWhite = const Color(0xFFFFFFFF); // Bright white

  // Current state
  int _currentPageIndex = 0;
  String? _selectedDeviceId;
  String?
      _defaultDeviceId; // Store default device ID separately for easy access
  bool _isScanning = false;
  bool _isManualSelection = false; // Track if user manually selected a device

  // Connection monitoring
  Timer? _connectionMonitorTimer;
  final Set<String> _notifiedDisconnections =
      {}; // Track which devices we've already notified about

  // Terminal resize debouncing
  final Map<String, Timer?> _resizeTimers = {};

  // UI Settings
  bool _defaultGridView = false;
  bool _autoRefreshEnabled = true;
  int _autoRefreshInterval = 30; // seconds
  int _appsGridColumns = 4; // Default to 4 columns
  bool _developerModeEnabled = false; // Developer mode toggle

  // Process State Management - Global state that persists across navigation
  final Map<String, Map<int, ProcessState>> _deviceProcessStates =
      {}; // deviceId -> {pid -> state}

  // Getters
  List<Device> get devices => List.unmodifiable(_devices);
  Map<String, SSHConnection> get sshConnections =>
      Map.unmodifiable(_sshConnections);
  List<String> get terminalOutput => List.unmodifiable(_terminalOutput);

  // App Hub terminal output
  List<String> get appHubTerminalOutput =>
      List.unmodifiable(_appHubTerminalOutput);

  void addAppHubTerminalOutput(String output) {
    _appHubTerminalOutput.add(output);
    notifyListeners();
  }

  void clearAppHubTerminalOutput() {
    _appHubTerminalOutput.clear();
    notifyListeners();
  }

  int get currentPageIndex => _currentPageIndex;
  String? get selectedDeviceId => _selectedDeviceId;
  String? get defaultDeviceId => _defaultDeviceId;
  bool get isScanning => _isScanning;

  // UI Settings Getters
  bool get defaultGridView => _defaultGridView;
  bool get autoRefreshEnabled => _autoRefreshEnabled;
  int get autoRefreshInterval => _autoRefreshInterval;
  int get appsGridColumns => _appsGridColumns;
  bool get developerModeEnabled => _developerModeEnabled;
  double get terminalFontSize => _terminalFontSize;
  String get terminalFontFamily => _terminalFontFamily;
  Color get terminalBackground => _terminalBackground;
  Color get terminalForeground => _terminalForeground;
  Color get terminalCursor => _terminalCursor;
  Color get terminalSelection => _terminalSelection;
  Color get terminalBold => _terminalBold;

  // Standard ANSI colors
  Color get terminalBlack => _terminalBlack;
  Color get terminalRed => _terminalRed;
  Color get terminalGreen => _terminalGreen;
  Color get terminalYellow => _terminalYellow;
  Color get terminalBlue => _terminalBlue;
  Color get terminalMagenta => _terminalMagenta;
  Color get terminalCyan => _terminalCyan;
  Color get terminalWhite => _terminalWhite;

  // Bright ANSI colors
  Color get terminalBrightBlack => _terminalBrightBlack;
  Color get terminalBrightRed => _terminalBrightRed;
  Color get terminalBrightGreen => _terminalBrightGreen;
  Color get terminalBrightYellow => _terminalBrightYellow;
  Color get terminalBrightBlue => _terminalBrightBlue;
  Color get terminalBrightMagenta => _terminalBrightMagenta;
  Color get terminalBrightCyan => _terminalBrightCyan;
  Color get terminalBrightWhite => _terminalBrightWhite;

  Device? get selectedDevice => _selectedDeviceId != null
      ? _devices.where((d) => d.id == _selectedDeviceId).firstOrNull
      : null;

  Device? get defaultDevice => _defaultDeviceId != null
      ? _devices.where((d) => d.id == _defaultDeviceId).firstOrNull
      : null;

  SSHConnection? get currentSSHConnection =>
      _selectedDeviceId != null ? _sshConnections[_selectedDeviceId] : null;

  // Get SSH connection for a specific device
  SSHConnection? getSSHConnection(String deviceId) => _sshConnections[deviceId];

  // Check if a specific device is connected
  bool isDeviceConnected(String deviceId) {
    return _sshConnections.containsKey(deviceId) &&
        _sshConnections[deviceId]?.isConnected == true;
  }

  // Get all devices that have active SSH connections
  List<Device> get connectedDevices => _devices
      .where((device) =>
          _sshConnections.containsKey(device.id) &&
          _sshConnections[device.id]?.isConnected == true)
      .toList();

  AppState() {
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadUISettings();
    await loadDevices();
    _startConnectionMonitoring();
    notifyListeners();
  }

  @override
  void dispose() {
    _connectionMonitorTimer?.cancel();
    // Cancel all resize timers
    for (final timer in _resizeTimers.values) {
      timer?.cancel();
    }
    _resizeTimers.clear();
    super.dispose();
  }

  // UI Settings Management
  Future<void> _loadUISettings() async {
    _defaultGridView = _prefs.getBool('default_grid_view') ?? false;
    _autoRefreshEnabled = _prefs.getBool('auto_refresh_enabled') ?? true;
    _autoRefreshInterval = _prefs.getInt('auto_refresh_interval') ?? 30;
    _appsGridColumns = _prefs.getInt('apps_grid_columns') ?? 4;
    _developerModeEnabled = _prefs.getBool('developer_mode_enabled') ?? false;
    _terminalFontSize = _prefs.getDouble('terminal_font_size') ?? 18.0;
    _terminalFontFamily = _prefs.getString('terminal_font_family') ?? 'Menlo';

    // Load default device ID
    _defaultDeviceId = _prefs.getString('default_device_id');

    // Load terminal colors
    _terminalBackground =
        Color(_prefs.getInt('terminal_background') ?? 0xFF0D1117);
    _terminalForeground =
        Color(_prefs.getInt('terminal_foreground') ?? 0xFFE8EAED);
    _terminalCursor = Color(_prefs.getInt('terminal_cursor') ?? 0xFF64FFDA);
  }

  Future<void> _saveUISettings() async {
    await _prefs.setBool('default_grid_view', _defaultGridView);
    await _prefs.setBool('auto_refresh_enabled', _autoRefreshEnabled);
    await _prefs.setInt('auto_refresh_interval', _autoRefreshInterval);
    await _prefs.setInt('apps_grid_columns', _appsGridColumns);
    await _prefs.setBool('developer_mode_enabled', _developerModeEnabled);
    await _prefs.setDouble('terminal_font_size', _terminalFontSize);
    await _prefs.setString('terminal_font_family', _terminalFontFamily);

    // Save default device ID
    if (_defaultDeviceId != null) {
      await _prefs.setString('default_device_id', _defaultDeviceId!);
    } else {
      await _prefs.remove('default_device_id');
    }

    // Save terminal colors
    // Save terminal colors (using value property - deprecated but no alternative yet)
    // ignore: deprecated_member_use
    await _prefs.setInt('terminal_background', _terminalBackground.value);
    // ignore: deprecated_member_use
    await _prefs.setInt('terminal_foreground', _terminalForeground.value);
    // ignore: deprecated_member_use
    await _prefs.setInt('terminal_cursor', _terminalCursor.value);
  }

  Future<void> setDefaultGridView(bool value) async {
    _defaultGridView = value;
    await _saveUISettings();
    notifyListeners();
  }

  Future<void> setAutoRefreshEnabled(bool value) async {
    _autoRefreshEnabled = value;
    await _saveUISettings();

    // Restart connection monitoring with new settings
    if (value) {
      _startConnectionMonitoring();
    } else {
      _stopConnectionMonitoring();
    }

    notifyListeners();
  }

  Future<void> setAutoRefreshInterval(int value) async {
    _autoRefreshInterval = value;
    await _saveUISettings();

    // Restart connection monitoring with new interval
    if (_autoRefreshEnabled) {
      _startConnectionMonitoring();
    }

    notifyListeners();
  }

  Future<void> setAppsGridColumns(int value) async {
    _appsGridColumns = value;
    await _saveUISettings();
    notifyListeners();
  }

  Future<void> setDeveloperModeEnabled(bool enabled) async {
    _developerModeEnabled = enabled;
    await _saveUISettings();
    notifyListeners();
  }

  Future<void> setTerminalFontSize(double value) async {
    _terminalFontSize = value;
    await _saveUISettings();
    notifyListeners();
  }

  Future<void> setTerminalFontFamily(String value) async {
    _terminalFontFamily = value;
    await _saveUISettings();
    notifyListeners();
  }

  Future<void> setTerminalBackground(Color value) async {
    _terminalBackground = value;
    await _saveUISettings();
    notifyListeners();
  }

  Future<void> setTerminalForeground(Color value) async {
    _terminalForeground = value;
    await _saveUISettings();
    notifyListeners();
  }

  Future<void> setTerminalCursor(Color value) async {
    _terminalCursor = value;
    await _saveUISettings();
    notifyListeners();
  }

  Future<void> setTerminalSelection(Color value) async {
    _terminalSelection = value;
    await _saveUISettings();
    notifyListeners();
  }

  Future<void> setTerminalBold(Color value) async {
    _terminalBold = value;
    await _saveUISettings();
    notifyListeners();
  }

  // Navigation
  void setCurrentPageIndex(int index) {
    _currentPageIndex = index;
    notifyListeners();
  }

  // Device Management
  Future<void> loadDevices() async {
    final devicesJson = _prefs.getStringList('devices') ?? [];
    _devices.clear();
    _devices
        .addAll(devicesJson.map((json) => Device.fromJson(jsonDecode(json))));

    // Reset connection status on app launch - devices are not connected until SSH is established
    for (int i = 0; i < _devices.length; i++) {
      _devices[i] = _devices[i].copyWith(
        isConnected: false,
        status: DeviceStatus.unknown,
        systemInfo: {}, // Clear any stale system info
      );
    }

    // Restore default device flag based on stored default device ID
    if (_defaultDeviceId != null) {
      final defaultIndex = _devices.indexWhere((d) => d.id == _defaultDeviceId);
      if (defaultIndex != -1) {
        _devices[defaultIndex] =
            _devices[defaultIndex].copyWith(isDefaultDevice: true);
        debugPrint('Restored default device: ${_devices[defaultIndex].name}');
      } else {
        // Default device no longer exists, clear the stored ID
        _defaultDeviceId = null;
        await _saveUISettings();
        debugPrint(
            'Default device no longer exists, cleared default device ID');
      }
    }

    // Check status for all devices
    for (final device in _devices) {
      _checkDeviceStatus(device);
    }

    // Auto-connect to devices marked for connect on launch
    for (final device in _devices) {
      if (device.connectOnLaunch) {
        _autoConnectDevice(device);
      }
    }

    // Immediately try to select the default device if it exists (even if not connected yet)
    // This ensures the dropdown shows the correct device on app launch
    if (_defaultDeviceId != null && _selectedDeviceId == null) {
      final defaultDevice =
          _devices.where((d) => d.id == _defaultDeviceId).firstOrNull;
      if (defaultDevice != null) {
        _selectedDeviceId = _defaultDeviceId;
        _isManualSelection = false; // This is an automatic selection
        debugPrint(
            'Pre-selected default device on launch: ${defaultDevice.name}');
      }
    }

    // After auto-connecting, try to auto-select the best device (prioritizing default)
    // Use a small delay to allow connections to establish and potentially switch to connected devices
    Future.delayed(const Duration(milliseconds: 500), () {
      autoSelectBestDevice();
    });

    notifyListeners();
  }

  Future<void> _saveDevices() async {
    final devicesJson = _devices.map((d) => jsonEncode(d.toJson())).toList();
    await _prefs.setStringList('devices', devicesJson);
  }

  Future<void> addDevice(Device device) async {
    _devices.add(device);
    await _saveDevices();
    await _checkDeviceStatus(device);
    notifyListeners();
  }

  Future<void> removeDevice(String deviceId) async {
    // Check if this was the selected device
    final wasSelectedDevice = _selectedDeviceId == deviceId;

    // Check if this was the default device
    final wasDefaultDevice = _defaultDeviceId == deviceId;

    _devices.removeWhere((d) => d.id == deviceId);
    await _saveDevices();

    // Disconnect if connected
    if (_sshConnections.containsKey(deviceId)) {
      final connection = _sshConnections[deviceId];
      if (connection != null) {
        await connection.dispose();
        _sshConnections.remove(deviceId);
      }
    }

    // Remove device terminal and prompt tracking
    _deviceTerminals.remove(deviceId);
    _terminalNeedsInitialPrompt.remove(deviceId);

    // Clear default device if this was the default
    if (wasDefaultDevice) {
      _defaultDeviceId = null;
      await _saveUISettings(); // Save the cleared default device ID
    }

    // If this was the selected device, auto-switch to another device
    if (wasSelectedDevice) {
      debugPrint(
          'Selected device $deviceId was removed, switching to next available device');
      _selectedDeviceId = null;
      _isManualSelection =
          false; // Reset manual selection flag to allow auto-switching

      // Auto-select the best available device
      autoSelectBestDevice();
    }

    notifyListeners();
  }

  Future<void> updateDevice(Device updatedDevice) async {
    final index = _devices.indexWhere((d) => d.id == updatedDevice.id);
    if (index != -1) {
      _devices[index] = updatedDevice;
      await _saveDevices();
      notifyListeners();
    }
  }

  // Set a device as the default device
  Future<void> setDefaultDevice(String deviceId) async {
    // Clear any existing default device flags
    for (int i = 0; i < _devices.length; i++) {
      _devices[i] = _devices[i].copyWith(isDefaultDevice: false);
    }

    // Set the new default device
    final index = _devices.indexWhere((d) => d.id == deviceId);
    if (index != -1) {
      _devices[index] = _devices[index].copyWith(isDefaultDevice: true);
      _defaultDeviceId = deviceId; // Store the default device ID

      // Immediately select this device as the current device if it's connected
      if (_sshConnections.containsKey(deviceId) &&
          _sshConnections[deviceId]?.isConnected == true) {
        _selectedDeviceId = deviceId;
        debugPrint('Switched to default device: ${_devices[index].name}');
      } else {
        // If not connected, still try to auto-select the best device
        autoSelectBestDevice();
      }
    }

    await _saveDevices();
    await _saveUISettings(); // Save the default device ID
    notifyListeners();
  }

  void selectDevice(String? deviceId) {
    _selectedDeviceId = deviceId;
    _isManualSelection = true; // Mark as manual selection
    notifyListeners();
  }

  // Get the default device if it exists and is connected
  Device? getDefaultConnectedDevice() {
    if (_defaultDeviceId != null &&
        _sshConnections.containsKey(_defaultDeviceId!) &&
        _sshConnections[_defaultDeviceId!]?.isConnected == true) {
      return _devices.where((d) => d.id == _defaultDeviceId).firstOrNull;
    }
    return null;
  }

  // Auto-select default device if no device is currently selected
  void autoSelectDefaultDevice() {
    if (_selectedDeviceId == null && _defaultDeviceId != null) {
      // Check if the default device is connected
      if (_sshConnections.containsKey(_defaultDeviceId!) &&
          _sshConnections[_defaultDeviceId!]?.isConnected == true) {
        final defaultDevice =
            _devices.where((d) => d.id == _defaultDeviceId).firstOrNull;

        if (defaultDevice != null) {
          _selectedDeviceId = _defaultDeviceId;
          debugPrint('Auto-selected default device: ${defaultDevice.name}');
          notifyListeners();
        }
      }
    }
  }

  // Get the best device to select (prioritizing default device)
  Device? getBestDeviceToSelect() {
    final connectedDevices = this.connectedDevices;

    if (connectedDevices.isEmpty) {
      return null;
    }

    // First priority: default device if it's connected
    if (_defaultDeviceId != null) {
      final defaultDevice =
          connectedDevices.where((d) => d.id == _defaultDeviceId).firstOrNull;
      if (defaultDevice != null) {
        debugPrint('Selected default device: ${defaultDevice.name}');
        return defaultDevice;
      }
    }

    // Second priority: currently selected device if it's still connected
    if (_selectedDeviceId != null) {
      final currentDevice =
          connectedDevices.where((d) => d.id == _selectedDeviceId).firstOrNull;
      if (currentDevice != null) {
        return currentDevice;
      }
    }

    // Last resort: first connected device
    debugPrint(
        'Selected first available device: ${connectedDevices.first.name}');
    return connectedDevices.first;
  }

  // Enhanced auto-select that tries to select the best device (respects manual selections)
  void autoSelectBestDevice() {
    debugPrint(
        'autoSelectBestDevice called - current selected: $_selectedDeviceId, default: $_defaultDeviceId, isManual: $_isManualSelection');

    // Don't override manual selections unless the selected device is disconnected
    if (_isManualSelection && _selectedDeviceId != null) {
      final selectedDevice =
          _devices.where((d) => d.id == _selectedDeviceId).firstOrNull;
      if (selectedDevice != null && selectedDevice.isConnected) {
        debugPrint('Keeping manually selected device: ${selectedDevice.name}');
        return;
      } else {
        debugPrint(
            'Manually selected device is no longer connected, resetting manual flag');
        _isManualSelection = false;
      }
    }

    // PRIORITY 1: If we have a default device, always prefer it when it's connected
    if (_defaultDeviceId != null) {
      final connectedDevices = this.connectedDevices;
      final defaultDevice =
          connectedDevices.where((d) => d.id == _defaultDeviceId).firstOrNull;

      if (defaultDevice != null) {
        // Default device is connected - always select it (unless manually overridden)
        if (_selectedDeviceId != _defaultDeviceId) {
          _selectedDeviceId = _defaultDeviceId;
          _isManualSelection = false;
          debugPrint(
              'Switched to connected default device: ${defaultDevice.name}');
          notifyListeners();
        } else {
          debugPrint(
              'Default device already selected and connected: ${defaultDevice.name}');
        }
        return;
      } else if (_selectedDeviceId == _defaultDeviceId) {
        // Default device is selected but not connected - keep it selected
        debugPrint(
            'Keeping default device selected even though not connected: ${_devices.where((d) => d.id == _defaultDeviceId).firstOrNull?.name ?? 'Unknown'}');
        return;
      }
    }

    // PRIORITY 2: If no default device or default device not available, select best connected device
    final bestDevice = getBestDeviceToSelect();
    if (bestDevice != null && _selectedDeviceId != bestDevice.id) {
      _selectedDeviceId = bestDevice.id;
      _isManualSelection = false; // This is an automatic selection
      debugPrint('Auto-selected best available device: ${bestDevice.name}');
      notifyListeners();
    } else if (bestDevice == null) {
      // PRIORITY 3: If no connected devices, select the first available device (if any)
      if (_devices.isNotEmpty && _selectedDeviceId == null) {
        _selectedDeviceId = _devices.first.id;
        _isManualSelection = false;
        debugPrint(
            'No connected devices available, selected first available device: ${_devices.first.name}');
        notifyListeners();
      } else if (_selectedDeviceId != null) {
        debugPrint('No connected devices available, keeping current selection');
      }
    }
  }

  // Switch terminal to a different connected device
  void switchTerminalDevice(String deviceId) {
    // Only allow switching to connected devices
    if (_sshConnections.containsKey(deviceId) &&
        _sshConnections[deviceId]?.isConnected == true) {
      _selectedDeviceId = deviceId;
      _isManualSelection = true; // Mark as manual selection
      notifyListeners();
    }
  }

  // Auto-switch to the next available connected device (prioritize default device)
  void _autoSwitchToNextConnectedDevice() {
    debugPrint('_autoSwitchToNextConnectedDevice called');
    final connectedDevices = this.connectedDevices;
    debugPrint(
        'Available connected devices: ${connectedDevices.map((d) => d.name).join(', ')}');

    if (connectedDevices.isNotEmpty) {
      // Clear the current selection first
      _selectedDeviceId = null;
      debugPrint('Cleared current device selection');

      // Now auto-select the best device
      autoSelectBestDevice();
    } else {
      debugPrint('No connected devices available for auto-switching');
      _selectedDeviceId = null;
      notifyListeners();
    }
  }

  // Track ongoing device operations (replaces manual status tracking)
  final Map<String, DeviceOperation> _deviceOperations = {};

  // Start a device operation (reboot, shutdown, update)
  void startDeviceOperation(
      String deviceId, DeviceOperationState operationState,
      {String? description}) {
    _deviceOperations[deviceId] = DeviceOperation(
      deviceId: deviceId,
      state: operationState,
      startTime: DateTime.now(),
      description: description,
    );

    // Set up automatic cleanup based on operation type
    if (operationState == DeviceOperationState.rebooting) {
      // For reboot: after 1 minute, start checking for reconnection
      Timer(const Duration(minutes: 1), () {
        if (_deviceOperations[deviceId]?.state ==
            DeviceOperationState.rebooting) {
          // Clear operation and let normal status checking resume
          _deviceOperations.remove(deviceId);
          notifyListeners();
          // Start checking for device to come back online
          refreshDeviceStatus(deviceId);
        }
      });
    } else if (operationState == DeviceOperationState.shuttingDown) {
      // For shutdown: after 1 minute, clear operation and mark as offline
      Timer(const Duration(minutes: 1), () {
        if (_deviceOperations[deviceId]?.state ==
            DeviceOperationState.shuttingDown) {
          _deviceOperations.remove(deviceId);
          // Force device to offline status
          final deviceIndex = _devices.indexWhere((d) => d.id == deviceId);
          if (deviceIndex != -1) {
            _devices[deviceIndex] = _devices[deviceIndex].copyWith(
              status: DeviceStatus.offline,
              isConnected: false,
            );
          }
          notifyListeners();
        }
      });
    }
    // Note: Update operations are cleared manually when the update completes

    notifyListeners();
  }

  // Clear a device operation
  void clearDeviceOperation(String deviceId) {
    _deviceOperations.remove(deviceId);
    notifyListeners();
  }

  // Get current operation for a device
  DeviceOperation? getDeviceOperation(String deviceId) {
    return _deviceOperations[deviceId];
  }

  // Check if device has an ongoing operation
  bool hasDeviceOperation(String deviceId) {
    return _deviceOperations.containsKey(deviceId);
  }

  // Device Status Checking
  Future<void> _checkDeviceStatus(Device device) async {
    final index = _devices.indexWhere((d) => d.id == device.id);
    if (index == -1) return;

    // Skip status checking if device has an ongoing operation
    if (hasDeviceOperation(device.id)) {
      return;
    }

    _devices[index] = device.copyWith(status: DeviceStatus.connecting);
    notifyListeners();

    try {
      // Use simple ping instead of Python script for better reliability
      final result = await Process.run(
        'ping',
        ['-c', '1', '-W', '2', device.ip],
      ).timeout(const Duration(seconds: 5));

      if (result.exitCode == 0) {
        // Device is reachable - check if SSH is connected
        final isSSHConnected = _sshConnections.containsKey(device.id) &&
            _sshConnections[device.id]?.isConnected == true;

        _devices[index] = device.copyWith(
          status: isSSHConnected ? DeviceStatus.connected : DeviceStatus.online,
          lastSeen: DateTime.now(),
          isConnected: isSSHConnected,
        );
      } else {
        // Device is not reachable
        _devices[index] = device.copyWith(
          status: DeviceStatus.offline,
          isConnected: false,
        );
      }
    } catch (e) {
      // Error checking device (timeout or other error)
      debugPrint('Error checking device ${device.ip}: $e');
      _devices[index] = device.copyWith(
        status: DeviceStatus.unknown,
        isConnected: false,
      );
    }

    notifyListeners();
  }

  Future<void> refreshDeviceStatus(String deviceId) async {
    final device = _devices.where((d) => d.id == deviceId).firstOrNull;
    if (device != null) {
      await _checkDeviceStatus(device);
    }
  }

  Future<void> refreshAllDevices() async {
    for (final device in _devices) {
      await _checkDeviceStatus(device);
      // Don't automatically fetch system info to avoid terminal interference
      // System info can be fetched manually when needed
    }
  }

  // Quiet version that doesn't call notifyListeners to avoid terminal interruption
  Future<void> _refreshAllDevicesQuiet() async {
    for (final device in _devices) {
      await _checkDeviceStatusQuiet(device);
    }
  }

  // Quiet version of device status check that doesn't call notifyListeners
  Future<void> _checkDeviceStatusQuiet(Device device) async {
    final index = _devices.indexWhere((d) => d.id == device.id);
    if (index == -1) return;

    // Skip status checking if device has an ongoing operation
    if (hasDeviceOperation(device.id)) {
      return;
    }

    try {
      // Use simple ping instead of Python script for better reliability
      final result = await Process.run(
        'ping',
        ['-c', '1', '-W', '2', device.ip],
      ).timeout(const Duration(seconds: 5));

      if (result.exitCode == 0) {
        // Device is reachable - check if SSH is connected
        final isSSHConnected = _sshConnections.containsKey(device.id) &&
            _sshConnections[device.id]?.isConnected == true;

        _devices[index] = device.copyWith(
          status: isSSHConnected ? DeviceStatus.connected : DeviceStatus.online,
          lastSeen: DateTime.now(),
          isConnected: isSSHConnected,
        );
      } else {
        // Device is not reachable
        _devices[index] = device.copyWith(
          status: DeviceStatus.offline,
          isConnected: false,
        );
      }
    } catch (e) {
      // Error checking device (timeout or other error)
      debugPrint('Error checking device ${device.ip}: $e');
      _devices[index] = device.copyWith(
        status: DeviceStatus.unknown,
        isConnected: false,
      );
    }

    // Don't call notifyListeners() here to avoid terminal interruption
  }

  // Terminal activity tracking to prevent interruptions
  bool _terminalIsActive = false;
  DateTime? _lastTerminalActivity;

  void setTerminalActive(bool active) {
    _terminalIsActive = active;
    if (active) {
      _lastTerminalActivity = DateTime.now();
    }
  }

  bool get isTerminalRecentlyActive {
    if (_lastTerminalActivity == null) return false;
    return DateTime.now().difference(_lastTerminalActivity!).inSeconds < 5;
  }

  // Connection monitoring functionality
  void _startConnectionMonitoring() {
    if (!_autoRefreshEnabled) return;

    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer = Timer.periodic(
      Duration(seconds: _autoRefreshInterval),
      (timer) => _monitorConnections(),
    );
    debugPrint(
        'Started connection monitoring with ${_autoRefreshInterval}s interval');
  }

  void _stopConnectionMonitoring() {
    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer = null;
    debugPrint('Stopped connection monitoring');
  }

  Future<void> _monitorConnections() async {
    if (!_autoRefreshEnabled) return;

    // Skip monitoring if terminal is actively being used to prevent input interruption
    if (_terminalIsActive || isTerminalRecentlyActive) {
      debugPrint('Skipping connection monitoring - terminal is active');
      return;
    }

    debugPrint('Monitoring connections...');
    final previouslyConnected = Set<String>.from(_sshConnections.keys
        .where((id) => _sshConnections[id]?.isConnected == true));

    // Check SSH connection health for all connected devices
    await _checkSSHConnectionHealth();

    // Check all device network status (but don't notify listeners if terminal is active)
    await _refreshAllDevicesQuiet();

    // Check for lost connections
    final currentlyConnected = Set<String>.from(_sshConnections.keys
        .where((id) => _sshConnections[id]?.isConnected == true));

    final lostConnections = previouslyConnected.difference(currentlyConnected);

    for (final deviceId in lostConnections) {
      if (!_notifiedDisconnections.contains(deviceId)) {
        final device = _devices.where((d) => d.id == deviceId).firstOrNull;
        if (device != null) {
          _showConnectionLostNotification(device);
          _notifiedDisconnections.add(deviceId);
        }
      }
    }

    // Clear notifications for devices that reconnected
    final reconnected = currentlyConnected.difference(previouslyConnected);
    for (final deviceId in reconnected) {
      _notifiedDisconnections.remove(deviceId);
    }

    // Only notify listeners if there were significant changes and terminal is not active
    if (lostConnections.isNotEmpty || reconnected.isNotEmpty) {
      if (!_terminalIsActive && !isTerminalRecentlyActive) {
        notifyListeners();
      }
    }
  }

  Future<void> _checkSSHConnectionHealth() async {
    final connectionsToCheck = List<String>.from(_sshConnections.keys
        .where((id) => _sshConnections[id]?.isConnected == true));

    for (final deviceId in connectionsToCheck) {
      final connection = _sshConnections[deviceId];
      if (connection == null || !connection.isConnected) continue;

      try {
        // Try to execute a simple command to check if SSH is still alive
        final session = await connection.client!.execute('echo "ping"');
        final output = await utf8.decoder.bind(session.stdout).join();

        if (!output.contains('ping')) {
          // SSH connection is not responding properly
          debugPrint('SSH connection to $deviceId is not responding');
          await _handleConnectionFailure(
              deviceId, 'SSH connection not responding');
        }
      } catch (e) {
        // SSH connection failed
        debugPrint('SSH connection to $deviceId failed: $e');
        await _handleConnectionFailure(deviceId, e.toString());
      }
    }
  }

  Future<void> _handleConnectionFailure(String deviceId, String error) async {
    final connection = _sshConnections[deviceId];
    if (connection != null) {
      connection.isConnected = false;
      connection.error = error;
    }

    // Update device status
    final deviceIndex = _devices.indexWhere((d) => d.id == deviceId);
    if (deviceIndex != -1) {
      _devices[deviceIndex] = _devices[deviceIndex].copyWith(
        status: DeviceStatus.error,
        isConnected: false,
        systemInfo: {}, // Clear system info when connection is lost
      );
    }

    // Trigger auto-selection logic in case the selected device was disconnected
    autoSelectBestDevice();

    notifyListeners();
  }

  void _showConnectionLostNotification(Device device) {
    debugPrint('Connection lost to ${device.name}');

    // Add to notification queue for UI to display
    _pendingNotifications.add({
      'type': 'connection_lost',
      'title': 'Connection Lost',
      'message': 'Lost connection to ${device.name}',
      'timestamp': DateTime.now(),
    });

    notifyListeners();
  }

  // Notification system
  final List<Map<String, dynamic>> _pendingNotifications = [];

  List<Map<String, dynamic>> get pendingNotifications =>
      List.unmodifiable(_pendingNotifications);

  void clearNotification(int index) {
    if (index >= 0 && index < _pendingNotifications.length) {
      _pendingNotifications.removeAt(index);
      notifyListeners();
    }
  }

  void clearAllNotifications() {
    _pendingNotifications.clear();
    notifyListeners();
  }

  // System Information Fetching
  Future<void> _fetchSystemInfo(String deviceId) async {
    final connection = _sshConnections[deviceId];
    if (connection == null || !connection.isConnected) {
      debugPrint('Cannot fetch system info: device not connected');
      return;
    }

    try {
      // Execute the system info script
      final scriptPath =
          'python3 -c "import sys; sys.path.append(\'/tmp\'); exec(open(\'/tmp/system_info.py\').read())"';

      // First, upload the system info script to the device
      await _uploadSystemInfoScript(deviceId);

      // Then execute it
      final session = await connection.client!.execute(scriptPath);
      final output = await utf8.decoder.bind(session.stdout).join();

      if (output.isNotEmpty) {
        try {
          final systemInfo = jsonDecode(output);
          final deviceIndex = _devices.indexWhere((d) => d.id == deviceId);
          if (deviceIndex != -1) {
            _devices[deviceIndex] = _devices[deviceIndex].copyWith(
              systemInfo: systemInfo,
            );
            notifyListeners();
          }
        } catch (e) {
          debugPrint('Error parsing system info JSON: $e');
        }
      }
    } catch (e) {
      debugPrint('Error fetching system info: $e');
    }
  }

  Future<void> _uploadSystemInfoScript(String deviceId) async {
    final connection = _sshConnections[deviceId];
    if (connection == null || !connection.isConnected) return;

    try {
      // Read the system info script
      final scriptContent =
          await rootBundle.loadString('lib/scripts/system_info.py');

      // Upload script to /tmp/system_info.py
      final uploadSession =
          await connection.client!.execute('cat > /tmp/system_info.py');
      uploadSession.stdin.add(utf8.encode(scriptContent));
      await uploadSession.stdin.close();
      await uploadSession.done;

      // Make it executable
      await connection.client!.execute('chmod +x /tmp/system_info.py');
    } catch (e) {
      debugPrint('Error uploading system info script: $e');
    }
  }

  Future<void> refreshSystemInfo(String deviceId) async {
    await _fetchSystemInfo(deviceId);
  }

  // Auto-connect functionality
  Future<void> _autoConnectDevice(Device device) async {
    try {
      // Get saved credentials
      final credentials = await getSavedSSHCredentials(device.id);
      if (credentials != null) {
        debugPrint('Auto-connecting to ${device.name} on launch...');
        await connectSSH(
          device.id,
          credentials['username']!,
          credentials['password']!,
          rememberCredentials: true,
        );

        // Don't automatically fetch system info to avoid cluttering the terminal
        // System info can be fetched manually when needed
        debugPrint('Auto-connection successful for ${device.name}');
      } else {
        debugPrint(
            'No saved credentials for ${device.name}, skipping auto-connect');
      }
    } catch (e) {
      debugPrint('Auto-connect failed for ${device.name}: $e');
    }
  }

  // Network Scanning
  Future<List<Map<String, String?>>> scanNetwork() async {
    _isScanning = true;
    notifyListeners();

    try {
      final tempDir = Directory.systemTemp;
      final outputFile = File('${tempDir.path}/discovered_devices.json');

      // Find the project root directory by looking for pubspec.yaml
      String projectRoot = Directory.current.path;
      while (!File('$projectRoot/pubspec.yaml').existsSync() &&
          projectRoot != '/') {
        projectRoot = Directory(projectRoot).parent.path;
      }

      final scriptPath = '$projectRoot/lib/scripts/device_discovery_new.py';

      debugPrint('Current working directory: ${Directory.current.path}');
      debugPrint('Project root: $projectRoot');
      debugPrint('Running network scan with script: $scriptPath');
      debugPrint('Output file: ${outputFile.path}');

      // Verify script exists
      if (!File(scriptPath).existsSync()) {
        debugPrint('ERROR: Script not found at $scriptPath');
        return [];
      }

      final result = await Process.run(
        'python3',
        [scriptPath, outputFile.path],
        workingDirectory: projectRoot,
      ).timeout(const Duration(minutes: 2));

      debugPrint('Python script exit code: ${result.exitCode}');
      debugPrint('Python script stdout: ${result.stdout}');
      debugPrint('Python script stderr: ${result.stderr}');

      if (result.exitCode == 0 && await outputFile.exists()) {
        final contents = await outputFile.readAsString();
        debugPrint('Output file contents: $contents');

        final List<dynamic> devices = jsonDecode(contents);

        return devices
            .map((device) => {
                  'ip': device['ip'] as String,
                  'mac': device['mac'] as String,
                  'vendor': device['vendor'] as String,
                  'hostname': device['hostname'] as String?,
                })
            .toList();
      } else {
        debugPrint('Script failed or output file not found');
        if (result.stderr.isNotEmpty) {
          debugPrint('Error: ${result.stderr}');
        }
      }
    } catch (e) {
      debugPrint('Network scan error: $e');
    } finally {
      _isScanning = false;
      notifyListeners();
    }

    return [];
  }

  // SSH Connection Management
  Future<void> connectSSH(String deviceId, String username, String password,
      {bool rememberCredentials = false, int port = 22}) async {
    final device = _devices.where((d) => d.id == deviceId).firstOrNull;
    if (device == null) return;

    // Check if already connected
    if (_sshConnections[deviceId]?.isConnected == true) {
      return;
    }

    // Don't automatically set the selected device here - let the auto-selection logic handle it
    // This prevents overriding the default device selection
    final connection = SSHConnection(
      deviceId: deviceId,
      deviceIp: device.ip,
      username: username,
    );
    _sshConnections[deviceId] = connection;

    // Update device connection status
    final deviceIndex = _devices.indexWhere((d) => d.id == deviceId);
    if (deviceIndex != -1) {
      _devices[deviceIndex] = device.copyWith(status: DeviceStatus.connecting);
      notifyListeners();
    }

    try {
      // Create SSH socket connection using device's port
      final socket = await SSHSocket.connect(device.ip, device.port);

      // Create SSH client
      connection.client = SSHClient(
        socket,
        username: username,
        onPasswordRequest: () => password,
      );

      // Create shell session with PTY using stored or default terminal size
      final terminal = getOrCreateTerminalForDevice(deviceId);

      // Use stored size if available, otherwise use reasonable defaults
      final terminalWidth = connection.lastKnownWidth ??
          (terminal.viewWidth > 0 ? terminal.viewWidth : 120);
      final terminalHeight = connection.lastKnownHeight ??
          (terminal.viewHeight > 0 ? terminal.viewHeight : 30);

      debugPrint(
          'Creating SSH session with terminal size: ${terminalWidth}x$terminalHeight');

      connection.session = await connection.client!.shell(
        pty: SSHPtyConfig(
          width: terminalWidth,
          height: terminalHeight,
        ),
      );

      // Setup output listener with terminal readiness detection
      connection.session!.stdout
          .cast<List<int>>()
          .transform(utf8.decoder)
          .listen((output) {
        connection.addOutput(output);

        // Check if this looks like a shell prompt (indicating terminal is ready)
        if (!connection.isTerminalReady &&
            (output.contains('\$') ||
                output.contains('#') ||
                output.contains('~') ||
                output.contains('pi@') ||
                output.contains('root@'))) {
          connection.isTerminalReady = true;
          debugPrint('SSH terminal ready for device ${connection.deviceId}');
          notifyListeners();
        }

        // Always write to terminal if connected, regardless of readiness
        if (connection.terminal != null) {
          connection.terminal!.write(output);
        } else {
          // Fallback to legacy terminal output
          _terminalOutput.add(output);
          notifyListeners();
        }
      });

      connection.session!.stderr
          .cast<List<int>>()
          .transform(utf8.decoder)
          .listen((output) {
        connection.addOutput(output);

        // Always write to terminal if connected
        if (connection.terminal != null) {
          connection.terminal!.write(output);
        } else {
          // Fallback to legacy terminal output
          _terminalOutput.add(output);
          notifyListeners();
        }
      });

      // Update device status to connected
      if (deviceIndex != -1) {
        _devices[deviceIndex] = device.copyWith(
          status: DeviceStatus.connected,
          isConnected: true,
        );
        notifyListeners();
      }

      connection.isConnected = true;
      connection.error = null;

      if (rememberCredentials) {
        await _saveSSHCredentials(deviceId, username, password);
      } else {
        await _clearSSHCredentials(deviceId);
      }

      // Always detect OS on connection to keep it up to date
      await _detectOperatingSystem(deviceId);

      // Clear existing system info to show loading state
      if (deviceIndex != -1) {
        _devices[deviceIndex] = _devices[deviceIndex].copyWith(
          systemInfo: {},
        );
        notifyListeners();
      }

      // Add a small delay to make loading state visible
      await Future.delayed(const Duration(milliseconds: 500));

      // Automatically fetch system health when device connects
      debugPrint('Fetching system health for ${device.name}...');
      try {
        await _fetchSystemInfo(deviceId);
        debugPrint('System health fetched successfully for ${device.name}');
      } catch (e) {
        debugPrint('Failed to fetch system health for ${device.name}: $e');
      }

      // Trigger auto-selection logic now that this device is connected
      // This ensures the default device gets selected if it just connected
      autoSelectBestDevice();
    } catch (e) {
      // SSH connection failed
      connection.isConnected = false;
      connection.error = 'Failed to connect: ${e.toString()}';

      // Update device connection status on failure
      if (deviceIndex != -1) {
        _devices[deviceIndex] = device.copyWith(
          status: DeviceStatus.error,
          isConnected: false,
        );
        notifyListeners();
      }
    }
    notifyListeners();
  }

  Future<void> disconnectSSH(String deviceId) async {
    final device = _devices.where((d) => d.id == deviceId).firstOrNull;
    if (device == null) return;

    final deviceIndex = _devices.indexWhere((d) => d.id == deviceId);

    // Update device status to disconnecting
    if (deviceIndex != -1) {
      _devices[deviceIndex] = device.copyWith(
        status: DeviceStatus.disconnecting,
        isConnected: false,
        systemInfo: {}, // Clear system info when disconnecting
      );
      notifyListeners();
    }

    final connection = _sshConnections[deviceId];
    if (connection != null) {
      try {
        // Dispose of connection resources
        await connection.dispose();
      } catch (e) {
        debugPrint('Error disposing SSH connection: $e');
      }
      _sshConnections.remove(deviceId);
    }

    // If this was the selected device, switch to another connected device
    if (_selectedDeviceId == deviceId) {
      debugPrint(
          'Selected device $deviceId was disconnected, switching to next available device');
      _isManualSelection =
          false; // Reset manual selection flag to allow auto-switching
      _autoSwitchToNextConnectedDevice();
    }

    // Check device status after disconnection (unless device has an ongoing operation)
    if (deviceIndex != -1 && !hasDeviceOperation(deviceId)) {
      await _checkDeviceStatus(_devices[deviceIndex]);
    }
  }

  Future<void> executeSSHCommand(String deviceId, String data) async {
    final connection = _sshConnections[deviceId];
    if (connection == null || !connection.isConnected) {
      throw Exception('Not connected to device');
    }

    if (connection.session == null) {
      throw Exception('SSH session not established');
    }

    try {
      // Write raw data to SSH session (for terminal emulator)
      connection.session!.write(utf8.encode(data));
    } catch (e) {
      connection.error = 'Command failed: ${e.toString()}';
      notifyListeners();
      rethrow;
    }
  }

  // SSH Credentials Management
  Future<void> _saveSSHCredentials(
      String deviceId, String username, String password) async {
    // Store credentials securely (in production, passwords should be encrypted)
    await _prefs.setString('ssh_username_$deviceId', username);
    // Note: In production, passwords should be encrypted using secure storage
    await _prefs.setString('ssh_password_$deviceId', password);
  }

  Future<void> _clearSSHCredentials(String deviceId) async {
    await _prefs.remove('ssh_username_$deviceId');
    await _prefs.remove('ssh_password_$deviceId');
  }

  Future<Map<String, String>?> getSavedSSHCredentials(String deviceId) async {
    final username = _prefs.getString('ssh_username_$deviceId');
    final password = _prefs.getString('ssh_password_$deviceId');

    if (username != null && password != null) {
      return {'username': username, 'password': password};
    }
    return null;
  }

  // OS Detection
  Future<void> _detectOperatingSystem(String deviceId) async {
    final connection = _sshConnections[deviceId];
    if (connection == null || !connection.isConnected) return;

    try {
      debugPrint('Detecting OS for device $deviceId...');

      // Try to detect OS using uname command
      final session = await connection.client!.execute('uname -a');
      final output = await utf8.decoder.bind(session.stdout).join();

      String detectedOS = 'Unknown';

      if (output.isNotEmpty) {
        final osOutput = output.toLowerCase();

        if (osOutput.contains('linux')) {
          // Get comprehensive Linux distribution and architecture info
          detectedOS = await _detectLinuxDistribution(connection);
        } else if (osOutput.contains('darwin')) {
          detectedOS = 'macOS';
        } else if (osOutput.contains('freebsd')) {
          detectedOS = 'FreeBSD';
        } else if (osOutput.contains('openbsd')) {
          detectedOS = 'OpenBSD';
        } else if (osOutput.contains('netbsd')) {
          detectedOS = 'NetBSD';
        }
      } else {
        // Try to detect Android (which might not respond to uname)
        try {
          final androidSession = await connection.client!.execute(
              'getprop ro.build.version.release 2>/dev/null || echo ""');
          final androidOutput =
              await utf8.decoder.bind(androidSession.stdout).join();

          if (androidOutput.trim().isNotEmpty) {
            // Check if it's a Raspberry Pi running Android
            final isRaspberryPi = await _isRaspberryPiDevice(connection);
            if (isRaspberryPi) {
              detectedOS = 'Raspberry Pi Android';
            } else {
              detectedOS = 'Android';
            }
          }
        } catch (e) {
          // Not Android or detection failed
        }
      }

      // Update device with detected OS
      final deviceIndex = _devices.indexWhere((d) => d.id == deviceId);
      if (deviceIndex != -1) {
        _devices[deviceIndex] = _devices[deviceIndex].copyWith(
          operatingSystem: detectedOS,
        );
        notifyListeners();
        debugPrint('Detected OS for device $deviceId: $detectedOS');
      }
    } catch (e) {
      debugPrint('Failed to detect OS for device $deviceId: $e');

      // Update device to show detection failed
      final deviceIndex = _devices.indexWhere((d) => d.id == deviceId);
      if (deviceIndex != -1) {
        _devices[deviceIndex] = _devices[deviceIndex].copyWith(
          operatingSystem: 'Unknown',
        );
        notifyListeners();
      }
    }
  }

  // Helper method to detect if device is a Raspberry Pi
  Future<bool> _isRaspberryPiDevice(SSHConnection connection) async {
    try {
      // Check /proc/cpuinfo for Raspberry Pi indicators
      final cpuInfoSession = await connection.client!
          .execute('cat /proc/cpuinfo 2>/dev/null || echo ""');
      final cpuInfoOutput =
          await utf8.decoder.bind(cpuInfoSession.stdout).join();

      if (cpuInfoOutput.toLowerCase().contains('raspberry pi') ||
          cpuInfoOutput.toLowerCase().contains('bcm')) {
        return true;
      }

      // Check /proc/device-tree/model for Pi model info
      final modelSession = await connection.client!
          .execute('cat /proc/device-tree/model 2>/dev/null || echo ""');
      final modelOutput = await utf8.decoder.bind(modelSession.stdout).join();

      if (modelOutput.toLowerCase().contains('raspberry pi')) {
        return true;
      }

      // Check for vcgencmd (Raspberry Pi specific command)
      final vcgencmdSession =
          await connection.client!.execute('which vcgencmd 2>/dev/null');
      final vcgencmdOutput =
          await utf8.decoder.bind(vcgencmdSession.stdout).join();

      if (vcgencmdOutput.trim().isNotEmpty) {
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // Comprehensive Linux distribution detection
  Future<String> _detectLinuxDistribution(SSHConnection connection) async {
    try {
      // Check if it's a Raspberry Pi first
      final isRaspberryPi = await _isRaspberryPiDevice(connection);

      // Get architecture information
      final archSession =
          await connection.client!.execute('uname -m 2>/dev/null || echo ""');
      final architecture =
          (await utf8.decoder.bind(archSession.stdout).join()).trim();

      // Get distribution information from multiple sources
      final distroSession = await connection.client!.execute(
          'cat /etc/os-release 2>/dev/null || cat /etc/lsb-release 2>/dev/null || echo "ID=linux"');

      final distroOutput = await utf8.decoder.bind(distroSession.stdout).join();

      // Parse the distribution information
      final Map<String, String> osInfo = {};
      for (final line in distroOutput.split('\n')) {
        if (line.contains('=')) {
          final parts = line.split('=');
          if (parts.length >= 2) {
            final key = parts[0].trim();
            final value = parts[1].trim().replaceAll('"', '');
            osInfo[key] = value;
          }
        }
      }

      // Determine the distribution name and version
      String distroName = _getDistributionName(osInfo, distroOutput);
      String version = _getDistributionVersion(osInfo);

      // Format the final OS string
      String finalOS;
      if (isRaspberryPi) {
        if (distroName.toLowerCase().contains('raspbian') ||
            distroName.toLowerCase().contains('raspberry pi os')) {
          finalOS = 'Raspberry Pi OS';
        } else {
          finalOS = 'Raspberry Pi $distroName';
        }
      } else {
        finalOS = distroName;
      }

      // Add version if available and not too long
      if (version.isNotEmpty &&
          version != 'unknown' &&
          finalOS.length + version.length < 25) {
        finalOS += ' $version';
      }

      // Add architecture if it's not standard and string isn't too long
      if (architecture.isNotEmpty &&
          !['x86_64', 'amd64'].contains(architecture) &&
          finalOS.length + architecture.length < 30) {
        finalOS += ' ($architecture)';
      }

      return finalOS;
    } catch (e) {
      debugPrint('Failed to detect Linux distribution: $e');
      return 'Linux';
    }
  }

  String _getDistributionName(Map<String, String> osInfo, String rawOutput) {
    // Try to get the pretty name first
    if (osInfo.containsKey('PRETTY_NAME')) {
      return osInfo['PRETTY_NAME']!;
    }

    // Try to get name and version separately
    if (osInfo.containsKey('NAME')) {
      return osInfo['NAME']!;
    }

    // Check for specific distributions by ID
    final id = osInfo['ID']?.toLowerCase() ?? '';
    final idLike = osInfo['ID_LIKE']?.toLowerCase() ?? '';

    // Comprehensive list of Linux distributions
    if (id.contains('ubuntu')) return 'Ubuntu';
    if (id.contains('debian')) return 'Debian';
    if (id.contains('raspbian')) return 'Raspbian';
    if (id.contains('fedora')) return 'Fedora';
    if (id.contains('centos')) return 'CentOS';
    if (id.contains('rhel') || id.contains('redhat')) {
      return 'Red Hat Enterprise Linux';
    }
    if (id.contains('opensuse')) return 'openSUSE';
    if (id.contains('sles')) return 'SUSE Linux Enterprise';
    if (id.contains('arch')) return 'Arch Linux';
    if (id.contains('manjaro')) return 'Manjaro';
    if (id.contains('mint')) return 'Linux Mint';
    if (id.contains('elementary')) return 'elementary OS';
    if (id.contains('zorin')) return 'Zorin OS';
    if (id.contains('pop')) return 'Pop!_OS';
    if (id.contains('kali')) return 'Kali Linux';
    if (id.contains('parrot')) return 'Parrot OS';
    if (id.contains('alpine')) return 'Alpine Linux';
    if (id.contains('void')) return 'Void Linux';
    if (id.contains('gentoo')) return 'Gentoo';
    if (id.contains('slackware')) return 'Slackware';
    if (id.contains('nixos')) return 'NixOS';
    if (id.contains('solus')) return 'Solus';
    if (id.contains('mageia')) return 'Mageia';
    if (id.contains('pclinuxos')) return 'PCLinuxOS';
    if (id.contains('antergos')) return 'Antergos';
    if (id.contains('endeavouros')) return 'EndeavourOS';
    if (id.contains('garuda')) return 'Garuda Linux';
    if (id.contains('artix')) return 'Artix Linux';

    // Check ID_LIKE for base distributions
    if (idLike.contains('debian')) return 'Debian-based';
    if (idLike.contains('ubuntu')) return 'Ubuntu-based';
    if (idLike.contains('fedora')) return 'Fedora-based';
    if (idLike.contains('rhel')) return 'RHEL-based';
    if (idLike.contains('arch')) return 'Arch-based';
    if (idLike.contains('suse')) return 'SUSE-based';

    // Parse from raw output for older systems
    final rawLower = rawOutput.toLowerCase();
    if (rawLower.contains('ubuntu')) return 'Ubuntu';
    if (rawLower.contains('debian')) return 'Debian';
    if (rawLower.contains('fedora')) return 'Fedora';
    if (rawLower.contains('centos')) return 'CentOS';
    if (rawLower.contains('red hat')) return 'Red Hat';
    if (rawLower.contains('suse')) return 'SUSE';
    if (rawLower.contains('arch')) return 'Arch Linux';

    return 'Linux';
  }

  String _getDistributionVersion(Map<String, String> osInfo) {
    // Try version ID first (usually more concise)
    if (osInfo.containsKey('VERSION_ID')) {
      return osInfo['VERSION_ID']!;
    }

    // Try full version
    if (osInfo.containsKey('VERSION')) {
      final version = osInfo['VERSION']!;
      // Extract just the version number if it's too long
      final match = RegExp(r'(\d+\.?\d*)').firstMatch(version);
      if (match != null) {
        return match.group(1)!;
      }
      return version;
    }

    return '';
  }

  // Terminal Management
  void clearTerminalOutput() {
    _terminalOutput.clear();
    notifyListeners();
  }

  void addTerminalOutput(String output) {
    _terminalOutput.add(output);
    notifyListeners();
  }

  // Terminal resize handling using native dartssh2 PTY resizing
  Future<void> resizeSSHTerminal(String deviceId, int width, int height) async {
    final connection = _sshConnections[deviceId];
    if (connection == null ||
        !connection.isConnected ||
        connection.session == null) {
      return;
    }

    // Check if this is a significant size change to avoid spam
    final lastWidth = connection.lastKnownWidth ?? 80;
    final lastHeight = connection.lastKnownHeight ?? 24;
    final widthDiff = (width - lastWidth).abs();
    final heightDiff = (height - lastHeight).abs();

    // Only process resize if there's a meaningful change (at least 2 columns or 1 row)
    if (widthDiff < 2 && heightDiff < 1) {
      return;
    }

    // Store the new size
    connection.lastKnownWidth = width;
    connection.lastKnownHeight = height;

    // Cancel any existing resize timer
    _resizeTimers[deviceId]?.cancel();

    // Debounce resize requests to avoid sending too many commands
    _resizeTimers[deviceId] =
        Timer(const Duration(milliseconds: 300), () async {
      try {
        debugPrint(
            'Resizing terminal PTY: ${lastWidth}x$lastHeight -> ${width}x$height');

        // Use the native dartssh2 PTY resize functionality!
        connection.session!.resizeTerminal(width, height);

        debugPrint('Terminal PTY successfully resized to ${width}x$height');
      } catch (e) {
        debugPrint('Failed to resize terminal PTY: $e');
      }
    });
  }

  // Clear terminal screen
  Future<void> clearTerminal(String deviceId) async {
    final connection = _sshConnections[deviceId];
    if (connection == null ||
        !connection.isConnected ||
        connection.session == null) {
      return;
    }

    try {
      // Send clear command to the terminal
      connection.session!.write(utf8.encode('clear\n'));
      debugPrint('Terminal cleared for device $deviceId');
    } catch (e) {
      debugPrint('Failed to clear terminal: $e');
    }
  }

  // Get or create a terminal for a specific device
  Terminal getOrCreateTerminalForDevice(String deviceId) {
    if (!_deviceTerminals.containsKey(deviceId)) {
      _deviceTerminals[deviceId] = Terminal(maxLines: 10000);
      // Mark this terminal as needing initial prompt
      _terminalNeedsInitialPrompt[deviceId] = true;
    }
    return _deviceTerminals[deviceId]!;
  }

  // Connect terminal to SSH session
  void connectTerminalToSSH(String deviceId, Terminal terminal,
      {bool sendInitialPrompt = false}) {
    final connection = _sshConnections[deviceId];
    if (connection == null ||
        !connection.isConnected ||
        connection.session == null) {
      debugPrint('SSH connection not available for device $deviceId');
      return;
    }

    // Store terminal reference in connection
    connection.terminal = terminal;

    // Set up terminal output handler to send data to SSH
    terminal.onOutput = (data) {
      try {
        connection.session!.write(utf8.encode(data));
      } catch (e) {
        debugPrint('Failed to send terminal data to SSH: $e');
      }
    };

    debugPrint('Terminal connected to SSH session for device $deviceId');

    // Check if this terminal needs initial prompt (new terminal) or explicit request
    final needsInitialPrompt =
        _terminalNeedsInitialPrompt[deviceId] == true || sendInitialPrompt;

    if (needsInitialPrompt) {
      try {
        // Send an empty command (just Enter) to trigger the shell prompt
        connection.session!.write(utf8.encode('\n'));
        // Mark that this terminal no longer needs initial prompt
        _terminalNeedsInitialPrompt[deviceId] = false;
      } catch (e) {
        debugPrint('Failed to send initial prompt trigger: $e');
      }
    }
  }

  // Process State Management - Global state that persists across navigation

  /// Get process states for a specific device
  Map<int, ProcessState> getProcessStates(String deviceId) {
    return _deviceProcessStates[deviceId] ?? {};
  }

  /// Set process state for a specific device and PID
  void setProcessState(String deviceId, int pid, ProcessState state) {
    _deviceProcessStates[deviceId] ??= {};
    _deviceProcessStates[deviceId]![pid] = state;
    _saveProcessStates(); // Persist to storage
    notifyListeners();
  }

  /// Get process state for a specific device and PID
  ProcessState? getProcessState(String deviceId, int pid) {
    return _deviceProcessStates[deviceId]?[pid];
  }

  /// Clear process state for a specific device and PID
  void clearProcessState(String deviceId, int pid) {
    _deviceProcessStates[deviceId]?.remove(pid);
    if (_deviceProcessStates[deviceId]?.isEmpty == true) {
      _deviceProcessStates.remove(deviceId);
    }
    _saveProcessStates(); // Persist to storage
    notifyListeners();
  }

  /// Clean up process states for processes that no longer exist
  void cleanupProcessStates(String deviceId, List<int> currentPids) {
    final deviceStates = _deviceProcessStates[deviceId];
    if (deviceStates == null) return;

    final currentPidSet = currentPids.toSet();
    final statePids = deviceStates.keys.toList();
    bool hasChanges = false;

    // Remove states for processes that no longer exist
    for (final pid in statePids) {
      if (!currentPidSet.contains(pid)) {
        deviceStates.remove(pid);
        hasChanges = true;
      }
    }

    // Remove device entry if no states remain
    if (deviceStates.isEmpty) {
      _deviceProcessStates.remove(deviceId);
      hasChanges = true;
    }

    if (hasChanges) {
      _saveProcessStates(); // Persist to storage
      notifyListeners();
    }
  }

  /// Clear all process states for a device (when device disconnects)
  void clearAllProcessStates(String deviceId) {
    _deviceProcessStates.remove(deviceId);
    _saveProcessStates(); // Persist to storage
    notifyListeners();
  }

  /// Save process states to SharedPreferences
  void _saveProcessStates() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert the nested map to a JSON-serializable format
      final Map<String, Map<String, String>> serializable = {};
      for (final deviceEntry in _deviceProcessStates.entries) {
        final deviceId = deviceEntry.key;
        final processStates = deviceEntry.value;

        serializable[deviceId] = {};
        for (final processEntry in processStates.entries) {
          final pid = processEntry.key.toString();
          final state = processEntry.value.toString();
          serializable[deviceId]![pid] = state;
        }
      }

      await prefs.setString('process_states', jsonEncode(serializable));
    } catch (e) {
      debugPrint('Failed to save process states: $e');
    }
  }

  /// Clear all application data
  Future<void> clearAllData() async {
    try {
      // Disconnect all SSH connections
      for (final deviceId in _sshConnections.keys.toList()) {
        await disconnectSSH(deviceId);
      }

      // Clear all stored data
      await _prefs.clear();

      // Reset all state variables
      _devices.clear();
      _sshConnections.clear();
      _selectedDeviceId = null;
      _defaultDeviceId = null;
      _isManualSelection = false;
      _deviceTerminals.clear();
      _terminalNeedsInitialPrompt.clear();
      _deviceProcessStates.clear();

      // Reset UI settings to defaults
      _defaultGridView = false;
      _autoRefreshEnabled = true;
      _autoRefreshInterval = 30;
      _appsGridColumns = 4;
      _developerModeEnabled = false;
      _terminalFontSize = 18.0;
      _terminalFontFamily = 'Menlo';

      // Reinitialize with default settings
      await _loadUISettings();

      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing all data: $e');
    }
  }
}
