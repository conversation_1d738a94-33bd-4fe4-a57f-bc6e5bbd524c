import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dartssh2/dartssh2.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:io';

class SSHConnection {
  final String deviceIp;
  final String username;
  SSHClient? client;
  SSHSession? session;
  bool isConnected = false;
  String? error;
  StreamController<String>? _outputController;
  Stream<String>? outputStream;

  SSHConnection(this.deviceIp, this.username) {
    _outputController = StreamController<String>.broadcast();
    outputStream = _outputController?.stream;
  }

  void addOutput(String output) {
    _outputController?.add(output);
  }

  Future<void> dispose() async {
    _outputController?.close();
    session?.close();
    client?.close();
  }
}

class Device {
  final String ip;
  final String mac;
  final String vendor;
  final String name;
  String status;
  bool isConnected;
  bool isPinging;

  Device({
    required this.ip,
    required this.mac,
    required this.vendor,
    required this.name,
    this.status = 'unknown',
    this.isConnected = false,
    this.isPinging = false,
  });

  factory Device.empty() => Device(
        ip: '',
        mac: '',
        vendor: '',
        name: '',
      );

  Map<String, dynamic> toJson() => {
        'ip': ip,
        'mac': mac,
        'vendor': vendor,
        'name': name,
        'status': status,
        'isConnected': isConnected,
        'isPinging': isPinging,
      };

  factory Device.fromJson(Map<String, dynamic> json) => Device(
        ip: json['ip'],
        mac: json['mac'],
        vendor: json['vendor'],
        name: json['name'],
        status: json['status'] ?? 'unknown',
        isConnected: json['isConnected'] ?? false,
        isPinging: json['isPinging'] ?? false,
      );
}

class AppState extends ChangeNotifier {
  final List<Device> _devices = [];
  final Map<String, SSHConnection> _sshConnections = {};
  late SharedPreferences _prefs; // Use shared_preferences
  List<Device> get devices => _devices;

  AppState() {
    _initializePreferences();
  }

  Future<void> _initializePreferences() async {
    _prefs = await SharedPreferences.getInstance();
  }

  Future<void> _checkDeviceStatus(Device device) async {
    // Only set checking status if it's not already checking
    if (device.status != 'checking') {
      device.status = 'checking';
      notifyListeners();
    }

    String pingStatus = 'unknown';
    Map<String, dynamic>? pingStats;

    try {
      // Set pinging state
      device.isPinging = true;
      notifyListeners();

      // Check ping status first
      final result = await Process.run(
          'python3', ['lib/scripts/ping_service.py', device.ip]);

      // Parse JSON response
      final response = jsonDecode(result.stdout.toString().trim());

      if (response is Map && response.containsKey('status')) {
        pingStatus = response['status'];
      }

      // Reset pinging state
      device.isPinging = false;
      notifyListeners();

      // Get ping statistics if available
      if (response is Map && response.containsKey('statistics')) {
        pingStats = response['statistics'];
        if (pingStats != null) {
          // Ping statistics available but not displayed
        }
      }
    } catch (e) {
      debugPrint('Error checking ping status: $e');
    }

    // Determine final status based on ping only
    if (pingStatus == 'online') {
      device.status = 'online';
      device.isConnected = false;
    } else {
      device.status = 'offline';
      device.isConnected = false;
    }

    notifyListeners();
  }

  bool _devicesLoaded = false;

  Future<void> loadDevicesIfNeeded() async {
    if (_devicesLoaded) return;
    await loadDevices();
    _devicesLoaded = true;
  }

  Future<void> loadDevices() async {
    final devicesJson =
        _prefs.getStringList('devices') ?? []; // Use shared_preferences
    _devices.clear();
    _devices
        .addAll(devicesJson.map((json) => Device.fromJson(jsonDecode(json))));

    // Set all devices to checking status initially
    for (final device in _devices) {
      device.status = 'checking';
    }
    notifyListeners();

    // Check status for all devices
    for (final device in _devices) {
      await _checkDeviceStatus(device);
    }
  }

  Future<void> addDevice(Device device) async {
    await _checkDeviceStatus(device);
    _devices.add(device);
    await _saveDevices();
    notifyListeners();
  }

  Future<void> removeDevice(Device device) async {
    _devices.remove(device);
    await _saveDevices();
    notifyListeners();
  }

  Future<void> updateDevice(Device device) async {
    final index = _devices.indexWhere((d) => d.mac == device.mac);
    if (index != -1) {
      await _checkDeviceStatus(device);
      _devices[index] = device;
      await _saveDevices();
      notifyListeners();
    }
  }

  Future<void> _saveDevices() async {
    final devicesJson = _devices.map((d) => jsonEncode(d.toJson())).toList();
    await _prefs.setStringList(
        'devices', devicesJson); // Use shared_preferences
  }

  // Current selected device
  String? _selectedDevice;
  String? get selectedDevice => _selectedDevice;

  // SSH connection for selected device
  SSHConnection? get currentSSHConnection =>
      _selectedDevice != null ? _sshConnections[_selectedDevice] : null;

  // SSH terminal output
  final List<String> _terminalOutput = [
    'pi@raspberrypi:~ \$ Welcome to Blueberry Pi SSH Terminal',
    'pi@raspberrypi:~ \$ Type \'help\' for available commands',
    'pi@raspberrypi:~ \$',
  ];
  List<String> get terminalOutput => _terminalOutput;

  // SSH Credential Management - Using shared_preferences for now
  Future<Map<String, String>?> getSSHCredentials(String deviceIp) async {
    final username = _prefs.getString('ssh_username_$deviceIp');
    final password = _prefs.getString('ssh_password_$deviceIp');
    if (username != null && password != null) {
      return {'username': username, 'password': password};
    }
    return null;
  }

  Future<void> saveSSHCredentials(
      String deviceIp, String username, String password) async {
    await _prefs.setString('ssh_username_$deviceIp', username);
    await _prefs.setString('ssh_password_$deviceIp', password);
    notifyListeners();
  }

  Future<void> clearSSHCredentials(String deviceIp) async {
    await _prefs.remove('ssh_username_$deviceIp');
    await _prefs.remove('ssh_password_$deviceIp');
    notifyListeners();
  }

  // SSH Connection Management
  Future<void> connectSSH(String deviceIp, String username, String password,
      {bool rememberCredentials = false, int port = 22}) async {
    // Check if already connected
    if (_sshConnections[deviceIp]?.isConnected == true) {
      return;
    }

    // Set the selected device
    _selectedDevice = deviceIp;
    final connection = SSHConnection(deviceIp, username);
    _sshConnections[deviceIp] = connection;

    // Update device connection status
    final deviceIndex = _devices.indexWhere((d) => d.ip == deviceIp);
    if (deviceIndex != -1) {
      _devices[deviceIndex].status = 'connecting';
      _devices[deviceIndex].isConnected = false;
      notifyListeners();
    }

    try {
      // Create SSH socket connection
      final socket = await SSHSocket.connect(deviceIp, port);

      // Create SSH client
      connection.client = SSHClient(
        socket,
        username: username,
        onPasswordRequest: () => password,
      );

      // Create shell session
      connection.session = await connection.client!.shell();

      // Setup output listener
      connection.session!.stdout
          .cast<List<int>>()
          .transform(utf8.decoder)
          .listen((output) {
        connection.addOutput(output);
        _terminalOutput.add(output);
        notifyListeners();
      });

      connection.session!.stderr
          .cast<List<int>>()
          .transform(utf8.decoder)
          .listen((output) {
        connection.addOutput(output);
        _terminalOutput.add(output);
        notifyListeners();
      });

      // Update device status to connected
      if (deviceIndex != -1) {
        _devices[deviceIndex].status = 'connected';
        _devices[deviceIndex].isConnected = true;
        notifyListeners();
      }

      connection.isConnected = true;
      connection.error = null;

      if (rememberCredentials) {
        await saveSSHCredentials(deviceIp, username, password);
      } else {
        await clearSSHCredentials(deviceIp);
      }
    } catch (e) {
      // SSH connection failed
      connection.isConnected = false;
      connection.error = 'Failed to connect: ${e.toString()}';

      // Update device connection status on failure
      final deviceIndex = _devices.indexWhere((d) => d.ip == deviceIp);
      if (deviceIndex != -1) {
        _devices[deviceIndex].status = 'offline';
        _devices[deviceIndex].isConnected = false;
        notifyListeners();
      }
    }
    notifyListeners();
  }

  Future<void> disconnectSSH(String deviceIp) async {
    // Update device status to disconnecting
    final deviceIndex = _devices.indexWhere((d) => d.ip == deviceIp);
    if (deviceIndex != -1) {
      _devices[deviceIndex].status = 'disconnecting';
      _devices[deviceIndex].isConnected = false;
      notifyListeners();
    }

    final connection = _sshConnections[deviceIp];
    if (connection != null) {
      // Dispose of connection resources
      await connection.dispose();
      _sshConnections.remove(deviceIp);

      // Update device connection status
      if (deviceIndex != -1) {
        _devices[deviceIndex].isConnected = false;
        // Check device status after disconnect
        await _checkDeviceStatus(_devices[deviceIndex]);
      }
    }
  }

  Future<void> executeSSHCommand(String deviceIp, String command) async {
    final connection = _sshConnections[deviceIp];
    if (connection == null || !connection.isConnected) {
      throw Exception('Not connected to $deviceIp');
    }

    if (connection.session == null) {
      throw Exception('SSH session not established');
    }

    try {
      // Add command to terminal output
      _terminalOutput.add('${connection.username}@$deviceIp:~ \$ $command');
      notifyListeners();

      // Write command to SSH session
      connection.session!.write(utf8.encode('$command\n'));

      // Wait briefly to allow command processing
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      connection.error = 'Command failed: ${e.toString()}';
      notifyListeners();
      rethrow;
    }
  }

  Future<bool> pingDevice(String deviceIp) async {
    try {
      // Use system ping to check device connectivity
      final result = await Process.run(
          'python3', ['lib/scripts/ping_service.py', deviceIp]);

      if (result.exitCode == 0) {
        final response = jsonDecode(result.stdout.toString().trim());
        return response is Map && response['status'] == 'online';
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Navigation state
  int _currentIndex = 0;
  int get currentIndex => _currentIndex;

  // Check if a device is selected and connected
  bool get isDeviceConnected {
    if (_selectedDevice == null) return false;
    return _sshConnections[_selectedDevice]?.isConnected ?? false;
  }

  // Methods to update state
  void selectDevice(String deviceId) {
    _selectedDevice = deviceId;
    notifyListeners();
  }

  void addTerminalOutput(String command, String output) {
    _terminalOutput.add('pi@raspberrypi:~ \$ $command');
    _terminalOutput.add(output);
    _terminalOutput.add('pi@raspberrypi:~ \$');
    notifyListeners();
  }

  void setCurrentIndex(int index) {
    _currentIndex = index;
    notifyListeners();
  }
}
