# 🍇 Jelly Pi - Development Scripts

This directory contains several scripts to help with development and running the application.

## Available Scripts

### 🚀 `./run_linux.sh`
**Primary run script** - Use this for normal development
- Builds and runs the application
- Handles dependencies automatically
- Shows build progress and app output
- **Use this most of the time**

### 🔄 `./rebuild_linux.sh`
**Full rebuild script** - Use when you need a complete clean rebuild
- Performs `flutter clean` to remove all build artifacts
- Gets fresh dependencies with `flutter pub get`
- Rebuilds and runs the application from scratch
- **Use this when:**
  - Changes aren't showing up in the app
  - You've made significant code changes
  - You're experiencing build issues
  - You want to ensure all changes are applied

### 🔥 `./hot_reload.sh`
**Hot reload script** - Use for rapid development (when app is already running)
- Connects to a running Flutter app for hot reload
- Allows you to press 'r' for hot reload, 'R' for hot restart
- Much faster than full rebuilds for UI changes
- **Use this when:**
  - The app is already running
  - You're making small UI changes
  - You want to see changes instantly

## Usage Examples

### Normal Development Workflow
```bash
# Start the app
./run_linux.sh

# Make code changes, then in another terminal:
./hot_reload.sh
# Press 'r' to hot reload changes
```

### When Changes Don't Appear
```bash
# Stop the running app (Ctrl+C), then:
./rebuild_linux.sh
```

### Quick Reference
- **Just run the app**: `./run_linux.sh`
- **Changes not showing**: `./rebuild_linux.sh`
- **Fast development**: `./hot_reload.sh` (with app running)

## Notes
- All scripts must be run from the project root directory
- Make sure you have Flutter installed and configured
- The scripts will check for `pubspec.yaml` to ensure you're in the right directory
